# SaaS Platform Comprehensive Deployment Guide

This guide provides step-by-step instructions for deploying the Mobile App Auto-Test SaaS platform on a live server.

## Table of Contents

1. [Prerequisites](#prerequisites)
2. [Server Requirements](#server-requirements)
3. [Quick Start Deployment](#quick-start-deployment)
4. [Manual Deployment](#manual-deployment)
5. [Production Configuration](#production-configuration)
6. [SSL/TLS Setup](#ssltls-setup)
7. [Database Configuration](#database-configuration)
8. [Authentication Setup](#authentication-setup)
9. [Monitoring and Logging](#monitoring-and-logging)
10. [Backup and Recovery](#backup-and-recovery)
11. [Troubleshooting](#troubleshooting)
12. [Maintenance](#maintenance)

## Prerequisites

### System Requirements
- **Operating System**: Ubuntu 20.04+ / CentOS 8+ / RHEL 8+ / Debian 11+
- **RAM**: Minimum 4GB, Recommended 8GB+ (32GB for high-load production)
- **Storage**: Minimum 50GB SSD
- **CPU**: 2+ cores (4+ cores recommended for production)
- **Network**: Public IP address with ports 80, 443, and 8080 accessible

### Software Dependencies
- Docker 20.10+
- Docker Compose 2.0+
- Git
- OpenSSL
- Curl

### Installation Commands

#### Ubuntu/Debian
```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install additional tools
sudo apt install -y git openssl curl

# Logout and login again to apply Docker group changes
```

#### CentOS/RHEL
```bash
# Update system
sudo yum update -y

# Install Docker
sudo yum install -y yum-utils
sudo yum-config-manager --add-repo https://download.docker.com/linux/centos/docker-ce.repo
sudo yum install -y docker-ce docker-ce-cli containerd.io
sudo systemctl start docker
sudo systemctl enable docker
sudo usermod -aG docker $USER

# Install Docker Compose
sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
sudo chmod +x /usr/local/bin/docker-compose

# Install additional tools
sudo yum install -y git openssl curl
```

## Server Requirements

### VPS Provider Recommendations

| Provider | Instance Type | RAM | CPU | Storage | Monthly Cost |
|----------|---------------|-----|-----|---------|-------------|
| DigitalOcean | Memory-Optimized | 32GB | 4 vCPUs | 200GB SSD | $120 |
| Linode | Dedicated CPU | 32GB | 8 vCPUs | 640GB SSD | $120 |
| Vultr | High Memory | 32GB | 8 vCPUs | 400GB SSD | $120 |
| AWS EC2 | r5.xlarge | 32GB | 4 vCPUs | EBS | $150+ |
| Google Cloud | n2-highmem-4 | 32GB | 4 vCPUs | Persistent Disk | $140+ |

### Network Configuration

#### Firewall Rules
```bash
# Ubuntu/Debian (UFW)
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw allow 8080/tcp  # For direct API access (optional)
sudo ufw enable

# CentOS/RHEL (firewalld)
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --permanent --add-port=8080/tcp
sudo firewall-cmd --reload
```

## Quick Start Deployment

### 1. Clone the Repository
```bash
# Clone the project
git clone <your-repository-url>
cd MobileApp-AutoTest

# Make deployment script executable
chmod +x deploy.sh
```

### 2. Run Automated Deployment
```bash
# Development deployment
./deploy.sh development

# Production deployment
./deploy.sh production
```

### 3. Access the Platform
- **Development**: http://your-server-ip:8080
- **Production**: https://your-domain.com

### 4. Login with Admin Credentials
Check the `.env` file for admin credentials:
```bash
grep ADMIN_ .env
```

## Manual Deployment

### 1. Environment Setup
```bash
# Create environment file
cp .env.template .env

# Generate secure passwords
POSTGRES_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
REDIS_PASSWORD=$(openssl rand -base64 32 | tr -d "=+/" | cut -c1-25)
SECRET_KEY=$(openssl rand -base64 64 | tr -d "=+/" | cut -c1-50)
JWT_SECRET_KEY=$(openssl rand -base64 64 | tr -d "=+/" | cut -c1-50)

# Update .env file
sed -i "s/secure_password_123/$POSTGRES_PASSWORD/g" .env
sed -i "s/redis_password_123/$REDIS_PASSWORD/g" .env
sed -i "s/your-super-secret-key-change-in-production/$SECRET_KEY/g" .env
sed -i "s/jwt-super-secret-key-change-in-production/$JWT_SECRET_KEY/g" .env
```

### 2. Create Required Directories
```bash
mkdir -p ssl logs uploads
chmod 755 ssl logs uploads
```

### 3. Build and Start Services
```bash
# Build the application
docker-compose build

# Start database and Redis
docker-compose up -d postgres redis

# Wait for database to be ready
while ! docker-compose exec -T postgres pg_isready -U saas_user -d saas_platform; do
    echo "Waiting for database..."
    sleep 2
done

# Start application server
docker-compose up -d saas_server

# For production, start Nginx
docker-compose --profile production up -d nginx
```

### 4. Initialize Database and Create Admin User
```bash
# Initialize database
docker-compose exec saas_server python -c "
import os
os.environ.setdefault('FLASK_APP', 'saas_unified_server.py')
from saas_unified_server import app, db
with app.app_context():
    db.create_all()
    print('Database initialized')
"

# Create admin user
docker-compose exec saas_server python -c "
import os
os.environ.setdefault('FLASK_APP', 'saas_unified_server.py')
from saas_unified_server import app, db, User
import uuid
from datetime import datetime

with app.app_context():
    admin_user = User(
        id=str(uuid.uuid4()),
        email='<EMAIL>',
        first_name='Admin',
        last_name='User',
        tenant_id=str(uuid.uuid4()),
        is_admin=True,
        is_active=True,
        created_at=datetime.utcnow()
    )
    admin_user.set_password('admin123')
    db.session.add(admin_user)
    db.session.commit()
    print('Admin user created')
"
```

## Production Configuration

### 1. Domain and DNS Setup
```bash
# Point your domain to your server IP
# Create A record: your-domain.com -> your-server-ip
# Create CNAME record: www.your-domain.com -> your-domain.com
```

### 2. Environment Variables for Production
Update `.env` file:
```bash
# Server configuration
FLASK_ENV=production
SERVER_HOST=0.0.0.0
SERVER_PORT=8080

# Security (use strong, unique values)
SECRET_KEY=your-production-secret-key
JWT_SECRET_KEY=your-production-jwt-secret

# Database (use strong password)
POSTGRES_PASSWORD=your-strong-database-password

# Redis (use strong password)
REDIS_PASSWORD=your-strong-redis-password

# Admin user
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=your-strong-admin-password
```

### 3. Resource Limits
Create `docker-compose.override.yml` for production:
```yaml
version: '3.8'

services:
  postgres:
    deploy:
      resources:
        limits:
          memory: 2G
          cpus: '1.0'
        reservations:
          memory: 1G
          cpus: '0.5'
    
  redis:
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.5'
        reservations:
          memory: 256M
          cpus: '0.25'
    
  saas_server:
    deploy:
      resources:
        limits:
          memory: 4G
          cpus: '2.0'
        reservations:
          memory: 2G
          cpus: '1.0'
```

## SSL/TLS Setup

### Option 1: Let's Encrypt (Recommended)
```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Stop Nginx temporarily
docker-compose stop nginx

# Generate certificate
sudo certbot certonly --standalone -d your-domain.com -d www.your-domain.com

# Copy certificates to project directory
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem ssl/key.pem
sudo chown $USER:$USER ssl/*.pem

# Start Nginx
docker-compose --profile production up -d nginx

# Setup auto-renewal
echo "0 12 * * * /usr/bin/certbot renew --quiet" | sudo crontab -
```

### Option 2: Self-Signed Certificate (Development)
```bash
# Generate self-signed certificate
openssl req -x509 -newkey rsa:4096 -keyout ssl/key.pem -out ssl/cert.pem \
    -days 365 -nodes -subj "/C=US/ST=State/L=City/O=Organization/CN=your-domain.com"
```

### Option 3: Commercial Certificate
```bash
# If you have commercial certificates, copy them to:
# ssl/cert.pem (certificate + intermediate certificates)
# ssl/key.pem (private key)
```

## Database Configuration

### PostgreSQL Optimization
Create `postgresql.conf` customizations:
```bash
# Create custom PostgreSQL configuration
cat > postgres-custom.conf << EOF
# Memory settings
shared_buffers = 256MB
effective_cache_size = 1GB
work_mem = 4MB
maintenance_work_mem = 64MB

# Connection settings
max_connections = 100

# Logging
log_statement = 'all'
log_duration = on
log_min_duration_statement = 1000

# Performance
random_page_cost = 1.1
effective_io_concurrency = 200
EOF

# Mount this in docker-compose.yml
# volumes:
#   - ./postgres-custom.conf:/etc/postgresql/postgresql.conf
```

### Database Backup Setup
```bash
# Create backup script
cat > backup-db.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/app/backups"
DATE=$(date +"%Y%m%d_%H%M%S")
BACKUP_FILE="saas_platform_backup_$DATE.sql"

mkdir -p $BACKUP_DIR

docker-compose exec -T postgres pg_dump -U saas_user saas_platform > "$BACKUP_DIR/$BACKUP_FILE"
gzip "$BACKUP_DIR/$BACKUP_FILE"

# Keep only last 7 days of backups
find $BACKUP_DIR -name "*.sql.gz" -mtime +7 -delete

echo "Backup completed: $BACKUP_FILE.gz"
EOF

chmod +x backup-db.sh

# Setup daily backup cron job
echo "0 2 * * * /path/to/your/project/backup-db.sh" | crontab -
```

## Authentication Setup

### JWT Configuration
The platform uses JWT for authentication. Key settings in `.env`:
```bash
# JWT settings
JWT_SECRET_KEY=your-super-secret-jwt-key
JWT_ACCESS_TOKEN_EXPIRES=3600  # 1 hour
JWT_REFRESH_TOKEN_EXPIRES=2592000  # 30 days
```

### User Management
```bash
# Create additional admin users
docker-compose exec saas_server python -c "
from saas_unified_server import app, db, User
import uuid
from datetime import datetime

with app.app_context():
    user = User(
        id=str(uuid.uuid4()),
        email='<EMAIL>',
        first_name='New',
        last_name='Admin',
        tenant_id=str(uuid.uuid4()),
        is_admin=True,
        is_active=True,
        created_at=datetime.utcnow()
    )
    user.set_password('newpassword')
    db.session.add(user)
    db.session.commit()
    print('New admin user created')
"
```

### API Authentication
The platform provides several authentication endpoints:

- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Token refresh

Example API usage:
```bash
# Register a new user
curl -X POST http://your-domain.com/api/auth/register \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123",
    "first_name": "John",
    "last_name": "Doe"
  }'

# Login
curl -X POST http://your-domain.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{
    "email": "<EMAIL>",
    "password": "password123"
  }'
```

## Monitoring and Logging

### Application Logs
```bash
# View real-time logs
docker-compose logs -f saas_server

# View specific service logs
docker-compose logs postgres
docker-compose logs redis
docker-compose logs nginx

# Export logs
docker-compose logs saas_server > app.log
```

### Health Monitoring
```bash
# Check service health
curl http://localhost:8080/health

# Check database connection
docker-compose exec postgres pg_isready -U saas_user -d saas_platform

# Check Redis connection
docker-compose exec redis redis-cli ping
```

### System Monitoring Setup
```bash
# Install monitoring tools
sudo apt install -y htop iotop nethogs

# Monitor Docker containers
docker stats

# Monitor disk usage
df -h
du -sh /var/lib/docker
```

### Log Rotation
```bash
# Setup log rotation for Docker
sudo tee /etc/logrotate.d/docker << EOF
/var/lib/docker/containers/*/*.log {
    rotate 7
    daily
    compress
    size=1M
    missingok
    delaycompress
    copytruncate
}
EOF
```

## Backup and Recovery

### Complete Backup Strategy
```bash
# Create comprehensive backup script
cat > full-backup.sh << 'EOF'
#!/bin/bash
BACKUP_DIR="/app/backups"
DATE=$(date +"%Y%m%d_%H%M%S")

mkdir -p $BACKUP_DIR

# Database backup
docker-compose exec -T postgres pg_dump -U saas_user saas_platform > "$BACKUP_DIR/db_$DATE.sql"

# Redis backup
docker-compose exec -T redis redis-cli --rdb - > "$BACKUP_DIR/redis_$DATE.rdb"

# Application files backup
tar -czf "$BACKUP_DIR/app_files_$DATE.tar.gz" uploads logs ssl .env

# Compress database backup
gzip "$BACKUP_DIR/db_$DATE.sql"

echo "Full backup completed: $DATE"
EOF

chmod +x full-backup.sh
```

### Recovery Procedures
```bash
# Database recovery
docker-compose exec -T postgres psql -U saas_user -d saas_platform < backup_file.sql

# Redis recovery
docker-compose stop redis
cp redis_backup.rdb /var/lib/docker/volumes/redis_data/_data/dump.rdb
docker-compose start redis

# Application files recovery
tar -xzf app_files_backup.tar.gz
```

## Troubleshooting

### Common Issues

#### 1. Database Connection Issues
```bash
# Check database status
docker-compose ps postgres

# Check database logs
docker-compose logs postgres

# Test connection
docker-compose exec postgres psql -U saas_user -d saas_platform -c "SELECT 1;"
```

#### 2. Application Won't Start
```bash
# Check application logs
docker-compose logs saas_server

# Check environment variables
docker-compose exec saas_server env | grep -E "(DATABASE|REDIS|SECRET)"

# Restart application
docker-compose restart saas_server
```

#### 3. SSL Certificate Issues
```bash
# Check certificate validity
openssl x509 -in ssl/cert.pem -text -noout

# Test SSL connection
openssl s_client -connect your-domain.com:443

# Check Nginx configuration
docker-compose exec nginx nginx -t
```

#### 4. Performance Issues
```bash
# Check resource usage
docker stats

# Check database performance
docker-compose exec postgres psql -U saas_user -d saas_platform -c "
  SELECT query, calls, total_time, mean_time 
  FROM pg_stat_statements 
  ORDER BY total_time DESC 
  LIMIT 10;
"

# Check Redis memory usage
docker-compose exec redis redis-cli info memory
```

### Debug Mode
```bash
# Enable debug mode
echo "FLASK_ENV=development" >> .env
echo "DEBUG=true" >> .env
docker-compose restart saas_server

# Disable debug mode for production
sed -i '/FLASK_ENV=development/d' .env
sed -i '/DEBUG=true/d' .env
docker-compose restart saas_server
```

## Maintenance

### Regular Maintenance Tasks

#### Daily
- Monitor application logs
- Check system resources
- Verify backup completion

#### Weekly
- Update Docker images
- Clean up old logs
- Review security logs

#### Monthly
- Update system packages
- Review and rotate secrets
- Performance optimization

### Update Procedures
```bash
# Update application
git pull origin main
docker-compose build --no-cache
docker-compose up -d

# Update Docker images
docker-compose pull
docker-compose up -d

# Clean up old images
docker image prune -f
docker volume prune -f
```

### Security Maintenance
```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Update Docker
curl -fsSL https://get.docker.com -o get-docker.sh
sudo sh get-docker.sh

# Review access logs
docker-compose logs nginx | grep -E "(40[0-9]|50[0-9])"

# Check for failed login attempts
docker-compose logs saas_server | grep "login failed"
```

## Performance Optimization

### Database Optimization
```sql
-- Run these queries periodically
ANALYZE;
VACUUM;

-- Check slow queries
SELECT query, calls, total_time, mean_time 
FROM pg_stat_statements 
ORDER BY total_time DESC 
LIMIT 10;
```

### Redis Optimization
```bash
# Monitor Redis performance
docker-compose exec redis redis-cli info stats

# Check memory usage
docker-compose exec redis redis-cli info memory

# Optimize Redis configuration
echo "maxmemory 512mb" >> redis.conf
echo "maxmemory-policy allkeys-lru" >> redis.conf
```

### Application Optimization
```bash
# Enable production optimizations in .env
echo "FLASK_ENV=production" >> .env
echo "PYTHONOPTIMIZE=1" >> .env
docker-compose restart saas_server
```

## Support and Documentation

### Getting Help
- Check application logs: `docker-compose logs saas_server`
- Review this deployment guide
- Check the project's issue tracker
- Contact system administrator

### Additional Resources
- [Docker Documentation](https://docs.docker.com/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)
- [Redis Documentation](https://redis.io/documentation)
- [Nginx Documentation](https://nginx.org/en/docs/)

---

**Note**: This deployment guide covers a comprehensive production setup. For development or testing purposes, you can use the simplified quick start deployment method. Always ensure you have proper backups before making any changes to a production system.