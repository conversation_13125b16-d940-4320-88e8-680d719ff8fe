/* Main Styles */
html, body {
    margin: 0;
    padding: 0;
    background-color: #f8f9fa;
    overflow-x: hidden;
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
    min-width: 1200px; /* Ensure minimum width to prevent layout issues */
}

/* Main container with proper margins and borders */
.container-fluid {
    margin: 20px auto !important;
    padding: 0 60px !important; /* Increased from 40px to 60px for better centering */
    max-width: 1600px; /* Increased from 1400px to 1600px */
    min-width: 1200px; /* Ensure minimum width for proper layout */
    background-color: #ffffff;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    border: 1px solid #e9ecef;
}

/* Add top padding to the main content area */
.container-fluid > .row {
    padding-top: 20px;
    padding-bottom: 20px;
}

.card {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    margin-bottom: 25px;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    transition: box-shadow 0.3s ease;
}

.card:hover {
    box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid #e9ecef;
    font-weight: 600;
    color: #495057;
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
}

/* Navigation tabs styling */
.nav-tabs {
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
}

.nav-tabs .nav-link {
    border: none;
    border-radius: 8px 8px 0 0;
    color: #6c757d;
    font-weight: 500;
    padding: 12px 20px;
    margin-right: 5px;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    background-color: #f8f9fa;
    color: #495057;
}

.nav-tabs .nav-link.active {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

/* Tab content styling */
.tab-content {
    background-color: #ffffff;
    border-radius: 0 8px 8px 8px;
    padding: 20px;
    /* Remove min-height restriction to allow natural content flow */
}

/* Button styling improvements */
.btn {
    border-radius: 6px;
    font-weight: 500;
    padding: 8px 16px;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
    box-shadow: 0 2px 4px rgba(0, 123, 255, 0.3);
}

.btn-primary:hover {
    background-color: #0056b3;
    border-color: #0056b3;
    box-shadow: 0 4px 8px rgba(0, 123, 255, 0.4);
    transform: translateY(-1px);
}

.btn-success {
    background-color: #28a745;
    border-color: #28a745;
    box-shadow: 0 2px 4px rgba(40, 167, 69, 0.3);
}

.btn-success:hover {
    background-color: #1e7e34;
    border-color: #1e7e34;
    box-shadow: 0 4px 8px rgba(40, 167, 69, 0.4);
    transform: translateY(-1px);
}

.btn-danger {
    background-color: #dc3545;
    border-color: #dc3545;
    box-shadow: 0 2px 4px rgba(220, 53, 69, 0.3);
}

.btn-danger:hover {
    background-color: #c82333;
    border-color: #c82333;
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.4);
    transform: translateY(-1px);
}

/* Form elements styling */
.form-control {
    border-radius: 6px;
    border: 1px solid #ced4da;
    padding: 10px 12px;
    transition: all 0.3s ease;
}

.form-control:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
}

.form-group {
    margin-bottom: 20px;
}

.form-label {
    font-weight: 500;
    color: #495057;
    margin-bottom: 8px;
}

/* App header styling */
.app-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 0;
    margin: -20px -30px 30px -30px;
    border-radius: 8px 8px 0 0;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.app-header h1 {
    margin: 0;
    font-size: 2rem;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.app-header p {
    margin: 5px 0 0 0;
    opacity: 0.9;
    font-size: 1.1rem;
}

/* Adjust column widths for the action builder and device sections */
@media (min-width: 768px) {
    #device-control-tab .col-md-5 {
        flex: 0 0 auto;
        width: 40%; /* Increased from 35% to 40% for better action list space */
        padding-right: 20px;
    }

    #device-control-tab .col-md-7 {
        flex: 0 0 auto;
        width: 60%; /* Decreased from 65% to 60% to balance with action list */
        padding-left: 20px;
    }
}

/* Improve action list card layout */
.card-body {
    padding: 15px; /* Reduced from 20px */
}

/* Action List Card Specific Styling */
#actionsList .card {
    margin-bottom: 15px; /* Reduced margin */
}

#actionsList .card-header {
    padding: 10px 15px; /* Reduced padding */
    font-size: 0.95rem;
}

/* Ensure action list card body allows natural expansion */
.card-body.p-0 {
    /* Remove any height restrictions */
    height: auto;
}

/* Responsive improvements for action list */
@media (max-width: 1200px) {
    .container-fluid {
        max-width: 100%;
        margin: 10px !important;
        padding: 0 20px !important;
    }
}

@media (max-width: 992px) {
    #device-control-tab .col-md-5,
    #device-control-tab .col-md-7 {
        width: 100%;
        padding: 0 15px;
    }

    #actionsList {
        margin-top: 20px;
    }
}

/* Add spacing between sections */
.row {
    margin-bottom: 20px;
}

.col-md-6, .col-md-4, .col-md-8, .col-md-12 {
    padding-left: 15px;
    padding-right: 15px;
}

/* Responsive improvements */
@media (max-width: 768px) {
    .container-fluid {
        margin: 10px auto !important;
        padding: 0 20px !important; /* Increased from 15px to 20px for better mobile centering */
        border-radius: 4px;
        min-width: auto; /* Remove min-width constraint on mobile */
    }

    .app-header {
        margin: -10px -15px 20px -15px;
        padding: 15px 0;
        border-radius: 4px 4px 0 0;
    }

    .app-header h1 {
        font-size: 1.5rem;
    }

    .app-header p {
        font-size: 1rem;
    }

    .card-body {
        padding: 15px;
    }

    .nav-tabs .nav-link {
        padding: 8px 12px;
        font-size: 0.9rem;
    }

    .btn {
        padding: 6px 12px;
        font-size: 0.9rem;
    }
}

/* Better visual hierarchy */
.card-header {
    border-bottom: 2px solid #e9ecef;
}

.card-header h5, .card-header h6 {
    color: #495057;
    font-weight: 600;
}

/* Improved focus states */
.form-control:focus,
.form-select:focus {
    border-color: #007bff;
    box-shadow: 0 0 0 0.25rem rgba(0, 123, 255, 0.25);
    outline: none;
}

/* Better spacing for action lists */
.list-group-item {
    border-radius: 6px !important;
    margin-bottom: 4px;
    border: 1px solid #e9ecef !important;
    transition: all 0.2s ease;
    padding: 8px 12px; /* Reduced padding for more compact layout */
}

.list-group-item:hover {
    background-color: #f8f9fa;
    border-color: #007bff !important;
    transform: translateX(2px);
}

/* Action List Container Improvements */
#actionsList {
    /* Remove height restrictions to allow natural expansion */
    padding-right: 8px;
}

/* Test Case Container Improvements */
.test-case-container {
    margin-bottom: 12px !important;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    overflow: hidden;
    background-color: #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

/* Test Case Header Improvements */
.test-case-header {
    background-color: #f8f9fa !important;
    border-bottom: 1px solid #dee2e6 !important;
    padding: 10px 15px !important; /* Reduced padding */
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex !important;
    justify-content: space-between;
    align-items: center;
}

.test-case-header:hover {
    background-color: #e9ecef !important;
}

.test-case-header h6 {
    margin: 0 !important;
    font-size: 0.95rem; /* Slightly smaller font */
    font-weight: 600;
    color: #495057;
}

/* Collapse Icon Animation */
.collapse-icon {
    transition: transform 0.2s ease !important;
    color: #6c757d;
    font-size: 0.9rem;
}

/* Action Item Improvements */
.action-item {
    padding: 6px 12px !important; /* More compact padding */
    border: none !important;
    border-bottom: 1px solid #f1f1f1 !important;
    margin-bottom: 0 !important;
    background-color: #ffffff;
    transition: all 0.2s ease;
}

.action-item:last-child {
    border-bottom: none !important;
}

.action-item:hover {
    background-color: #f8f9fa !important;
    transform: none; /* Remove transform for action items */
}

/* Action Content Layout */
.action-content {
    display: flex;
    align-items: center;
    flex-grow: 1;
    font-size: 0.9rem; /* Slightly smaller font */
}

.action-content .badge {
    font-size: 0.75rem; /* Smaller badges */
}

.step-number {
    min-width: 24px;
    text-align: center;
}

/* Action Buttons Improvements */
.action-buttons {
    display: flex;
    gap: 4px;
}

.action-buttons .btn {
    padding: 4px 8px !important; /* Smaller buttons */
    font-size: 0.8rem;
}

/* Test Case Actions Container */
.test-case-actions {
    background-color: #ffffff;
    border-radius: 0;
}

/* Badge Improvements */
.test-case-header .badge {
    font-size: 0.75rem;
    padding: 4px 8px;
}

/* Button Group in Header */
.test-case-header .btn {
    padding: 4px 8px !important;
    font-size: 0.8rem;
    margin-left: 4px;
}

/* Expand/Collapse All Buttons */
#expandAllBtn, #collapseAllBtn {
    padding: 4px 8px !important;
    font-size: 0.8rem;
    border-radius: 4px;
}

/* Action List Header Improvements */
.card-header .btn-group-sm .btn {
    padding: 4px 8px;
    font-size: 0.8rem;
}

/* Improve spacing in action list header */
.card-header .d-flex:first-child {
    align-items: center;
}

.card-header .d-flex:first-child h5 {
    margin-bottom: 0;
}

/* Status indicators for test cases during execution */
.test-case-container.running {
    border-left: 4px solid #007bff;
    box-shadow: 0 0 8px rgba(0, 123, 255, 0.3);
}

.test-case-container.completed {
    border-left: 4px solid #28a745;
}

.test-case-container.failed {
    border-left: 4px solid #dc3545;
}

/* Improve action list layout on smaller screens */
@media (max-width: 768px) {
    .test-case-header {
        padding: 8px 12px !important;
    }

    .test-case-header h6 {
        font-size: 0.9rem;
    }

    .action-item {
        padding: 4px 8px !important;
    }

    .action-item .action-content {
        font-size: 0.85rem;
    }

    .action-item .action-buttons .btn {
        padding: 2px 4px !important;
        font-size: 0.7rem;
    }
}

/* Device Screen */
#deviceScreenContainer {
    position: relative;
    max-width: 100%;
    height: auto;
    text-align: center;
}

.device-screen {
    max-width: 100%;
    max-height: 70vh;
    border: 1px solid #ddd;
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* For larger screens, use a bigger width */
@media (min-width: 1200px) {
    .device-screen {
        max-width: 480px;
    }
}

/* Make the screen container responsive */
@media (max-width: 400px) {
    #screenContainer {
        width: 90%; /* Full width on small screens */
    }
}

/* Improve screen overlay for better visualization */
#overlayCanvas {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 10;
}

/* Recording Indicator */
.recording-indicator {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 15px;
    height: 15px;
    background-color: #f00;
    border-radius: 50%;
    animation: pulse 1s infinite;
    z-index: 20;
}

@keyframes pulse {
    0% {
        transform: scale(0.8);
        opacity: 1;
    }
    50% {
        transform: scale(1);
        opacity: 0.8;
    }
    100% {
        transform: scale(0.8);
        opacity: 1;
    }
}

/* Code Output */
#codeOutput {
    max-height: 300px;
    overflow-y: auto;
    font-size: 0.9rem;
    font-family: 'Courier New', monospace;
}

/* Action Log */
.log-entry {
    border-bottom: 1px solid #eee;
    padding: 4px 8px;
    font-size: 0.9rem;
}

.log-info {
    background-color: #f8f9fa;
}

.log-warning {
    background-color: #fff3cd;
}

.log-error {
    background-color: #f8d7da;
}

.log-code {
    background-color: #f0f8ff;
    color: #333;
}

.log-timestamp {
    color: #666;
    font-size: 0.8rem;
}

.log-action {
    font-weight: bold;
    margin-right: 5px;
}

/* Action Status Log Styles */
.log-entry.log-action-status {
    border-left: 4px solid #6c757d;
    background-color: #f8f9fa;
    font-family: 'Courier New', monospace;
    font-weight: 500;
    padding: 8px 12px;
    margin-bottom: 5px;
    border-radius: 4px;
}

.log-entry.log-action-status.log-pass {
    border-left-color: #28a745;
    background-color: #d4edda;
}

.log-entry.log-action-status.log-fail {
    border-left-color: #dc3545;
    background-color: #f8d7da;
}

.log-entry.log-action-status.log-running {
    border-left-color: #007bff;
    background-color: #d1ecf1;
}

.log-time {
    color: #6c757d;
    font-size: 0.75rem;
    margin-right: 8px;
}

.log-icon {
    margin-right: 8px;
}

.log-message {
    color: #495057;
}

/* Code display in log */
.log-code {
    margin: 0;
    padding: 8px;
    background-color: #f8f9fa;
    border-left: 3px solid #007bff;
    font-family: monospace;
    font-size: 0.85rem;
    overflow-x: auto;
    white-space: pre;
    margin-bottom: 4px;
}

/* Enhance tap animation */
.tap-marker {
    position: absolute;
    width: 40px;
    height: 40px;
    background-color: rgba(255, 0, 0, 0.5);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    pointer-events: none;
    animation: tap-animation 1s forwards;
    z-index: 20;
    box-shadow: 0 0 10px rgba(255, 0, 0, 0.7);
}

@keyframes tap-animation {
    0% {
        opacity: 0.8;
        transform: translate(-50%, -50%) scale(0.5);
    }
    50% {
        opacity: 0.6;
        transform: translate(-50%, -50%) scale(1);
    }
    100% {
        opacity: 0;
        transform: translate(-50%, -50%) scale(1.5);
    }
}

/* Swipe Path Animation */
.swipe-path {
    position: absolute;
    height: 4px;
    background-color: rgba(0, 128, 255, 0.7);
    pointer-events: none;
    z-index: 10;
}

.swipe-path:after {
    content: '';
    position: absolute;
    top: -3px;
    right: -3px;
    width: 10px;
    height: 10px;
    background-color: rgba(0, 128, 255, 0.8);
    border-radius: 50%;
}

.swipe-preview {
    opacity: 0.5;
    background-color: rgba(0, 200, 255, 0.5);
}

/* Change Highlight */
.change-highlight {
    position: absolute;
    border: 2px solid rgba(255, 200, 0, 0.7);
    background-color: rgba(255, 255, 0, 0.1);
    pointer-events: none;
    animation: highlight-fade 3s forwards;
    z-index: 5;
}

@keyframes highlight-fade {
    0% {
        opacity: 0.8;
    }
    80% {
        opacity: 0.8;
    }
    100% {
        opacity: 0;
    }
}

/* Loading Overlay */
#loadingOverlay {
    background-color: rgba(255, 255, 255, 0.7);
    z-index: 1000;
}

/* Responsive adjustments */
@media (max-width: 767.98px) {
    .card-body {
        padding: 1rem;
    }

    #deviceScreen {
        max-height: 50vh;
    }

    .btn {
        padding: 0.25rem 0.5rem;
        font-size: 0.875rem;
    }
}

/* Element highlighting */
.element-highlight {
    position: absolute;
    border: 2px dashed rgba(0, 128, 0, 0.7);
    background-color: rgba(0, 255, 0, 0.1);
    pointer-events: none;
    z-index: 15;
}

.text-element {
    border-color: rgba(0, 128, 255, 0.7);
    background-color: rgba(0, 128, 255, 0.1);
}

.button-element {
    border-color: rgba(128, 0, 128, 0.7);
    background-color: rgba(128, 0, 128, 0.1);
}

.input-element {
    border-color: rgba(255, 128, 0, 0.7);
    background-color: rgba(255, 128, 0, 0.1);
}

/* Element tooltip */
.element-tooltip {
    position: absolute;
    background-color: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 0.8rem;
    z-index: 25;
    transform: translate(5px, -100%);
    max-width: 200px;
    word-break: break-word;
}

/* Elements panel */
#elementsPanel {
    max-height: 300px;
    overflow-y: auto;
}

.element-item {
    padding: 5px;
    margin-bottom: 5px;
    background-color: #f8f9fa;
    border-radius: 3px;
    cursor: pointer;
    transition: background-color 0.2s;
}

.element-item:hover {
    background-color: #e9ecef;
}

.element-text {
    font-weight: bold;
}

.element-type {
    color: #6c757d;
    font-size: 0.8rem;
}

/* Add error indicator for action errors */
.error-indicator {
    position: absolute;
    top: 20px;
    right: 20px;
    width: 40px;
    height: 40px;
    background-color: #f53939;
    color: white;
    border-radius: 50%;
    display: flex;
    justify-content: center;
    align-items: center;
    font-size: 20px;
    font-weight: bold;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.5);
    z-index: 1000;
    animation: pulse 1.5s infinite;
}

/* Highlight for rerun target step */
.rerun-target {
    position: relative;
    border-left: 4px solid #ff9800 !important;
    background-color: rgba(255, 152, 0, 0.1) !important;
    box-shadow: 0 0 8px rgba(255, 152, 0, 0.5);
}

.rerun-target::before {
    content: "⟳";
    position: absolute;
    left: -12px;
    top: 50%;
    transform: translateY(-50%);
    color: #ff9800;
    font-size: 16px;
    font-weight: bold;
    animation: spin 2s infinite linear;
}

.rerun-target::after {
    content: "FAILED STEP";
    position: absolute;
    right: 10px;
    top: 50%;
    transform: translateY(-50%);
    background-color: #ff9800;
    color: white;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 10px;
    font-weight: bold;
}

@keyframes spin {
    from { transform: translateY(-50%) rotate(0deg); }
    to { transform: translateY(-50%) rotate(360deg); }
}



@keyframes pulse {
    0% {
        transform: scale(1);
        opacity: 1;
    }
    50% {
        transform: scale(1.2);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

/* Manual Action Builder Styles */
.action-form {
    border-top: 1px solid #e9ecef;
    padding-top: 15px;
    margin-top: 10px;
}

/* Fallback Locators Styling */
.fallback-locators-container {
    margin-bottom: 15px;
}

.fallback-locator {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 4px;
    padding: 10px;
    margin-bottom: 10px;
    position: relative;
    transition: all 0.2s ease;
}

.fallback-locator:hover {
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.fallback-locator .remove-fallback {
    position: absolute;
    top: 10px;
    right: 10px;
}

/* Action Builder collapse/expand styling */
.collapse-toggle {
    padding: 0 0.25rem;
    margin-left: 8px;
    color: rgba(0, 0, 0, 0.5);
}

.collapse-toggle:hover {
    color: rgba(0, 0, 0, 0.8);
}

.collapse-icon {
    transition: transform 0.3s ease;
}

.collapse-toggle[aria-expanded="false"] .collapse-icon {
    transform: rotate(180deg);
}

.action-builder-title {
    display: flex;
    align-items: center;
}

#actionBuilderCollapse {
    transition: height 0.3s ease;
}

#actionsList .list-group-item {
    transition: background-color 0.2s;
}

#actionsList .list-group-item:hover {
    background-color: #f8f9fa;
}

.badge-pill {
    margin-right: 8px;
}

.delete-action {
    opacity: 0.6;
    transition: opacity 0.2s;
}

.delete-action:hover {
    opacity: 1;
}

.screen-container {
    position: relative;
    min-height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.loading-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: none;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 10;
}

#loadingMessage {
    margin-top: 15px;
    font-weight: 500;
    color: #333;
}

/* Fix for Bootstrap tab transitions */
.tab-content > .tab-pane.fade {
    transition: opacity 0.15s linear;
    position: static !important;
    margin-top: 0 !important;
    display: none;
}

.tab-content > .tab-pane.fade.show {
    opacity: 1;
    display: block;
}

/* Fix for tab content positioning in Test Suites, Reports, and Settings tabs */
#test-suites-tab, #reports-tab, #settings-tab {
    position: static !important;
    margin-top: 0 !important;
}

/* Random Data Generator Styles */
.input-group .dropdown-toggle {
    border-top-right-radius: 0.25rem !important;
    border-bottom-right-radius: 0.25rem !important;
}

.input-group .dropdown-menu {
    max-height: 300px;
    overflow-y: auto;
}

.input-group .dropdown-item {
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
}

.input-group .dropdown-item:hover {
    background-color: #f8f9fa;
}

.input-group .dropdown-header {
    font-size: 0.75rem;
    font-weight: bold;
    color: #6c757d;
    padding: 0.5rem 1rem;
    margin-bottom: 0;
}

.action-log {
    height: 250px;
    overflow-y: auto;
    padding: 0;
    font-size: 0.9rem;
}

/* Additional styles for action editing and visual feedback */

/* Action buttons */
.action-buttons {
    display: flex;
    align-items: center;
}

.action-buttons .btn {
    margin-right: 0.25rem;
}

/* Visual rolling logs overlay */
.rolling-log-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background-color: rgba(0, 0, 0, 0.7);
    color: white;
    max-height: 150px;
    overflow-y: auto;
    padding: 8px 12px;
    font-family: monospace;
    font-size: 12px;
    z-index: 100;
    backdrop-filter: blur(2px);
    border-top: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.rolling-log-overlay.hidden {
    transform: translateY(100%);
}

.log-message {
    margin-bottom: 4px;
    padding: 2px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    word-wrap: break-word;
}

.log-message.info {
    color: #8cc;
}

.log-message.success {
    color: #8c8;
}

.log-message.warning {
    color: #fc8;
}

.log-message.error {
    color: #f88;
}

/* Image preview overlay */
.image-preview-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
}

.image-preview-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.7);
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-preview-content {
    background-color: white;
    padding: 20px;
    border-radius: 8px;
    max-width: 80%;
    max-height: 80%;
    overflow: auto;
    text-align: center;
}

.image-preview-content img {
    max-width: 100%;
    max-height: 60vh;
    margin: 10px 0;
    border: 1px solid #ddd;
}

/* Status indicators during playback */
.status-indicator {
    display: inline-block;
    width: 10px;
    height: 10px;
    border-radius: 50%;
    margin-right: 6px;
}

.status-indicator.pending {
    background-color: #ffcc00;
    box-shadow: 0 0 5px #ffcc00;
}

.status-indicator.success {
    background-color: #44cc44;
    box-shadow: 0 0 5px #44cc44;
}

.status-indicator.error {
    background-color: #ff5555;
    box-shadow: 0 0 5px #ff5555;
}

.status-indicator.running {
    background-color: #44aaff;
    box-shadow: 0 0 5px #44aaff;
    animation: pulsate 1s infinite;
}

@keyframes pulsate {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* Highlight current action in the list during playback */
.list-group-item.current-action {
    background-color: rgba(0, 123, 255, 0.1);
    border-left: 3px solid #007bff;
}

/* Insert action separator */
.insert-separator {
    position: relative;
    height: 3px;
    background-color: #007bff;
    margin: 0;
    animation: pulse 2s infinite;
    border-radius: 3px;
    z-index: 10;
}

.insert-separator::before {
    content: "Insert Here";
    position: absolute;
    top: -20px;
    left: 10px;
    background-color: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 10px;
    z-index: 11;
}

@keyframes pulse {
    0% {
        opacity: 0.6;
        box-shadow: 0 0 2px #007bff;
    }
    50% {
        opacity: 1;
        box-shadow: 0 0 8px #007bff;
    }
    100% {
        opacity: 0.6;
        box-shadow: 0 0 2px #007bff;
    }
}

/* Dropdown styling for insert options */
.action-buttons .dropdown-toggle::after {
    display: none; /* Hide default caret */
}

.action-buttons .dropdown-menu {
    min-width: 8rem;
    padding: 0.25rem 0;
    font-size: 0.875rem;
}

.action-buttons .dropdown-item {
    padding: 0.25rem 0.75rem;
}

.action-buttons .dropdown-item:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

/* Assertion region preview */
.assert-region-preview {
    position: absolute;
    border: 2px dashed rgba(255, 165, 0, 0.9);
    background-color: rgba(255, 165, 0, 0.2);
    pointer-events: none;
    z-index: 25;
    box-shadow: 0 0 10px rgba(255, 165, 0, 0.5);
}

/* Assertion status indicators */
.assertion-success {
    position: absolute;
    background-color: rgba(0, 255, 0, 0.5);
    border: 2px solid rgba(0, 128, 0, 0.8);
    animation: flash-success 1.5s;
    z-index: 20;
}

.assertion-failure {
    position: absolute;
    background-color: rgba(255, 0, 0, 0.5);
    border: 2px solid rgba(128, 0, 0, 0.8);
    animation: flash-failure 1.5s;
    z-index: 20;
}

@keyframes flash-success {
    0% { opacity: 0; }
    25% { opacity: 0.8; }
    75% { opacity: 0.8; }
    100% { opacity: 0; }
}

@keyframes flash-failure {
    0% { opacity: 0; }
    25% { opacity: 0.8; }
    50% { opacity: 0.4; }
    75% { opacity: 0.8; }
    100% { opacity: 0; }
}

/* Action Items and Drag-and-Drop Styles */
.sortable-list {
    margin-top: 10px;
}

.sortable-item {
    cursor: move;
    transition: background-color 0.2s, transform 0.2s, box-shadow 0.2s;
    margin-bottom: 5px;
    border-radius: 4px;
    position: relative;
}

.sortable-item:hover {
    background-color: #f8f9fa;
}

.sortable-item.dragging {
    opacity: 0.4;
    z-index: 999;
}

.sortable-item.drag-over {
    border-top: 2px solid #007bff;
    transform: translateY(2px);
}

.drag-handle {
    cursor: grab;
    color: #6c757d;
    padding: 0 8px;
    border-right: 1px solid #dee2e6;
    margin-right: 10px;
}

.drag-handle i {
    font-size: 1.2rem;
}

.sortable-item.active-action {
    background-color: rgba(0, 123, 255, 0.1);
    border-left: 3px solid #007bff;
}

/* Add this to your existing styles to enhance the action buttons */
.action-buttons .btn {
    box-shadow: none;
    transition: all 0.2s;
}

.action-buttons .btn:hover {
    transform: scale(1.1);
}

.action-content {
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Animation for drag and drop hints */
@keyframes pulse-border {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 5px rgba(0, 123, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
    }
}

.sortable-item.drag-hint {
    animation: pulse-border 2s infinite;
}

/* Action editing styles */
.action-item.editing {
    background-color: #fff3cd !important;
    border-left: 4px solid #ffc107 !important;
    position: relative;
}

.action-item.editing::after {
    content: "Editing";
    position: absolute;
    top: 0;
    right: 5px;
    background-color: #ffc107;
    color: #212529;
    font-size: 10px;
    padding: 2px 6px;
}

/* Edit mode indicator */
.edit-mode-indicator {
    display: inline-block;
    background-color: #ffc107;
    color: #212529;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 10px;
    animation: blink 2s infinite;
    vertical-align: middle;
}

.action-builder-title .edit-mode-indicator {
    margin-left: 10px;
}

h5:not(.action-builder-title) .edit-mode-indicator {
    font-size: 9px;
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

#addAction.btn-primary[data-editing-index] {
    animation: pulse-primary 2s infinite;
}

@keyframes pulse-primary {
    0% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.4);
    }
    70% {
        box-shadow: 0 0 0 5px rgba(13, 110, 253, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
    }
}

/* General styles */
.container {
    max-width: 1200px;
}

/* Remove excessive white space scrolling */
.tab-content {
    min-height: auto;
}

.tab-pane {
    padding-bottom: 20px;
}

/* Specific fixes for action list and device screen containers */
#device-control-tab #actionsList {
    min-height: 200px;
    /* Removed max-height restriction to allow full expansion */
    overflow-y: visible;
}

#device-control-tab #deviceScreenContainer {
    overflow: hidden;
}

/* Connect device card */
.device-card {
    margin-bottom: 20px;
    border-radius: 8px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.device-status {
    font-weight: bold;
    margin-bottom: 15px;
}

/* Action buttons */
.action-btn {
    margin-right: 5px;
    margin-bottom: 5px;
}

.connect-btn {
    background-color: #28a745;
    border-color: #28a745;
}

.disconnect-btn {
    background-color: #dc3545;
    border-color: #dc3545;
}

/* Test case list styling */
.test-case-item {
    transition: all 0.3s ease;
    font-size: 1rem;
    border-radius: 8px;
    font-weight: 500;
    margin-bottom: 12px;
    padding: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    background-color: #fff;
    border: 1px solid rgba(0, 0, 0, 0.08);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.test-case-item:hover {
    transform: translateX(5px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
    background-color: #f8f9fa;
    border-color: rgba(13, 110, 253, 0.2);
}

.test-case-item .test-case-info {
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.test-case-item .test-case-actions {
    display: flex;
    gap: 8px;
}

.test-case-item h5 {
    margin: 0;
    font-size: 1.1rem;
    color: #333;
}

.test-case-item small {
    color: #6c757d;
    font-size: 0.85rem;
}

.test-case-item .badge {
    margin-left: 8px;
    font-size: 0.75rem;
}

.test-case-item .action-btn {
    padding: 6px 12px;
    border-radius: 6px;
    font-size: 0.85rem;
    display: flex;
    align-items: center;
    gap: 4px;
    transition: all 0.2s ease;
}

.test-case-item .action-btn:hover {
    transform: translateY(-1px);
}

.test-case-item.active {
    background-color: #e2f0ff;
    border-left: 3px solid #0d6efd;
}

/* Test Cases Container */
#testCasesContainer {
    padding: 16px;
    background-color: #f8f9fa;
    border-radius: 12px;
}

#testCasesList {
    margin-top: 16px;
}

/* Search and Filter Section */
.test-case-filters {
    background-color: #fff;
    padding: 16px;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
    margin-bottom: 20px;
}

.test-case-filters .input-group {
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

.test-case-filters .btn-group .btn {
    padding: 8px 16px;
    border-radius: 6px;
    font-weight: 500;
}

.test-case-filters .btn-group .btn.active {
    background-color: #0d6efd;
    color: white;
    box-shadow: 0 2px 4px rgba(13, 110, 253, 0.2);
}

/* Empty State */
#noTestCasesMessage {
    padding: 40px;
    text-align: center;
    background-color: #fff;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
}

#noTestCasesMessage i {
    font-size: 3.5rem;
    color: #6c757d;
    opacity: 0.5;
    margin-bottom: 16px;
}

#noTestCasesMessage p {
    font-size: 1.1rem;
    color: #6c757d;
    margin-bottom: 20px;
}

#noTestCasesMessage .btn {
    padding: 10px 20px;
    border-radius: 6px;
    font-weight: 500;
    display: inline-flex;
    align-items: center;
    gap: 8px;
}

/* Loading State */
#testCasesLoading {
    padding: 40px;
    text-align: center;
}

#testCasesLoading .spinner-border {
    width: 2.5rem;
    height: 2.5rem;
    color: #0d6efd;
}

#testCasesLoading p {
    margin-top: 16px;
    color: #6c757d;
    font-size: 1rem;
}

/* Screenshot area */
.screenshot-container {
    position: relative;
    overflow: hidden;
    border: 1px solid #ddd;
    background-color: #eee;
    text-align: center;
}

.screenshot-img {
    max-width: 100%;
    max-height: 500px;
}

/* Appium Inspector */
.inspector-container {
    display: flex;
    flex-direction: row;
    height: calc(100vh - 120px);
}

.elements-panel {
    flex: 0 0 300px;
    overflow-y: auto;
    border-right: 1px solid #ddd;
    padding: 10px;
}

.screen-panel {
    flex: 1;
    display: flex;
    flex-direction: column;
    padding: 10px;
}

.element-details {
    flex: 0 0 200px;
    overflow-y: auto;
    border-top: 1px solid #ddd;
    padding: 10px;
    margin-top: 10px;
}

/* Spinner for loading states */
.spinner {
    width: 40px;
    height: 40px;
    margin: 20px auto;
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-left-color: #007bff;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

#imageSelectionOverlay {
    cursor: crosshair;
    z-index: 1000;
}

.image-preview-container {
    border: 1px solid #dee2e6;
    border-radius: 0.25rem;
    padding: 10px;
    margin-bottom: 15px;
    min-height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #f8f9fa;
}

#selectedImagePreview {
    max-width: 100%;
    max-height: 150px;
}

.capture-image-btn {
    margin-bottom: 15px;
}

/* Make sure the device screen container has position relative for overlay positioning */
#deviceScreenContainer {
    position: relative;
    max-width: 100%;
    height: auto;
    text-align: center;
}

/* Action item status styles */
.action-item {
    position: relative;
    transition: all 0.3s ease;
}

.action-item.executing {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

.action-item.success {
    background-color: #d4edda;
    border-left: 4px solid #28a745;
}

.action-item.error {
    background-color: #f8d7da;
    border-left: 4px solid #dc3545;
}

.action-item.skipped {
    background-color: #e2e3e5;
    border-left: 4px solid #6c757d;
}

.action-loading {
    display: none;
}

.action-status {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
    margin-right: 10px;
    border-radius: 50%;
}

.action-status i {
    font-size: 16px;
}

.action-item.success .action-status i {
    color: #28a745;
}

.action-item.error .action-status i {
    color: #dc3545;
}

.action-item.skipped .action-status i {
    color: #6c757d;
}

/* Test case header status styles */
.test-case-header.success {
    background-color: #d4edda;
    border-left: 4px solid #28a745;
}

.test-case-header.error {
    background-color: #f8d7da;
    border-left: 4px solid #dc3545;
}

.test-case-header.skipped {
    background-color: #e2e3e5;
    border-left: 4px solid #6c757d;
}

.test-case-header.running {
    background-color: #fff3cd;
    border-left: 4px solid #ffc107;
}

/* Make sure test case headers remain visible */
.test-case-header {
    display: flex !important;
    background-color: #f8f9fa;
    cursor: pointer;
    transition: background-color 0.2s;
    border-bottom: 1px solid #dee2e6;
    padding: 0.75rem 1rem;
}

.test-case-header:hover {
    background-color: #e9ecef;
}

.test-case-header h6 {
    margin: 0;
    color: #495057;
    font-weight: 600;
}

.test-case-header .collapse-icon {
    color: #6c757d;
    transition: transform 0.2s;
}

/* Highlight current action in the list during playback */
.list-group-item.executing-highlight {
    background-color: rgba(0, 123, 255, 0.1);
    border-left: 3px solid #007bff;
}

/* Insert action separator */
.insert-separator {
    position: relative;
    height: 3px;
    background-color: #007bff;
    margin: 0;
    animation: pulse 2s infinite;
    border-radius: 3px;
    z-index: 10;
}

.insert-separator::before {
    content: "Insert Here";
    position: absolute;
    top: -20px;
    left: 10px;
    background-color: #007bff;
    color: white;
    padding: 2px 8px;
    border-radius: 3px;
    font-size: 10px;
    z-index: 11;
}

@keyframes pulse {
    0% {
        opacity: 0.6;
        box-shadow: 0 0 2px #007bff;
    }
    50% {
        opacity: 1;
        box-shadow: 0 0 8px #007bff;
    }
    100% {
        opacity: 0.6;
        box-shadow: 0 0 2px #007bff;
    }
}

/* Dropdown styling for insert options */
.action-buttons .dropdown-toggle::after {
    display: none; /* Hide default caret */
}

.action-buttons .dropdown-menu {
    min-width: 8rem;
    padding: 0.25rem 0;
    font-size: 0.875rem;
}

.action-buttons .dropdown-item {
    padding: 0.25rem 0.75rem;
}

.action-buttons .dropdown-item:hover {
    background-color: rgba(0, 123, 255, 0.1);
}

/* Assertion region preview */
.assert-region-preview {
    position: absolute;
    border: 2px dashed rgba(255, 165, 0, 0.9);
    background-color: rgba(255, 165, 0, 0.2);
    pointer-events: none;
    z-index: 25;
    box-shadow: 0 0 10px rgba(255, 165, 0, 0.5);
}

/* Assertion status indicators */
.assertion-success {
    position: absolute;
    background-color: rgba(0, 255, 0, 0.5);
    border: 2px solid rgba(0, 128, 0, 0.8);
    animation: flash-success 1.5s;
    z-index: 20;
}

.assertion-failure {
    position: absolute;
    background-color: rgba(255, 0, 0, 0.5);
    border: 2px solid rgba(128, 0, 0, 0.8);
    animation: flash-failure 1.5s;
    z-index: 20;
}

@keyframes flash-success {
    0% { opacity: 0; }
    25% { opacity: 0.8; }
    75% { opacity: 0.8; }
    100% { opacity: 0; }
}

@keyframes flash-failure {
    0% { opacity: 0; }
    25% { opacity: 0.8; }
    50% { opacity: 0.4; }
    75% { opacity: 0.8; }
    100% { opacity: 0; }
}

/* Action Items and Drag-and-Drop Styles */
.sortable-list {
    margin-top: 10px;
}

.sortable-item {
    cursor: move;
    transition: background-color 0.2s, transform 0.2s, box-shadow 0.2s;
    margin-bottom: 5px;
    border-radius: 4px;
    position: relative;
}

.sortable-item:hover {
    background-color: #f8f9fa;
}

.sortable-item.dragging {
    opacity: 0.4;
    z-index: 999;
}

.sortable-item.drag-over {
    border-top: 2px solid #007bff;
    transform: translateY(2px);
}

.drag-handle {
    cursor: grab;
    color: #6c757d;
    padding: 0 8px;
    border-right: 1px solid #dee2e6;
    margin-right: 10px;
}

.drag-handle i {
    font-size: 1.2rem;
}

.sortable-item.active-action {
    background-color: rgba(0, 123, 255, 0.1);
    border-left: 3px solid #007bff;
}

/* Add this to your existing styles to enhance the action buttons */
.action-buttons .btn {
    box-shadow: none;
    transition: all 0.2s;
}

.action-buttons .btn:hover {
    transform: scale(1.1);
}

.action-content {
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Animation for drag and drop hints */
@keyframes pulse-border {
    0% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0.4);
    }
    70% {
        box-shadow: 0 0 0 5px rgba(0, 123, 255, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(0, 123, 255, 0);
    }
}

.sortable-item.drag-hint {
    animation: pulse-border 2s infinite;
}

/* Action editing styles */
.action-item.editing {
    background-color: #fff3cd !important;
    border-left: 4px solid #ffc107 !important;
    position: relative;
}

.action-item.editing::after {
    content: "Editing";
    position: absolute;
    top: 0;
    right: 5px;
    background-color: #ffc107;
    color: #212529;
    font-size: 10px;
    padding: 2px 6px;
}

/* Edit mode indicator */
.edit-mode-indicator {
    display: inline-block;
    background-color: #ffc107;
    color: #212529;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 3px;
    margin-left: 10px;
    animation: blink 2s infinite;
    vertical-align: middle;
}

.action-builder-title .edit-mode-indicator {
    margin-left: 10px;
}

h5:not(.action-builder-title) .edit-mode-indicator {
    font-size: 9px;
}

@keyframes blink {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.6; }
}

#addAction.btn-primary[data-editing-index] {
    animation: pulse-primary 2s infinite;
}

@keyframes pulse-primary {
    0% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.4);
    }
    70% {
        box-shadow: 0 0 0 5px rgba(13, 110, 253, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
    }
}

/* Element Inspector Styles */
.locator-item {
    transition: background-color 0.2s ease;
    cursor: pointer;
    border-left: 3px solid transparent;
}

.locator-item:hover {
    background-color: rgba(13, 110, 253, 0.05);
    border-left-color: #0d6efd;
}

.locator-item.selected {
    background-color: rgba(255, 193, 7, 0.1);
    border-left-color: #ffc107;
}

.locator-value {
    display: inline-block;
    max-width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    padding: 0.25rem 0;
}

.locator-list {
    max-height: 200px;
    overflow-y: auto;
}

/* Cancel inspect button style */
.btn-warning.inspect-active {
    background-color: #ffc107;
    color: #000;
    font-weight: 500;
    border: 2px solid #e0a800;
    animation: pulse-warning 2s infinite;
}

@keyframes pulse-warning {
    0% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0.7);
    }
    70% {
        box-shadow: 0 0 0 6px rgba(255, 193, 7, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(255, 193, 7, 0);
    }
}

/* Add Action button disabled state */
.add-action-disabled {
    opacity: 0.6;
    pointer-events: none;
    cursor: not-allowed;
}

/* Overlay for inspect mode */
.inspect-mode-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: rgba(255, 193, 7, 0.1);
    z-index: 1000;
    pointer-events: none;
    border: 3px solid #ffc107;
}

/* Element inspector tabs styling */
#inspectorTabs .nav-link.active {
    font-weight: 500;
    border-bottom-color: #0d6efd;
}

/* Attributes and locators tables */
.attributes-table, .locator-list {
    border-radius: 4px;
    border: 1px solid #dee2e6;
    overflow: hidden;
}

/* Modal footer action buttons */
.modal-footer .action-button {
    min-width: 120px;
}

/* Multi Step Action Styles */
.multi-step-container {
    margin-top: 10px;
    padding-left: 15px;
    border-left: 2px solid #dee2e6;
}

/* Hide multi-step container by default */
.multi-step-container.collapsed {
    display: none;
}

/* Show multi-step container when expanded */
.multi-step-container.expanded {
    display: block;
}

/* Force display when multistep is executing */
.multistep-executing .multi-step-container {
    display: block !important;
    visibility: visible !important;
}

.multi-step-item {
    padding: 8px;
    margin-bottom: 5px;
    border-radius: 4px;
    background-color: #f8f9fa;
    transition: all 0.2s ease;
}

.multi-step-item:hover {
    background-color: #e9ecef;
}

.multi-step-content {
    flex-grow: 1;
    overflow: hidden;
    text-overflow: ellipsis;
}

.multi-step-status {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 24px;
    height: 24px;
}

.multi-step-executing {
    background-color: #e2f0ff !important;
    border-left: 3px solid #0d6efd !important;
    animation: pulse-primary 1.5s infinite;
}

.multi-step-success {
    background-color: #d4edda !important;
    border-left: 3px solid #28a745 !important;
}

.multi-step-error {
    background-color: #f8d7da !important;
    border-left: 3px solid #dc3545 !important;
}

.multi-step-item .badge.bg-danger {
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    margin-left: 0.5rem;
    animation: blink 1.5s infinite;
}

/* Multistep action executing state */
.multistep-executing {
    background-color: #e3f2fd !important;
    border: 2px solid #2196f3 !important;
    border-radius: 8px !important;
    box-shadow: 0 0 10px rgba(33, 150, 243, 0.3) !important;
    animation: pulse-multistep 2s infinite;
}

.multistep-executing .multi-step-container {
    background-color: #f8f9fa;
    border-radius: 4px;
    padding: 10px;
    margin-top: 10px;
}

@keyframes pulse-multistep {
    0% { box-shadow: 0 0 10px rgba(33, 150, 243, 0.3); }
    50% { box-shadow: 0 0 20px rgba(33, 150, 243, 0.6); }
    100% { box-shadow: 0 0 10px rgba(33, 150, 243, 0.3); }
}

@keyframes pulse-primary {
    0% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0.4);
    }
    70% {
        box-shadow: 0 0 0 5px rgba(13, 110, 253, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(13, 110, 253, 0);
    }
}

/* Hook Action Styles */
.hook-action-item {
    background-color: #f8f9fa;
    border-left: 4px solid #6c757d;
}



.action-item.action-item-recovery {
    background-color: rgba(255, 193, 7, 0.2) !important;
    border-left: 4px solid #ffc107 !important;
}

.action-item.action-item-retrying {
    background-color: rgba(13, 110, 253, 0.2) !important;
    border-left: 4px solid #0d6efd !important;
}

.hook-executing {
    background-color: rgba(255, 193, 7, 0.2) !important;
    border-left: 4px solid #ffc107 !important;
    animation: pulse-warning 1.5s infinite;
}

.rerun-target {
    border-left: 5px solid #dc3545 !important;
    animation: pulse-danger 1.5s infinite;
}

/* Rerun Failed functionality has been removed */

/* Running badge style */
.running-badge {
    animation: blink 1s infinite;
    font-weight: bold;
    margin-left: 10px !important;
    font-size: 0.9rem !important;
    padding: 0.3rem 0.6rem !important;
}

/* Make the currently executing step more visible */
.action-item.executing-highlight {
    background-color: rgba(13, 110, 253, 0.15) !important;
    border: 1px solid rgba(13, 110, 253, 0.5) !important;
    border-left: 5px solid #0d6efd !important;
    box-shadow: 0 0 10px rgba(13, 110, 253, 0.5) !important;
    position: relative;
    z-index: 100;
    transform: scale(1.01);
    transition: all 0.2s ease-in-out;
}

@keyframes blink {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

@keyframes pulse-danger {
    0% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4);
    }
    70% {
        box-shadow: 0 0 0 5px rgba(220, 53, 69, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0);
    }
}

/* Import Execution Modal Styles */
.execution-report-item {
    border: 1px solid #dee2e6;
    border-radius: 0.375rem;
    margin-bottom: 0.5rem;
    padding: 1rem;
    transition: all 0.2s ease-in-out;
}

.execution-report-item:hover {
    background-color: #f8f9fa;
    border-color: #28a745;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.execution-report-info h6 {
    color: #495057;
    font-weight: 600;
}

.execution-report-info p {
    font-size: 0.9rem;
    margin-bottom: 0.25rem;
}

.execution-report-info small {
    font-size: 0.8rem;
}

/* Action Status Styles */
.action-item.action-passed {
    background-color: rgba(40, 167, 69, 0.1);
}

.action-item.action-failed {
    background-color: rgba(220, 53, 69, 0.1);
}

.action-item.action-running {
    background-color: rgba(255, 193, 7, 0.1);
}

.action-item {
    transition: all 0.2s ease-in-out;
}

/* Status badges */
.badge.bg-success {
    background-color: #28a745 !important;
}

.badge.bg-danger {
    background-color: #dc3545 !important;
}

.badge.bg-warning {
    background-color: #ffc107 !important;
    color: #212529 !important;
}

/* Session Status Indicator Styles */
.session-status-container {
    display: flex;
    align-items: center;
}

.session-status-indicator {
    position: relative;
    display: inline-block;
}

.status-circle {
    width: 16px;
    height: 16px;
    border-radius: 50%;
    position: relative;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.status-pulse {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    border-radius: 50%;
    opacity: 0;
    transform: scale(1);
    transition: all 0.3s ease;
}

/* Status States */
.status-disconnected {
    background-color: #6c757d;
    border: 2px solid #495057;
}

.status-connecting {
    background-color: #ffc107;
    border: 2px solid #e0a800;
}

.status-connecting .status-pulse {
    background-color: #ffc107;
    animation: pulse-connecting 1.5s infinite;
}

.status-connected {
    background-color: #28a745;
    border: 2px solid #1e7e34;
}

.status-connected .status-pulse {
    background-color: #28a745;
    animation: pulse-connected 2s infinite;
}

.status-error {
    background-color: #dc3545;
    border: 2px solid #c82333;
}

.status-error .status-pulse {
    background-color: #dc3545;
    animation: pulse-error 1s infinite;
}

.status-warning {
    background-color: #fd7e14;
    border: 2px solid #e55a00;
}

.status-warning .status-pulse {
    background-color: #fd7e14;
    animation: pulse-warning 1.2s infinite;
}

/* Pulse Animations */
@keyframes pulse-connecting {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.6;
        transform: scale(1.3);
    }
    100% {
        opacity: 0;
        transform: scale(1.6);
    }
}

@keyframes pulse-connected {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    70% {
        opacity: 0.4;
        transform: scale(1.2);
    }
    100% {
        opacity: 0;
        transform: scale(1.4);
    }
}

@keyframes pulse-error {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.2);
    }
    100% {
        opacity: 0;
        transform: scale(1.4);
    }
}

@keyframes pulse-warning {
    0% {
        opacity: 1;
        transform: scale(1);
    }
    60% {
        opacity: 0.5;
        transform: scale(1.25);
    }
    100% {
        opacity: 0;
        transform: scale(1.5);
    }
}

/* Session Status Text */
#sessionStatusText {
    font-size: 0.75rem;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Restart Session Button */
#restartSessionBtn {
    border-radius: 50%;
    width: 32px;
    height: 32px;
    padding: 0;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.2s ease;
}

#restartSessionBtn:hover:not(:disabled) {
    background-color: #0d6efd;
    border-color: #0d6efd;
    color: white;
    transform: rotate(180deg);
}

#restartSessionBtn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}
