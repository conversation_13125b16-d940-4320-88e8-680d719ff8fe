import os
import logging
import json
from pathlib import Path

logger = logging.getLogger(__name__)

def get_reports_directory():
    """Get reports directory with platform isolation"""
    try:
        from app.utils.directory_paths_db import DirectoryPathsDB
        db = DirectoryPathsDB()
        reports_dir = db.get_reports_directory()
        if reports_dir:
            return reports_dir
    except Exception:
        pass
    
    # Try config.json
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
            if 'reports_directory' in config:
                return config['reports_directory']
    except Exception:
        pass
    
    # Default with platform suffix
    return 'reports_ios'

def get_test_cases_directory():
    """Get test cases directory with platform isolation"""
    try:
        from app.utils.directory_paths_db import DirectoryPathsDB
        db = DirectoryPathsDB()
        test_cases_dir = db.get_test_cases_directory()
        if test_cases_dir:
            return test_cases_dir
    except Exception:
        pass
    
    # Try config.json
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
            if 'test_cases_directory' in config:
                return config['test_cases_directory']
    except Exception:
        pass
    
    # Default with platform suffix
    return 'test_cases_ios'
