"""
Bulk Locator Manager
Handles bulk search and replace operations for locators across test cases and test suites
"""

import os
import json
import shutil
import re
import sqlite3
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import logging

logger = logging.getLogger(__name__)

class BulkLocatorManager:
    """Manager for bulk locator search and replace operations"""
    
    def __init__(self, test_cases_dir: str, db_path: str):
        """Initialize the bulk locator manager"""
        self.test_cases_dir = Path(test_cases_dir)
        self.db_path = db_path
        self.backup_dir = Path("tmp/backup_locators")
        
        # Ensure directories exist
        self.test_cases_dir.mkdir(parents=True, exist_ok=True)
        self.backup_dir.mkdir(parents=True, exist_ok=True)
    
    def search_locators(self, folder_path: str, locator_value: str) -> List[Dict[str, Any]]:
        """
        Search for locators across test cases in the specified folder
        
        Args:
            folder_path: Path to search within (can be relative to test_cases_dir)
            locator_value: Locator value to search for
            
        Returns:
            List of dictionaries containing search results
        """
        results = []
        search_path = Path(folder_path) if os.path.isabs(folder_path) else self.test_cases_dir / folder_path
        
        if not search_path.exists():
            logger.warning(f"Search path does not exist: {search_path}")
            return results
        
        # Search in JSON files
        for json_file in search_path.rglob('*.json'):
            if json_file.name.endswith('.bak'):
                continue
                
            try:
                with open(json_file, 'r', encoding='utf-8') as f:
                    test_case = json.load(f)
                
                # Search through actions
                file_results = self._search_in_test_case(test_case, json_file, locator_value)
                results.extend(file_results)
                
            except Exception as e:
                logger.error(f"Error reading {json_file}: {str(e)}")
        
        return results
    
    def _search_in_test_case(self, test_case: Dict, file_path: Path, locator_value: str) -> List[Dict[str, Any]]:
        """Search for locator value within a single test case"""
        results = []
        actions = test_case.get('actions', [])
        
        for i, action in enumerate(actions):
            action_type = action.get('action_type', '')
            action_id = action.get('action_id', '')
            
            # Check various locator fields
            locator_fields = ['xpath', 'id', 'accessibility_id', 'class_name', 'name', 'locator', 'locator_value']
            
            for field in locator_fields:
                if field in action:
                    field_value = str(action[field])
                    if self._matches_locator(field_value, locator_value):
                        results.append({
                            'filename': file_path.name,
                            'filepath': str(file_path),
                            'action_type': action_type,
                            'action_id': action_id,
                            'action_index': i,
                            'locator_field': field,
                            'locator_value': field_value,
                            'line_number': self._estimate_line_number(test_case, i)
                        })
        
        return results
    
    def _matches_locator(self, field_value: str, search_value: str) -> bool:
        """Check if a field value matches the search criteria"""
        # Exact match
        if field_value == search_value:
            return True
        
        # Partial match (case-insensitive)
        if search_value.lower() in field_value.lower():
            return True
        
        # Environment variable match
        if search_value.startswith('env[') and search_value in field_value:
            return True
        
        return False
    
    def _estimate_line_number(self, test_case: Dict, action_index: int) -> int:
        """Estimate line number for an action in the JSON file"""
        # Rough estimation: header lines + action lines
        base_lines = 10  # Estimated lines for test case metadata
        lines_per_action = 8  # Estimated lines per action
        return base_lines + (action_index * lines_per_action)
    
    def replace_locators(self, search_results: List[Dict[str, Any]], old_value: str, new_value: str) -> Dict[str, Any]:
        """
        Replace locators across multiple files
        
        Args:
            search_results: Results from search_locators
            old_value: Old locator value to replace
            new_value: New locator value
            
        Returns:
            Dictionary with operation results
        """
        try:
            # Create backup
            backup_timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            backup_path = self.backup_dir / f"backup_{backup_timestamp}"
            backup_path.mkdir(exist_ok=True)
            
            # Group results by file
            files_to_update = {}
            for result in search_results:
                filepath = result['filepath']
                if filepath not in files_to_update:
                    files_to_update[filepath] = []
                files_to_update[filepath].append(result)
            
            updated_files = []
            updated_count = 0
            
            for filepath, file_results in files_to_update.items():
                try:
                    # Create backup of original file
                    original_path = Path(filepath)
                    backup_file_path = backup_path / original_path.name
                    shutil.copy2(original_path, backup_file_path)
                    
                    # Load and update the test case
                    with open(original_path, 'r', encoding='utf-8') as f:
                        test_case = json.load(f)
                    
                    # Update locators
                    file_updated = False
                    for result in file_results:
                        action_index = result['action_index']
                        locator_field = result['locator_field']
                        
                        if action_index < len(test_case.get('actions', [])):
                            action = test_case['actions'][action_index]
                            if locator_field in action:
                                old_field_value = str(action[locator_field])
                                new_field_value = old_field_value.replace(old_value, new_value)
                                action[locator_field] = new_field_value
                                file_updated = True
                                updated_count += 1
                    
                    if file_updated:
                        # Update timestamp
                        test_case['updated'] = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
                        
                        # Save updated file
                        with open(original_path, 'w', encoding='utf-8') as f:
                            json.dump(test_case, f, indent=2, ensure_ascii=False)
                        
                        updated_files.append(str(original_path))
                        
                        # Update database
                        self._update_database_locators(original_path.name, old_value, new_value)
                
                except Exception as e:
                    logger.error(f"Error updating file {filepath}: {str(e)}")
            
            return {
                'success': True,
                'updated_files': updated_files,
                'updated_count': updated_count,
                'backup_path': str(backup_path),
                'backup_timestamp': backup_timestamp
            }
            
        except Exception as e:
            logger.error(f"Error in replace_locators: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def _update_database_locators(self, test_case_name: str, old_value: str, new_value: str):
        """Update locator values in the database"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Update locators_repository table
            cursor.execute('''
                UPDATE locators_repository 
                SET locator_value = ?, last_used_date = ?
                WHERE test_case_name = ? AND locator_value = ?
            ''', (new_value, datetime.now().isoformat(), test_case_name, old_value))
            
            conn.commit()
            conn.close()
            
        except Exception as e:
            logger.error(f"Error updating database locators: {str(e)}")
    
    def undo_last_changes(self, backup_timestamp: str) -> Dict[str, Any]:
        """
        Undo the last bulk replace operation
        
        Args:
            backup_timestamp: Timestamp of the backup to restore
            
        Returns:
            Dictionary with operation results
        """
        try:
            backup_path = self.backup_dir / f"backup_{backup_timestamp}"
            
            if not backup_path.exists():
                return {
                    'success': False,
                    'error': f'Backup not found: {backup_timestamp}'
                }
            
            restored_files = []
            
            # Restore files from backup
            for backup_file in backup_path.glob('*.json'):
                original_path = self.test_cases_dir / backup_file.name
                
                try:
                    shutil.copy2(backup_file, original_path)
                    restored_files.append(str(original_path))
                except Exception as e:
                    logger.error(f"Error restoring {backup_file}: {str(e)}")
            
            return {
                'success': True,
                'restored_files': restored_files,
                'restored_count': len(restored_files)
            }
            
        except Exception as e:
            logger.error(f"Error in undo_last_changes: {str(e)}")
            return {
                'success': False,
                'error': str(e)
            }
    
    def get_available_backups(self) -> List[Dict[str, Any]]:
        """Get list of available backups"""
        backups = []
        
        try:
            for backup_dir in self.backup_dir.glob('backup_*'):
                if backup_dir.is_dir():
                    timestamp = backup_dir.name.replace('backup_', '')
                    file_count = len(list(backup_dir.glob('*.json')))
                    
                    # Parse timestamp
                    try:
                        dt = datetime.strptime(timestamp, '%Y%m%d_%H%M%S')
                        formatted_date = dt.strftime('%Y-%m-%d %H:%M:%S')
                    except:
                        formatted_date = timestamp
                    
                    backups.append({
                        'timestamp': timestamp,
                        'formatted_date': formatted_date,
                        'file_count': file_count,
                        'path': str(backup_dir)
                    })
            
            # Sort by timestamp (newest first)
            backups.sort(key=lambda x: x['timestamp'], reverse=True)
            
        except Exception as e:
            logger.error(f"Error getting available backups: {str(e)}")
        
        return backups
    
    def validate_locator_format(self, locator_value: str) -> Dict[str, Any]:
        """
        Validate locator format
        
        Args:
            locator_value: Locator value to validate
            
        Returns:
            Dictionary with validation results
        """
        if not locator_value or not locator_value.strip():
            return {
                'valid': False,
                'error': 'Locator value cannot be empty'
            }
        
        # Check for common locator patterns
        patterns = {
            'xpath': r'^(//|\.//|\./)',
            'id': r'^[a-zA-Z][a-zA-Z0-9_\-\.]*$',
            'class_name': r'^[a-zA-Z][a-zA-Z0-9_\-\.]*$',
            'accessibility_id': r'^.+$',
            'env_var': r'^env\[[^\]]+\]$'
        }
        
        detected_type = 'unknown'
        for pattern_type, pattern in patterns.items():
            if re.match(pattern, locator_value):
                detected_type = pattern_type
                break
        
        return {
            'valid': True,
            'detected_type': detected_type,
            'locator_value': locator_value.strip()
        }
