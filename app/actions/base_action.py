import logging

class BaseAction:
    """Base class for all action handlers"""

    def __init__(self, controller=None):
        """
        Initialize the action handler

        Args:
            controller: The device controller to use for actions
        """
        self.controller = controller
        self.logger = logging.getLogger(self.__class__.__name__)

    def execute(self, params):
        """
        Execute the action with the given parameters

        Args:
            params: Dictionary of parameters for the action

        Returns:
            dict: Result of the action with status and message
        """
        raise NotImplementedError("Subclasses must implement execute method")

    def set_controller(self, controller):
        """
        Update the controller for the action handler

        Args:
            controller: The device controller to use
        """
        self.controller = controller

    def get_global_timeout(self, default=60):
        """
        Get the global element timeout value from the database

        Args:
            default: Default timeout value if global setting can't be retrieved

        Returns:
            int: The global timeout value in seconds
        """
        try:
            try:
                from ..utils.global_values_db import GlobalValuesDB
            except ImportError:
                import sys
                import os
                current_dir = os.path.dirname(os.path.abspath(__file__))
                parent_dir = os.path.dirname(current_dir)
                if parent_dir not in sys.path:
                    sys.path.insert(0, parent_dir)
                from utils.global_values_db import GlobalValuesDB
            global_values_db = GlobalValuesDB()
            timeout = global_values_db.get_value('default_element_timeout', default)
            return int(timeout)
        except Exception as e:
            self.logger.warning(f"Could not get default timeout from global settings: {e}")
            return default

    def take_screenshot_after_action(self):
        """PERFORMANCE OPTIMIZATION: Automatic screenshot capture disabled for improved test execution speed"""
        # Screenshots are now only taken when explicitly requested via takeScreenshot actions
        self.logger.info("Automatic screenshot capture disabled for performance optimization")
        return None

    def execute_with_retry(self, operation_func, max_retries=3, retry_delay=2, operation_name="operation", action_timeout=None):
        """
        Execute an operation with retry mechanism for better stability and timeout compliance

        Args:
            operation_func: Function to execute
            max_retries: Maximum number of retry attempts
            retry_delay: Delay between retries in seconds
            operation_name: Name of the operation for logging
            action_timeout: Original action timeout in seconds (for compliance enforcement)

        Returns:
            Result of the operation or error dict
        """
        import time
        from requests.exceptions import ConnectionError, ReadTimeout
        from selenium.common.exceptions import WebDriverException

        action_start_time = time.time()
        last_exception = None

        for attempt in range(max_retries + 1):
            try:
                # Check timeout compliance before each attempt
                if action_timeout:
                    elapsed_time = time.time() - action_start_time
                    remaining_timeout = action_timeout - elapsed_time

                    if remaining_timeout <= 0:
                        self.logger.warning(f"{operation_name} timeout exceeded ({action_timeout}s) before attempt {attempt + 1}")
                        return {
                            "status": "error",
                            "message": f"Action timeout ({action_timeout}s) exceeded during retry attempts"
                        }

                    self.logger.info(f"{operation_name} attempt {attempt + 1}: {remaining_timeout:.1f}s remaining of {action_timeout}s timeout")

                if attempt > 0:
                    self.logger.info(f"Retry attempt {attempt}/{max_retries} for {operation_name}")
                    time.sleep(retry_delay)

                result = operation_func()

                if attempt > 0:
                    self.logger.info(f"{operation_name} succeeded on retry attempt {attempt}")

                return result

            except (ConnectionError, ReadTimeout, WebDriverException) as e:
                last_exception = e
                error_msg = str(e)

                # Check timeout compliance before recovery
                if action_timeout:
                    elapsed_time = time.time() - action_start_time
                    remaining_timeout = action_timeout - elapsed_time

                    if remaining_timeout <= retry_delay:
                        self.logger.warning(f"{operation_name} insufficient time for recovery: {remaining_timeout:.1f}s remaining")
                        return {
                            "status": "error",
                            "message": f"Action timeout ({action_timeout}s) exceeded - insufficient time for session recovery"
                        }

                # Check for specific timeout errors
                if "Read timed out" in error_msg or "HTTPConnectionPool" in error_msg:
                    self.logger.warning(f"{operation_name} failed with timeout error (attempt {attempt + 1}/{max_retries + 1}): {error_msg}")

                    # Try to reconnect if controller supports it and we have time
                    if hasattr(self.controller, 'reconnect_if_needed'):
                        try:
                            recovery_start = time.time()
                            self.controller.reconnect_if_needed()
                            recovery_time = time.time() - recovery_start
                            self.logger.info(f"Session recovery took {recovery_time:.1f}s")
                        except Exception as reconnect_e:
                            self.logger.warning(f"Reconnection attempt failed: {reconnect_e}")
                else:
                    self.logger.warning(f"{operation_name} failed (attempt {attempt + 1}/{max_retries + 1}): {error_msg}")

                if attempt == max_retries:
                    break

                # Check timeout compliance before retry delay
                if action_timeout:
                    elapsed_time = time.time() - action_start_time
                    remaining_timeout = action_timeout - elapsed_time

                    if remaining_timeout <= retry_delay:
                        self.logger.warning(f"{operation_name} insufficient time for retry delay: {remaining_timeout:.1f}s remaining")
                        break

            except Exception as e:
                # For non-retryable exceptions, fail immediately
                self.logger.error(f"{operation_name} failed with non-retryable error: {e}")
                return {
                    "status": "error",
                    "message": f"{operation_name} failed: {str(e)}"
                }

        # All retries exhausted
        return {
            "status": "error",
            "message": f"{operation_name} failed after {max_retries + 1} attempts. Last error: {str(last_exception)}"
        }

    def scale_ios_coordinates(self, coordinates, device_info=None):
        """
        Scale coordinates for iOS devices to match the UI scale

        This implements similar scaling logic to the client-side JavaScript:
        const scaleX = this.deviceScreen.naturalWidth / rect.width;
        const scaleY = this.deviceScreen.naturalHeight / rect.height;

        Args:
            coordinates: Tuple of (x, y) coordinates to scale
            device_info: Optional device info to use for scaling factors

        Returns:
            Tuple of scaled (x, y) coordinates
        """
        from airtest.core.helper import G

        # If not iOS or no coordinates, return as is
        if not hasattr(self.controller, 'platform_name') or self.controller.platform_name != 'iOS':
            return coordinates

        if not coordinates or len(coordinates) != 2:
            return coordinates

        # Get original coordinates
        original_x, original_y = coordinates

        # Try to get device dimensions from controller
        device_dimensions = None
        if hasattr(self.controller, 'get_device_dimensions'):
            try:
                device_dimensions = self.controller.get_device_dimensions()
                self.logger.info(f"Device physical dimensions: {device_dimensions}")
            except Exception as dim_err:
                self.logger.warning(f"Could not get device dimensions: {dim_err}")

        # Try to get UI dimensions from Airtest
        ui_dimensions = None
        try:
            if hasattr(G, 'DEVICE') and G.DEVICE and hasattr(G.DEVICE, 'display_info'):
                display_info = G.DEVICE.display_info
                if display_info and 'width' in display_info and 'height' in display_info:
                    ui_dimensions = (display_info['width'], display_info['height'])
                    self.logger.info(f"Device UI dimensions: {ui_dimensions}")
        except Exception as e:
            self.logger.warning(f"Could not get Airtest display info: {e}")

        # If we have both dimensions, calculate actual scaling factor
        if device_dimensions and ui_dimensions and device_dimensions[0] > 0 and device_dimensions[1] > 0:
            # This matches the client-side scaling logic
            scale_x = ui_dimensions[0] / device_dimensions[0]
            scale_y = ui_dimensions[1] / device_dimensions[1]

            scaled_x = int(original_x * scale_x)
            scaled_y = int(original_y * scale_y)

            self.logger.info(f"iOS coordinate scaling: ({original_x}, {original_y}) -> ({scaled_x}, {scaled_y})")
            return (scaled_x, scaled_y)
        else:
            # Apply a fixed scaling factor based on the examples given
            # Appears to be approximately 1/3 (from 651->214, 2623->876)
            scale_factor = 0.33

            scaled_x = int(original_x * scale_factor)
            scaled_y = int(original_y * scale_factor)

            self.logger.info(f"iOS coordinate scaling (fixed): ({original_x}, {original_y}) -> ({scaled_x}, {scaled_y})")
            return (scaled_x, scaled_y)