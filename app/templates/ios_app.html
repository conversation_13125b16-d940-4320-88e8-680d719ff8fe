<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>iOS Automation Platform - Mobile Testing Suite</title>
    <link rel="icon" href="{{ url_for('static', filename='img/favicon.ico') }}" type="image/x-icon">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap">
    <style>
        body {
            font-family: 'Inter', sans-serif;
            background-color: #f8f9fa;
        }
        .app-header {
            background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
            color: white;
            padding: 2rem;
            border-radius: 10px;
            margin-bottom: 2rem;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        .auth-bar {
            background: white;
            padding: 1rem 2rem;
            border-radius: 10px;
            margin-bottom: 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .user-info {
            display: flex;
            align-items: center;
            gap: 1rem;
        }
        .nav-tabs {
            border-bottom: 2px solid #e9ecef;
            margin-bottom: 2rem;
        }
        .nav-tabs .nav-link {
            border: none;
            border-radius: 10px 10px 0 0;
            color: #6c757d;
            font-weight: 500;
            padding: 1rem 1.5rem;
            margin-right: 0.5rem;
        }
        .nav-tabs .nav-link.active {
            background: white;
            color: #495057;
            border-bottom: 3px solid #007AFF;
        }
        .tab-content {
            background: white;
            border-radius: 10px;
            padding: 2rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
        }
        .device-card {
            border: 1px solid #e9ecef;
            border-radius: 10px;
            padding: 1.5rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        .device-card:hover {
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }
        
        /* Action Log Styles */
        .action-log-success {
            background-color: #d1edff;
            border-left: 4px solid #28a745;
        }
        
        .action-log-error {
            background-color: #f8d7da;
            border-left: 4px solid #dc3545;
        }
        
        .action-log-warning {
            background-color: #fff3cd;
            border-left: 4px solid #ffc107;
        }
        
        .action-log-info {
            background-color: #e2f3ff;
            border-left: 4px solid #17a2b8;
        }
        .btn-primary {
            background: linear-gradient(135deg, #007AFF 0%, #5856D6 100%);
            border: none;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
        }
        .btn-outline-primary {
            border: 2px solid #007AFF;
            color: #007AFF;
            border-radius: 8px;
            padding: 0.75rem 1.5rem;
            font-weight: 500;
        }
        .btn-outline-primary:hover {
            background: #007AFF;
            border-color: #007AFF;
        }
        .status-badge {
            padding: 0.5rem 1rem;
            border-radius: 20px;
            font-size: 0.875rem;
            font-weight: 500;
        }
        .status-connected {
            background-color: #d4edda;
            color: #155724;
        }
        .status-disconnected {
            background-color: #f8d7da;
            color: #721c24;
        }
    </style>
</head>
<body>
    <div class="container-fluid p-4">
        <!-- Authentication Bar -->
        <div class="auth-bar">
            <div class="user-info">
                <i class="bi bi-person-circle fs-4 text-primary"></i>
                <div>
                    <strong id="user-email">{{ session.user_email or request.args.get('user_email') or 'User' }}</strong>
                    <small class="text-muted d-block">iOS Automation Platform</small>
                    <small class="text-success d-block" id="session-status">
                        <i class="bi bi-shield-check"></i> Authenticated
                    </small>
                </div>
            </div>
            <div class="d-flex gap-2">
                {% if return_url %}
                    <a href="{{ return_url }}" class="btn btn-outline-primary btn-sm">
                        <i class="bi bi-arrow-left"></i> Return to Dashboard
                    </a>
                {% else %}
                    <a href="http://localhost:8080/dashboard" class="btn btn-outline-primary btn-sm" id="dashboard-link">
                        <i class="bi bi-arrow-left me-1"></i>Return to Dashboard
                    </a>
                {% endif %}
                <button onclick="logout()" class="btn btn-outline-danger btn-sm">
                    <i class="bi bi-box-arrow-right me-1"></i>Close App
                </button>
            </div>
        </div>

        <!-- App Header -->
        <div class="app-header">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <h2><i class="bi bi-apple me-2"></i>iOS Automation Platform</h2>
                    <p class="mb-0">Professional iOS app testing and automation suite</p>
                </div>
                <div class="text-end">
                    <div class="badge bg-light text-dark fs-6">
                        <i class="bi bi-server"></i> Port: <span id="current-port">{{ request.environ.get('SERVER_PORT', '8081') }}</span>
                    </div>
                    <div class="badge bg-success fs-6 mt-1">
                        <i class="bi bi-wifi"></i> Connected
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <ul class="nav nav-tabs" id="main-tabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="device-tab" data-bs-toggle="tab" data-bs-target="#device-control-tab" type="button" role="tab">
                    <i class="bi bi-phone me-2"></i>Device Control
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="test-cases-tab-btn" data-bs-toggle="tab" data-bs-target="#test-cases-tab" type="button" role="tab">
                    <i class="bi bi-card-checklist me-2"></i>Test Cases
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="test-suites-tab-btn" data-bs-toggle="tab" data-bs-target="#test-suites-tab" type="button" role="tab">
                    <i class="bi bi-collection me-2"></i>Test Suites
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="settings-tab-btn" data-bs-toggle="tab" data-bs-target="#settings-tab" type="button" role="tab">
                    <i class="bi bi-gear me-2"></i>Settings
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="environments-tab-btn" data-bs-toggle="tab" data-bs-target="#environments-tab" type="button" role="tab">
                    <i class="bi bi-layers me-2"></i>Environments
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="tools-tab-btn" data-bs-toggle="tab" data-bs-target="#tools-tab" type="button" role="tab">
                    <i class="bi bi-tools me-2"></i>Tools
                </button>
            </li>
        </ul>

        <!-- Tab Content -->
        <div class="tab-content" id="main-tab-content">
            <!-- Device Control Tab -->
            <div class="tab-pane fade show active" id="device-control-tab" role="tabpanel">
                <div class="row">
                    <div class="col-md-6">
                        <h4><i class="bi bi-phone me-2"></i>Device Connection</h4>
                        <div class="card">
                            <div class="card-body">
                                <div class="mb-3">
                                    <label for="device-select" class="form-label">Select Device:</label>
                                    <div class="input-group">
                                        <select class="form-select" id="device-select" onchange="onDeviceSelectionChange()">
                                            <option value="">No devices available</option>
                                        </select>
                                        <button class="btn btn-outline-secondary" type="button" id="refresh-devices-btn" onclick="refreshDevices()">
                                            <i class="bi bi-arrow-clockwise"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="mb-3">
                                    <div class="d-flex gap-2">
                                        <button class="btn btn-success" id="connect-btn" onclick="connectToDevice()" disabled>
                                            <i class="bi bi-plug me-2"></i>Connect
                                        </button>
                                        <button class="btn btn-danger" id="disconnect-btn" onclick="disconnectDevice()" disabled>
                                            <i class="bi bi-plug-fill me-2"></i>Disconnect
                                        </button>
                                    </div>
                                </div>
                                <div id="connection-status" class="alert alert-secondary" role="alert">
                                    <i class="bi bi-info-circle me-2"></i>No device selected
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <h4><i class="bi bi-display me-2"></i>Device Screen</h4>
                        <div class="card">
                            <div class="card-body">
                                <div class="device-screen-container">
                                    <div id="device-screen" class="text-center p-4 border rounded" style="min-height: 400px; background-color: #f8f9fa;">
                                        <i class="bi bi-phone fs-1 text-muted mb-3"></i>
                                        <p class="text-muted">Connect to a device to view screen</p>
                                    </div>
                                    <div class="mt-3 d-flex gap-2 justify-content-center">
                                        <button class="btn btn-outline-primary btn-sm" id="refresh-screenshot-btn" onclick="refreshScreenshot()" disabled>
                                            <i class="bi bi-arrow-clockwise me-1"></i>Refresh
                                        </button>
                                        <button class="btn btn-outline-secondary btn-sm" id="web-inspector-btn" onclick="openWebInspector()" disabled>
                                            <i class="bi bi-code-square me-1"></i>Web Inspector
                                        </button>
                                        <button class="btn btn-outline-info btn-sm" id="detect-btn" onclick="detectElements()" disabled>
                                            <i class="bi bi-search me-1"></i>Detect
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Action Log Section -->
                <div class="row mt-4">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h5><i class="bi bi-list-ul me-2"></i>Action Log</h5>
                                <div class="btn-group btn-group-sm" role="group">
                                    <button type="button" class="btn btn-outline-secondary" onclick="clearActionLog()">
                                        <i class="bi bi-trash"></i> Clear
                                    </button>
                                    <button type="button" class="btn btn-outline-secondary" onclick="exportActionLog()">
                                        <i class="bi bi-download"></i> Export
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <div id="action-log" class="border rounded p-3" style="height: 300px; overflow-y: auto; background-color: #f8f9fa; font-family: monospace; font-size: 0.9em;">
                                    <div class="text-muted">Action log will appear here...</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Test Cases Tab -->
            <div class="tab-pane fade" id="test-cases-tab" role="tabpanel">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4><i class="bi bi-card-checklist me-2"></i>Test Cases</h4>
                    <button class="btn btn-primary" onclick="createNewTestCase()">
                        <i class="bi bi-plus-circle me-2"></i>Create New Test Case
                    </button>
                </div>
                <div id="test-cases-list">
                    <div class="text-center p-5">
                        <i class="bi bi-card-checklist fs-1 text-muted mb-3"></i>
                        <p class="text-muted">No test cases created yet</p>
                        <button class="btn btn-outline-primary" onclick="createNewTestCase()">
                            <i class="bi bi-plus-circle me-2"></i>Create Your First Test Case
                        </button>
                    </div>
                </div>
            </div>

            <!-- Test Suites Tab -->
            <div class="tab-pane fade" id="test-suites-tab" role="tabpanel">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h4><i class="bi bi-collection me-2"></i>Test Suites</h4>
                    <button class="btn btn-primary" onclick="createNewTestSuite()">
                        <i class="bi bi-plus-circle me-2"></i>Create New Test Suite
                    </button>
                </div>
                <div id="test-suites-list">
                    <div class="text-center p-5">
                        <i class="bi bi-collection fs-1 text-muted mb-3"></i>
                        <p class="text-muted">No test suites created yet</p>
                        <button class="btn btn-outline-primary" onclick="createNewTestSuite()">
                            <i class="bi bi-plus-circle me-2"></i>Create Your First Test Suite
                        </button>
                    </div>
                </div>
            </div>

            <!-- Settings Tab -->
            <div class="tab-pane fade" id="settings-tab" role="tabpanel">
                <h4><i class="bi bi-gear me-2"></i>iOS Settings</h4>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-apple me-2"></i>iOS Configuration</h5>
                            </div>
                            <div class="card-body">
                                <div class="mb-3">
                                    <label class="form-label">WebDriverAgent Path</label>
                                    <input type="text" class="form-control" id="wda-path" placeholder="/path/to/WebDriverAgent">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Appium Server URL</label>
                                    <input type="text" class="form-control" id="appium-url" value="http://localhost:4723">
                                </div>
                                <div class="mb-3">
                                    <label class="form-label">Team ID</label>
                                    <input type="text" class="form-control" id="team-id" placeholder="Apple Developer Team ID">
                                </div>
                                <button class="btn btn-primary">Save Settings</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5><i class="bi bi-shield-check me-2"></i>Security Settings</h5>
                            </div>
                            <div class="card-body">
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="auto-accept-alerts">
                                    <label class="form-check-label" for="auto-accept-alerts">
                                        Auto-accept system alerts
                                    </label>
                                </div>
                                <div class="form-check mb-3">
                                    <input class="form-check-input" type="checkbox" id="reduce-motion">
                                    <label class="form-check-label" for="reduce-motion">
                                        Reduce motion for testing
                                    </label>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Environments Tab -->
            <div class="tab-pane fade" id="environments-tab" role="tabpanel">
                <h4><i class="bi bi-layers me-2"></i>Test Environments</h4>
                <div class="text-center p-5">
                    <i class="bi bi-layers fs-1 text-muted mb-3"></i>
                    <p class="text-muted">Environment management coming soon</p>
                </div>
            </div>

            <!-- Tools Tab -->
            <div class="tab-pane fade" id="tools-tab" role="tabpanel">
                <h4><i class="bi bi-tools me-2"></i>iOS Tools</h4>
                <div class="row">
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-phone fs-1 text-primary mb-3"></i>
                                <h5>UI Inspector</h5>
                                <p class="text-muted">Inspect iOS UI elements and properties</p>
                                <button class="btn btn-outline-primary">Launch Inspector</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-camera-video fs-1 text-success mb-3"></i>
                                <h5>Screen Recorder</h5>
                                <p class="text-muted">Record device screen during testing</p>
                                <button class="btn btn-outline-success">Start Recording</button>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4">
                        <div class="card h-100">
                            <div class="card-body text-center">
                                <i class="bi bi-file-earmark-text fs-1 text-warning mb-3"></i>
                                <h5>Console Logs</h5>
                                <p class="text-muted">View device and app console logs</p>
                                <button class="btn btn-outline-warning">View Logs</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/axios/dist/axios.min.js"></script>
    <script>
        // Authentication functions
        function logout() {
            if (confirm('Are you sure you want to logout?')) {
                fetch('/logout', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        window.location.href = '/login';
                    }
                })
                .catch(error => {
                    console.error('Logout error:', error);
                    window.location.href = '/login';
                });
            }
        }

        // Global variables
        let currentDeviceId = null;
        let isConnected = false;
        let sessionId = null;
        let actionLogEntries = [];
        
        // Session management
        function initializeSession() {
            // Handle SaaS session token and user information
            const sessionToken = '{{ session_token or "" }}';
            const userEmail = '{{ request.args.get("user_email") or session.user_email or "" }}';
            const returnUrl = '{{ return_url or "" }}';

            if (sessionToken && userEmail) {
                // Update user info in the UI
                document.getElementById('user-email').textContent = userEmail;
                document.getElementById('session-status').innerHTML = '<i class="bi bi-shield-check"></i> Authenticated';

                // Update dashboard link if no return URL
                if (!returnUrl) {
                    const dashboardLink = document.getElementById('dashboard-link');
                    if (dashboardLink) {
                        dashboardLink.href = 'http://localhost:8080/dashboard';
                    }
                }

                console.log('Session initialized for user:', userEmail);
            } else {
                // No session token, show warning
                document.getElementById('session-status').innerHTML = '<i class="bi bi-exclamation-triangle"></i> Session Warning';
                document.getElementById('session-status').className = 'text-warning d-block';
            }

            // Generate or retrieve local session ID for device management
            sessionId = localStorage.getItem('ios_session_id');
            if (!sessionId) {
                sessionId = 'ios_session_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
                localStorage.setItem('ios_session_id', sessionId);
            }
            console.log('Local session ID:', sessionId);
        }
        
        function getSessionHeaders() {
            return {
                'Content-Type': 'application/json',
                'X-Session-ID': sessionId || '',
                'X-Device-ID': currentDeviceId || ''
            };
        }

        // Device management functions
        function refreshDevices() {
            const deviceSelect = document.getElementById('device-select');
            const refreshBtn = document.getElementById('refresh-devices-btn');
            
            logAction('info', 'Refreshing device list');
            
            // Show loading state
            refreshBtn.disabled = true;
            refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise spin"></i>';
            
            // Fetch devices from the API
            fetch('/api/devices', {
                headers: getSessionHeaders()
            })
                .then(response => response.json())
                .then(data => {
                    deviceSelect.innerHTML = '<option value="">Select a device...</option>';
                    
                    if (data.devices && data.devices.length > 0) {
                        data.devices.forEach(device => {
                            const option = document.createElement('option');
                            option.value = device.device_id;
                            option.textContent = `${device.name || 'iOS Device'} (${device.device_id})`;
                            deviceSelect.appendChild(option);
                        });
                        updateConnectionStatus('Devices found. Select a device to connect.', 'info');
                        logAction('success', 'Device list refreshed', `Found ${data.devices.length} device(s)`);
                    } else {
                        updateConnectionStatus('No devices found. Make sure your iOS device is connected and trusted.', 'warning');
                        logAction('warning', 'No devices found', 'Make sure your iOS device is connected and trusted');
                    }
                })
                .catch(error => {
                    console.error('Error fetching devices:', error);
                    updateConnectionStatus('Error fetching devices. Please try again.', 'danger');
                    logAction('error', 'Failed to refresh device list', error.message);
                })
                .finally(() => {
                    refreshBtn.disabled = false;
                    refreshBtn.innerHTML = '<i class="bi bi-arrow-clockwise"></i>';
                });
        }

        function connectToDevice() {
            const deviceSelect = document.getElementById('device-select');
            const selectedDeviceId = deviceSelect.value;
            
            if (!selectedDeviceId) {
                updateConnectionStatus('Please select a device first.', 'warning');
                logAction('warning', 'No device selected', 'Please select a device from the dropdown');
                return;
            }
            
            const connectBtn = document.getElementById('connect-btn');
            connectBtn.disabled = true;
            connectBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Connecting...';
            
            updateConnectionStatus('Connecting to device...', 'info');
            logAction('info', 'Connecting to device', `Device ID: ${selectedDeviceId}`);
            
            // Connect to device via API
            fetch('/api/device/connect', {
                method: 'POST',
                headers: getSessionHeaders(),
                body: JSON.stringify({
                    device_id: selectedDeviceId,
                    platform: 'iOS'
                })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    currentDeviceId = selectedDeviceId;
                    isConnected = true;
                    updateUIForConnectedState(true);
                    updateConnectionStatus(`Connected to device: ${selectedDeviceId}`, 'success');
                    logAction('success', 'Device connected successfully', `Connected to device: ${selectedDeviceId}`);
                    refreshScreenshot();
                } else {
                    updateConnectionStatus(`Connection failed: ${data.error || 'Unknown error'}`, 'danger');
                    logAction('error', 'Failed to connect to device', data.error || 'Unknown error');
                }
            })
            .catch(error => {
                console.error('Connection error:', error);
                updateConnectionStatus('Connection failed. Please try again.', 'danger');
                logAction('error', 'Connection error', error.message);
            })
            .finally(() => {
                connectBtn.disabled = false;
                connectBtn.innerHTML = '<i class="bi bi-plug me-2"></i>Connect';
            });
        }

        function disconnectDevice() {
            if (!currentDeviceId) {
                logAction('warning', 'No device to disconnect', 'No device is currently connected');
                return;
            }
            
            const disconnectBtn = document.getElementById('disconnect-btn');
            disconnectBtn.disabled = true;
            disconnectBtn.innerHTML = '<i class="bi bi-hourglass-split me-2"></i>Disconnecting...';
            
            updateConnectionStatus('Disconnecting from device...', 'info');
            logAction('info', 'Disconnecting from device', `Device ID: ${currentDeviceId}`);
            
            // Disconnect from device via API
            fetch('/api/device/disconnect', {
                method: 'POST',
                headers: getSessionHeaders(),
                body: JSON.stringify({
                    device_id: currentDeviceId
                })
            })
            .then(response => response.json())
            .then(data => {
                const disconnectedDeviceId = currentDeviceId;
                currentDeviceId = null;
                isConnected = false;
                updateUIForConnectedState(false);
                updateConnectionStatus('Disconnected from device', 'secondary');
                clearDeviceScreen();
                logAction('success', 'Device disconnected successfully', `Disconnected from device: ${disconnectedDeviceId}`);
            })
            .catch(error => {
                console.error('Disconnection error:', error);
                updateConnectionStatus('Disconnection failed', 'warning');
                logAction('error', 'Disconnection error', error.message);
            })
            .finally(() => {
                disconnectBtn.disabled = false;
                disconnectBtn.innerHTML = '<i class="bi bi-plug-fill me-2"></i>Disconnect';
            });
        }

        function updateConnectionStatus(message, type) {
            const statusDiv = document.getElementById('connection-status');
            statusDiv.className = `alert alert-${type}`;
            statusDiv.innerHTML = `<i class="bi bi-info-circle me-2"></i>${message}`;
        }

        function updateUIForConnectedState(connected) {
            const connectBtn = document.getElementById('connect-btn');
            const disconnectBtn = document.getElementById('disconnect-btn');
            const deviceSelect = document.getElementById('device-select');
            const refreshBtn = document.getElementById('refresh-devices-btn');
            const screenshotBtn = document.getElementById('refresh-screenshot-btn');
            const webInspectorBtn = document.getElementById('web-inspector-btn');
            const detectBtn = document.getElementById('detect-btn');
            
            if (connected) {
                connectBtn.disabled = true;
                disconnectBtn.disabled = false;
                deviceSelect.disabled = true;
                refreshBtn.disabled = true;
                screenshotBtn.disabled = false;
                webInspectorBtn.disabled = false;
                detectBtn.disabled = false;
            } else {
                connectBtn.disabled = false;
                disconnectBtn.disabled = true;
                deviceSelect.disabled = false;
                refreshBtn.disabled = false;
                screenshotBtn.disabled = true;
                webInspectorBtn.disabled = true;
                detectBtn.disabled = true;
            }
        }

        function refreshScreenshot() {
            if (!currentDeviceId) {
                logAction('warning', 'Cannot refresh screenshot', 'No device connected');
                return;
            }
            
            logAction('info', 'Refreshing device screenshot');
            
            const deviceScreen = document.getElementById('device-screen');
            const timestamp = Date.now();
            
            // Show loading state
            deviceScreen.innerHTML = `
                <div class="d-flex justify-content-center align-items-center" style="min-height: 400px;">
                    <div class="text-center">
                        <i class="bi bi-arrow-clockwise spin fs-1 text-primary mb-3"></i>
                        <p class="text-muted">Refreshing screenshot...</p>
                    </div>
                </div>
            `;
            
            // Fetch screenshot with session headers
            fetch(`/api/screenshot?device_id=${currentDeviceId}&t=${timestamp}`, {
                headers: getSessionHeaders()
            })
            .then(response => {
                if (response.ok) {
                    return response.blob();
                } else {
                    throw new Error('Failed to get screenshot');
                }
            })
            .then(blob => {
                const imageUrl = URL.createObjectURL(blob);
                
                // Create new image element
                const img = document.createElement('img');
                img.className = 'img-fluid';
                img.style.maxHeight = '400px';
                img.style.cursor = 'pointer';
                img.src = imageUrl;
                
                img.onload = function() {
                    deviceScreen.innerHTML = '';
                    deviceScreen.appendChild(img);
                    
                    // Add click handler for device interaction
                    img.onclick = function(event) {
                        handleDeviceClick(event, img);
                    };
                    
                    logAction('success', 'Device screenshot refreshed');
                };
                
                img.onerror = function() {
                    logAction('error', 'Failed to load screenshot image');
                    deviceScreen.innerHTML = `
                        <div class="text-center p-4">
                            <i class="bi bi-exclamation-triangle fs-1 text-warning mb-3"></i>
                            <p class="text-muted">Failed to load screenshot</p>
                            <button class="btn btn-outline-primary btn-sm" onclick="refreshScreenshot()">
                                <i class="bi bi-arrow-clockwise me-1"></i>Try Again
                            </button>
                        </div>
                    `;
                };
            })
            .catch(error => {
                console.error('Error refreshing screenshot:', error);
                logAction('error', 'Failed to refresh screenshot', error.message);
                deviceScreen.innerHTML = `
                    <div class="text-center p-4">
                        <i class="bi bi-exclamation-triangle fs-1 text-warning mb-3"></i>
                        <p class="text-muted">Failed to load screenshot</p>
                        <button class="btn btn-outline-primary btn-sm" onclick="refreshScreenshot()">
                            <i class="bi bi-arrow-clockwise me-1"></i>Try Again
                        </button>
                    </div>
                `;
            });
        }

        function clearDeviceScreen() {
            const deviceScreen = document.getElementById('device-screen');
            deviceScreen.innerHTML = `
                <div class="text-center p-4" style="min-height: 400px; background-color: #f8f9fa;">
                    <i class="bi bi-phone fs-1 text-muted mb-3"></i>
                    <p class="text-muted">Connect to a device to view screen</p>
                </div>
            `;
        }

        function handleDeviceClick(event, imgElement) {
            if (!currentDeviceId || !isConnected) {
                logAction('warning', 'Cannot tap on screen', 'No device connected');
                return;
            }
            
            const rect = imgElement.getBoundingClientRect();
            const x = event.clientX - rect.left;
            const y = event.clientY - rect.top;
            
            // Calculate relative coordinates
            const relativeX = x / imgElement.width;
            const relativeY = y / imgElement.height;
            
            executeAction('tap', {
                x: relativeX,
                y: relativeY
            });
        }

        function openWebInspector() {
            if (!currentDeviceId) {
                return;
            }
            
            // Open web inspector in new window
            window.open(`/web_inspector?device_id=${currentDeviceId}`, '_blank');
        }

        function detectElements() {
            if (!currentDeviceId) {
                return;
            }
            
            // Implement element detection functionality
            alert('Element detection will be implemented in the next phase');
        }

        // Action logging functions
        function logAction(type, message, details = null) {
            const timestamp = new Date().toLocaleTimeString();
            const entry = {
                timestamp: timestamp,
                type: type, // 'info', 'success', 'warning', 'error'
                message: message,
                details: details
            };
            
            actionLogEntries.push(entry);
            
            // Update UI
            const actionLog = document.getElementById('action-log');
            const logEntry = document.createElement('div');
            logEntry.className = `mb-1 p-2 rounded action-log-${type}`;
            
            let icon = '';
            let colorClass = '';
            switch(type) {
                case 'success':
                    icon = 'bi-check-circle';
                    colorClass = 'text-success';
                    break;
                case 'error':
                    icon = 'bi-x-circle';
                    colorClass = 'text-danger';
                    break;
                case 'warning':
                    icon = 'bi-exclamation-triangle';
                    colorClass = 'text-warning';
                    break;
                default:
                    icon = 'bi-info-circle';
                    colorClass = 'text-info';
            }
            
            logEntry.innerHTML = `
                <span class="text-muted">[${timestamp}]</span>
                <i class="bi ${icon} ${colorClass} me-1"></i>
                <span class="${colorClass}">${message}</span>
                ${details ? `<div class="text-muted small mt-1">${details}</div>` : ''}
            `;
            
            // Clear initial message if this is the first log
            if (actionLogEntries.length === 1) {
                actionLog.innerHTML = '';
            }
            
            actionLog.appendChild(logEntry);
            actionLog.scrollTop = actionLog.scrollHeight;
        }
        
        function clearActionLog() {
            actionLogEntries = [];
            const actionLog = document.getElementById('action-log');
            actionLog.innerHTML = '<div class="text-muted">Action log cleared...</div>';
        }
        
        function exportActionLog() {
            if (actionLogEntries.length === 0) {
                logAction('warning', 'No actions to export');
                return;
            }
            
            const logData = actionLogEntries.map(entry => 
                `[${entry.timestamp}] ${entry.type.toUpperCase()}: ${entry.message}${entry.details ? ' - ' + entry.details : ''}`
            ).join('\n');
            
            const blob = new Blob([logData], { type: 'text/plain' });
            const url = URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `ios_action_log_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.txt`;
            document.body.appendChild(a);
            a.click();
            document.body.removeChild(a);
            URL.revokeObjectURL(url);
            
            logAction('success', 'Action log exported successfully');
        }
        
        function executeAction(actionType, actionData = {}) {
            if (!currentDeviceId) {
                logAction('error', 'No device connected', 'Please connect to a device first');
                return Promise.reject('No device connected');
            }
            
            logAction('info', `Executing ${actionType} action`, JSON.stringify(actionData));
            
            const requestData = {
                action_type: actionType,
                device_id: currentDeviceId,
                ...actionData
            };
            
            return fetch('/api/action/execute', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    ...getSessionHeaders()
                },
                body: JSON.stringify(requestData)
            })
            .then(response => {
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                return response.json();
            })
            .then(data => {
                if (data.success) {
                    logAction('success', `${actionType} action completed`, data.message || 'Action executed successfully');
                    
                    // Refresh screenshot after action
                    if (actionType !== 'screenshot') {
                        setTimeout(() => refreshScreenshot(), 1000);
                    }
                } else {
                    logAction('error', `${actionType} action failed`, data.error || 'Unknown error');
                }
                return data;
            })
            .catch(error => {
                logAction('error', `${actionType} action error`, error.message);
                throw error;
            });
        }
        
        // Enable device selection
        function onDeviceSelectionChange() {
            const deviceSelect = document.getElementById('device-select');
            const connectBtn = document.getElementById('connect-btn');
            
            if (deviceSelect.value && !isConnected) {
                connectBtn.disabled = false;
            } else {
                connectBtn.disabled = true;
            }
        }

        // Test case functions
        function createNewTestCase() {
            alert('Test case creation will be implemented in the next phase');
        }

        // Test suite functions
        function createNewTestSuite() {
            alert('Test suite creation will be implemented in the next phase');
        }



        // Logout function
        function logout() {
            const returnUrl = '{{ return_url or "" }}';

            // Clear any local session data
            if (typeof(Storage) !== "undefined") {
                localStorage.removeItem('ios_app_session');
                sessionStorage.clear();
            }

            if (returnUrl) {
                // SaaS mode - redirect back to SaaS platform
                window.location.href = returnUrl;
            } else {
                // Redirect back to dashboard
                window.location.href = 'http://localhost:8080/dashboard';
            }
        }

        // Initialize page
        document.addEventListener('DOMContentLoaded', function() {
            console.log('iOS App Automation Tool loaded');
            
            // Initialize session
            initializeSession();
            
            // Initialize UI state
            updateUIForConnectedState(false);
            clearDeviceScreen();
            
            // Welcome message
            logAction('info', 'iOS App Automation Tool initialized', 'Ready to connect to devices');
            
            // Load initial data
            refreshDevices();
        });
        
        // Add some common action shortcuts
        function takeScreenshot() {
            executeAction('screenshot');
        }
        
        function goHome() {
            executeAction('home');
        }
        
        function lockDevice() {
            executeAction('lock');
        }
        
        function unlockDevice() {
            executeAction('unlock');
        }
    </script>
</body>
</html>