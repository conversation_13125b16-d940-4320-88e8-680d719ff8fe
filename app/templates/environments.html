<div class="container-fluid mt-3">
    <!-- Environment Management Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-primary text-white">
                    <h4 class="mb-0"><i class="bi bi-gear-wide-connected"></i> Environment Management</h4>
                    <small>Manage your test environments and variables. Switch between different environments for testing.</small>
                </div>
                <div class="card-body">
                    <div class="row align-items-center">
                        <div class="col-md-2">
                            <label for="selectedEnvironment" class="form-label fw-bold">Active Environment:</label>
                        </div>
                        <div class="col-md-3">
                            <select class="form-select" id="selectedEnvironment">
                                <option selected disabled>Select Environment</option>
                            </select>
                        </div>
                        <div class="col-md-7">
                            <div class="btn-group" role="group">
                                <button class="btn btn-success" id="saveEnvironment">
                                    <i class="bi bi-check-circle"></i> Set Active
                                </button>
                                <button class="btn btn-primary" id="createNewEnvironmentBtn">
                                    <i class="bi bi-plus-circle"></i> Create New
                                </button>
                                <button class="btn btn-info" id="duplicateEnvironmentBtn">
                                    <i class="bi bi-files"></i> Duplicate
                                </button>
                                <button class="btn btn-warning" id="importEnvironmentBtn">
                                    <i class="bi bi-upload"></i> Import
                                </button>
                                <button class="btn btn-secondary" id="exportEnvironmentBtn">
                                    <i class="bi bi-download"></i> Export
                                </button>
                                <button class="btn btn-danger" id="deleteEnvironmentBtn">
                                    <i class="bi bi-trash"></i> Delete
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Environment Variables Section -->
    <div class="row">
        <div class="col-12">
            <div class="card">
                <div class="card-header bg-light">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h5 class="mb-0">
                                <i class="bi bi-list-ul"></i> Environment Variables
                                <span id="environmentNameDisplay" class="badge bg-primary ms-2">No Environment Selected</span>
                            </h5>
                            <small class="text-muted">Variables can be used in test actions using the format <code>env[variable_name]</code></small>
                        </div>
                        <div>
                            <div class="input-group" style="width: 300px;">
                                <span class="input-group-text"><i class="bi bi-search"></i></span>
                                <input type="text" class="form-control" placeholder="Filter variables" id="filterVariables">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th style="width: 25%">Variable Name</th>
                                    <th style="width: 30%">Initial Value</th>
                                    <th style="width: 30%">Current Value</th>
                                    <th style="width: 15%" class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="environmentVariablesTableBody">
                                <tr>
                                    <td colspan="4" class="text-center text-muted py-4">
                                        <i class="bi bi-database"></i>
                                        <p class="mb-0">Select an environment to view its variables</p>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div class="d-flex justify-content-between align-items-center mt-3">
                        <button class="btn btn-primary" id="addNewVariable" disabled>
                            <i class="bi bi-plus-circle"></i> Add New Variable
                        </button>
                        <div class="text-muted">
                            <small>Double-click any cell to edit inline</small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Environment Modal -->
<div class="modal fade" id="environmentModal" tabindex="-1" aria-labelledby="environmentModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="environmentModalTitle">Add New Environment</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="environmentForm">
                    <input type="hidden" id="environmentId">
                    <div class="mb-3">
                        <label for="environmentName" class="form-label">Environment Name</label>
                        <input type="text" class="form-control" id="environmentName" required>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveEnvironmentBtn">Save Environment</button>
            </div>
        </div>
    </div>
</div>

<!-- Variable Modal -->
<div class="modal fade" id="variableModal" tabindex="-1" aria-labelledby="variableModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="variableModalTitle">Add New Variable</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <form id="variableForm">
                    <input type="hidden" id="variableId">
                    <div class="mb-3">
                        <label for="variableName" class="form-label">Variable Name</label>
                        <input type="text" class="form-control" id="variableName" required>
                    </div>
                    <div class="mb-3">
                        <label for="variableInitialValue" class="form-label">Initial Value</label>
                        <input type="text" class="form-control" id="variableInitialValue">
                    </div>
                    <div class="mb-3">
                        <label for="variableCurrentValue" class="form-label">Current Value</label>
                        <input type="text" class="form-control" id="variableCurrentValue">
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-primary" id="saveVariableBtn">Save Variable</button>
            </div>
        </div>
    </div>
</div>

<!-- Delete Confirmation Modal -->
<div class="modal fade" id="deleteConfirmModal" tabindex="-1" aria-labelledby="deleteConfirmModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="deleteConfirmModalTitle">Confirm Delete</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                <p id="deleteConfirmMessage">Are you sure you want to delete this item?</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteBtn">Delete</button>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Environment Management Script -->
<script type="text/javascript">
document.addEventListener('DOMContentLoaded', function () {
    const selectedEnvironmentDropdown = document.getElementById('selectedEnvironment');
    const environmentNameDisplay = document.getElementById('environmentNameDisplay');
    const environmentVariablesTableBody = document.getElementById('environmentVariablesTableBody');
    const filterInput = document.getElementById('filterVariables');
    const addNewVariableButton = document.getElementById('addNewVariable');
    const createNewEnvironmentButton = document.getElementById('createNewEnvironmentBtn');
    const deleteEnvironmentBtn = document.getElementById('deleteEnvironmentBtn');
    const duplicateEnvironmentBtn = document.getElementById('duplicateEnvironmentBtn');
    const saveEnvironmentBtn = document.getElementById('saveEnvironment');
    const importEnvironmentBtn = document.getElementById('importEnvironmentBtn');
    const exportEnvironmentBtn = document.getElementById('exportEnvironmentBtn');

    // Set initial state of buttons
    deleteEnvironmentBtn.disabled = true;
    duplicateEnvironmentBtn.disabled = true;
    addNewVariableButton.disabled = true;

    let currentSelectedEnvId = null; // Currently selected in UI (temporary until saved)
    let activeEnvironmentId = null; // The actually active/saved environment
    let environmentsData = []; // To store fetched environments
    let editingCell = null; // Track currently editing cell

    console.log('Environment Management loaded');

    // --- Toast Notification Helper ---
    function showAppToast(message, type = 'info') {
        if (window.showToast) {
            window.showToast(message, type);
        } else {
            console.log(`Toast (${type}): ${message}`);
            // Create a simple toast if showToast is not available
            const toast = document.createElement('div');
            toast.className = `alert alert-${type === 'error' ? 'danger' : type === 'success' ? 'success' : 'info'} position-fixed`;
            toast.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
            toast.innerHTML = `${message} <button type="button" class="btn-close" onclick="this.parentElement.remove()"></button>`;
            document.body.appendChild(toast);
            setTimeout(() => toast.remove(), 5000);
        }
    }

    // --- API Helper ---
    async function apiCall(url, method = 'GET', body = null) {
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
            },
        };
        if (body) {
            options.body = JSON.stringify(body);
        }
        try {
            const response = await fetch(url, options);
            const responseData = await response.json();
            if (!response.ok) {
                const errorMsg = responseData.error || `API Error: ${response.status}`;
                showAppToast(errorMsg, 'error');
                console.error('API Error:', response.status, responseData);
                return { success: false, data: responseData, status: response.status };
            }
            return { success: true, data: responseData, status: response.status };
        } catch (error) {
            showAppToast('Network or server error: ' + error.message, 'error');
            console.error('Fetch Error:', error);
            return { success: false, error: error.message, data: { error: error.message} };
        }
    }

    // --- Session Storage Helper ---
    function saveActiveEnvironmentToSession(envId) {
        if (envId) {
            sessionStorage.setItem('activeEnvironmentId', envId);
            console.log('Saved active environment to session storage:', envId);
        } else {
            sessionStorage.removeItem('activeEnvironmentId');
            console.log('Cleared active environment from session storage');
        }
    }

    function getActiveEnvironmentFromSession() {
        const envId = sessionStorage.getItem('activeEnvironmentId');
        return envId ? parseInt(envId) : null;
    }

    // --- Environment Management ---
    async function loadEnvironments() {
        const result = await apiCall('/api/environments');
        if (result.success) {
            environmentsData = result.data;

            // Get the currently active environment from the server
            const currentResult = await apiCall('/api/environments/current', 'GET');
            console.log('Current environment response:', currentResult);

            let activeEnvFound = false;

            if (currentResult.success) {
                if (currentResult.data && currentResult.data.id) {
                    activeEnvironmentId = parseInt(currentResult.data.id);
                    console.log('Setting active environment from server:', activeEnvironmentId);
                    activeEnvFound = true;
                }
            }

            // If no active environment from server, try session storage
            if (!activeEnvFound) {
                const sessionEnvId = getActiveEnvironmentFromSession();
                if (sessionEnvId && environmentsData.some(env => parseInt(env.id) === sessionEnvId)) {
                    console.log('Setting active environment from session storage:', sessionEnvId);
                    activeEnvironmentId = sessionEnvId;
                    activeEnvFound = true;
                }
            }

            // Populate environment dropdown
            populateEnvironmentDropdown(environmentsData);

            // Load the active environment or default to first one
            if (activeEnvFound) {
                await selectEnvironmentUI(activeEnvironmentId, false);
            } else if (environmentsData.length > 0) {
                await selectEnvironmentUI(environmentsData[0].id, false);
            } else {
                showNoEnvironmentState();
            }
        } else {
            showErrorState();
        }
    }

    function populateEnvironmentDropdown(environments) {
        selectedEnvironmentDropdown.innerHTML = '<option value="" disabled>Select Environment</option>';

        console.log('Populating dropdown with active environment:', activeEnvironmentId);

        environments.forEach(env => {
            const option = document.createElement('option');
            option.value = env.id;
            option.textContent = env.name;
            if (parseInt(env.id) === activeEnvironmentId) {
                option.selected = true;
            }
            selectedEnvironmentDropdown.appendChild(option);
        });
    }

    function showNoEnvironmentState() {
        environmentNameDisplay.textContent = 'No Environments';
        environmentNameDisplay.className = 'badge bg-secondary ms-2';
        environmentVariablesTableBody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center text-muted py-4">
                    <i class="bi bi-folder-x"></i>
                    <p class="mb-0">No environments created yet. Click "Create New" to add one.</p>
                </td>
            </tr>`;
        selectedEnvironmentDropdown.innerHTML = '<option value="" disabled>No Environments</option>';
        addNewVariableButton.disabled = true;
    }

    function showErrorState() {
        environmentNameDisplay.textContent = 'Error Loading';
        environmentNameDisplay.className = 'badge bg-danger ms-2';
        environmentVariablesTableBody.innerHTML = `
            <tr>
                <td colspan="4" class="text-center text-danger py-4">
                    <i class="bi bi-exclamation-triangle"></i>
                    <p class="mb-0">Error loading environments. Please refresh the page.</p>
                </td>
            </tr>`;
    }

    // --- Event Listeners ---
    selectedEnvironmentDropdown.addEventListener('change', async function() {
        const envId = this.value ? parseInt(this.value) : null;
        if (envId) {
            await selectEnvironmentUI(envId, false);
        } else {
            currentSelectedEnvId = null;
            environmentNameDisplay.textContent = 'Select Environment';
            environmentNameDisplay.className = 'badge bg-secondary ms-2';
            environmentVariablesTableBody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center text-muted py-4">
                        <i class="bi bi-database"></i>
                        <p class="mb-0">Select an environment to view its variables</p>
                    </td>
                </tr>`;
            deleteEnvironmentBtn.disabled = true;
            duplicateEnvironmentBtn.disabled = true;
            addNewVariableButton.disabled = true;
        }
    });

    saveEnvironmentBtn.addEventListener('click', async function() {
        if (!currentSelectedEnvId) {
            showAppToast('Please select an environment first.', 'info');
            return;
        }

        const envId = parseInt(currentSelectedEnvId);
        const result = await apiCall('/api/environments/current', 'POST', { environment_id: envId });
        if (result.success) {
            activeEnvironmentId = envId;
            const envName = environmentsData.find(env => parseInt(env.id) === envId)?.name || 'Selected environment';
            showAppToast(`Environment '${envName}' is now active.`, 'success');
            saveActiveEnvironmentToSession(envId);
            updateActiveEnvironmentDisplay();
        }
    });

    createNewEnvironmentButton.addEventListener('click', async () => {
        showAddEnvironmentModal();
    });

    deleteEnvironmentBtn.addEventListener('click', async () => {
        if (!currentSelectedEnvId) return;

        const envName = environmentsData.find(env => parseInt(env.id) === currentSelectedEnvId)?.name;
        showDeleteConfirmModal(`Are you sure you want to delete environment "${envName}"? This action cannot be undone.`, async () => {
            const result = await apiCall(`/api/environments/${currentSelectedEnvId}`, 'DELETE');
            if (result.success) {
                showAppToast(`Environment '${envName}' deleted.`, 'success');
                await loadEnvironments();
            }
        });
    });

    // Export Environment
    exportEnvironmentBtn.addEventListener('click', async () => {
        if (!currentSelectedEnvId) {
            showAppToast('Please select an environment to export.', 'info');
            return;
        }

        try {
            const result = await apiCall(`/api/environments/${currentSelectedEnvId}/export`);
            if (result.success) {
                const exportData = result.data;
                const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
                const url = URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = `environment_${exportData.name}_${new Date().toISOString().split('T')[0]}.json`;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                URL.revokeObjectURL(url);
                showAppToast('Environment exported successfully!', 'success');
            }
        } catch (error) {
            console.error('Export error:', error);
            showAppToast('Failed to export environment: ' + error.message, 'error');
        }
    });

    // Import Environment
    importEnvironmentBtn.addEventListener('click', () => {
        const input = document.createElement('input');
        input.type = 'file';
        input.accept = '.json';
        input.onchange = async (event) => {
            const file = event.target.files[0];
            if (!file) return;

            try {
                const text = await file.text();
                const importData = JSON.parse(text);

                // Validate import data structure
                if (!importData.name || !Array.isArray(importData.variables)) {
                    showAppToast('Invalid import file format. Expected environment data with name and variables.', 'error');
                    return;
                }

                const result = await apiCall('/api/environments/import', 'POST', importData);
                if (result.success) {
                    const message = result.data?.message || `Environment '${importData.name}' imported successfully!`;
                    showAppToast(message, 'success');
                    await loadEnvironments();
                    // Select the imported environment - look for the actual imported name (might have "Copy" suffix)
                    const importedEnvId = result.data?.environment?.id;
                    if (importedEnvId) {
                        currentSelectedEnvId = parseInt(importedEnvId);
                        updateEnvironmentSelection();
                        await loadEnvironmentVariables(currentSelectedEnvId);
                    } else {
                        // Fallback: try to find by name
                        const importedEnv = environmentsData.find(env => env.name.includes(importData.name));
                        if (importedEnv) {
                            currentSelectedEnvId = parseInt(importedEnv.id);
                            updateEnvironmentSelection();
                            await loadEnvironmentVariables(currentSelectedEnvId);
                        }
                    }
                }
            } catch (error) {
                console.error('Import error:', error);
                if (error instanceof SyntaxError) {
                    showAppToast('Invalid JSON file format.', 'error');
                } else {
                    showAppToast('Failed to import environment: ' + error.message, 'error');
                }
            }
        };
        input.click();
    });

    // --- Core Functions ---
    async function selectEnvironmentUI(envId, saveToServer = false) {
        envId = parseInt(envId);
        console.log('Selecting environment UI:', envId, 'Current active:', activeEnvironmentId);

        currentSelectedEnvId = envId;
        const selectedEnv = environmentsData.find(env => parseInt(env.id) === envId);

        deleteEnvironmentBtn.disabled = !envId;
        duplicateEnvironmentBtn.disabled = !envId;
        addNewVariableButton.disabled = !envId;

        if (selectedEnv) {
            environmentNameDisplay.textContent = selectedEnv.name;
            environmentNameDisplay.className = parseInt(envId) === activeEnvironmentId ?
                'badge bg-success ms-2' : 'badge bg-primary ms-2';
            selectedEnvironmentDropdown.value = envId;

            await loadEnvironmentVariables(envId);

            if (saveToServer) {
                const setResult = await apiCall('/api/environments/current', 'POST', { environment_id: envId });
                if (setResult.success) {
                    activeEnvironmentId = envId;
                    showAppToast(`Environment '${selectedEnv.name}' is now active.`, 'success');
                    updateActiveEnvironmentDisplay();
                } else {
                    showAppToast(`Failed to set '${selectedEnv.name}' as active on server.`, 'error');
                }
            }
        } else {
            currentSelectedEnvId = null;
            environmentNameDisplay.textContent = 'Select Environment';
            environmentNameDisplay.className = 'badge bg-secondary ms-2';
            environmentVariablesTableBody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center text-muted py-4">
                        <i class="bi bi-database"></i>
                        <p class="mb-0">Select an environment to view its variables</p>
                    </td>
                </tr>`;
            selectedEnvironmentDropdown.value = '';
        }
        applyFilter();
    }

    function updateActiveEnvironmentDisplay() {
        if (currentSelectedEnvId === activeEnvironmentId) {
            environmentNameDisplay.className = 'badge bg-success ms-2';
        }
    }

    // --- Variable Management ---
    async function loadEnvironmentVariables(envId) {
        if (!envId) {
            environmentVariablesTableBody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center text-muted py-4">
                        <i class="bi bi-database"></i>
                        <p class="mb-0">No environment selected</p>
                    </td>
                </tr>`;
            return;
        }

        const result = await apiCall(`/api/environments/${envId}/variables`);
        environmentVariablesTableBody.innerHTML = '';

        if (result.success && result.data) {
            if (result.data.length === 0) {
                environmentVariablesTableBody.innerHTML = `
                    <tr>
                        <td colspan="4" class="text-center text-muted py-4">
                            <i class="bi bi-plus-circle"></i>
                            <p class="mb-0">No variables defined. Click "Add New Variable" to create one.</p>
                        </td>
                    </tr>`;
            } else {
                result.data.forEach(variable => renderVariableRow(variable));
            }
        } else {
            environmentVariablesTableBody.innerHTML = `
                <tr>
                    <td colspan="4" class="text-center text-danger py-4">
                        <i class="bi bi-exclamation-triangle"></i>
                        <p class="mb-0">Error loading variables</p>
                    </td>
                </tr>`;
        }
        applyFilter();
    }

    function renderVariableRow(variable) {
        const row = environmentVariablesTableBody.insertRow();
        row.dataset.variableId = variable.id || '';
        row.innerHTML = `
            <td class="editable-cell" data-field="name">${escapeHtml(variable.name || '')}</td>
            <td class="editable-cell" data-field="initial_value">${escapeHtml(variable.initial_value || '')}</td>
            <td class="editable-cell" data-field="current_value">${escapeHtml(variable.current_value || '')}</td>
            <td class="text-center">
                <div class="btn-group btn-group-sm" role="group">
                    <button type="button" class="btn btn-outline-danger delete-variable-btn" title="Delete Variable">
                        <i class="bi bi-trash"></i>
                    </button>
                </div>
            </td>
        `;

        // Add double-click event listeners for inline editing
        row.querySelectorAll('.editable-cell').forEach(cell => {
            cell.addEventListener('dblclick', () => startInlineEdit(cell, variable));
        });

        // Add delete event listener
        row.querySelector('.delete-variable-btn').addEventListener('click', async () => {
            showDeleteConfirmModal(`Are you sure you want to delete variable "${variable.name}"?`, async () => {
                await deleteVariable(variable.id, row);
            });
        });
    }

    function escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    function startInlineEdit(cell, variable) {
        if (editingCell) {
            cancelInlineEdit(); // Cancel any existing edit
        }

        editingCell = cell;
        const field = cell.dataset.field;
        const currentValue = cell.textContent;

        // Store the original value for cancellation
        cell.dataset.originalValue = currentValue;

        cell.innerHTML = `<input type="text" class="form-control form-control-sm" value="${escapeHtml(currentValue)}" style="min-width: 150px;">`;
        const input = cell.querySelector('input');
        input.focus();
        input.select();

        // Save on Enter or blur
        const saveEdit = async () => {
            const newValue = input.value.trim();
            if (newValue !== currentValue) {
                await saveVariableField(variable.id, field, newValue, cell);
            } else {
                cancelInlineEdit();
            }
        };

        // Cancel on Escape
        const cancelEdit = () => {
            cancelInlineEdit();
        };

        input.addEventListener('blur', saveEdit);
        input.addEventListener('keydown', (e) => {
            if (e.key === 'Enter') {
                e.preventDefault();
                saveEdit();
            } else if (e.key === 'Escape') {
                e.preventDefault();
                cancelEdit();
            }
        });
    }

    function cancelInlineEdit() {
        if (editingCell) {
            const originalValue = editingCell.dataset.originalValue || '';
            editingCell.innerHTML = escapeHtml(originalValue);
            delete editingCell.dataset.originalValue;
            editingCell = null;
        }
    }

    async function saveVariableField(variableId, field, newValue, cell) {
        const updateData = { [field]: newValue };
        const result = await apiCall(`/api/environments/${currentSelectedEnvId}/variables/${variableId}`, 'PATCH', updateData);

        if (result.success) {
            cell.innerHTML = escapeHtml(newValue);
            editingCell = null;
            showAppToast(`Variable ${field} updated successfully.`, 'success');
        } else {
            showAppToast(`Failed to update variable ${field}.`, 'error');
            cancelInlineEdit();
        }
    }

    async function deleteVariable(variableId, row) {
        const result = await apiCall(`/api/environment_variables/${variableId}`, 'DELETE');
        if (result.success) {
            row.remove();
            showAppToast('Variable deleted successfully.', 'success');
            // Check if table is now empty
            if (environmentVariablesTableBody.children.length === 0) {
                environmentVariablesTableBody.innerHTML = `
                    <tr>
                        <td colspan="4" class="text-center text-muted py-4">
                            <i class="bi bi-plus-circle"></i>
                            <p class="mb-0">No variables defined. Click "Add New Variable" to create one.</p>
                        </td>
                    </tr>`;
            }
        } else {
            showAppToast('Failed to delete variable.', 'error');
        }
    }

    // --- Add New Variable ---
    addNewVariableButton.addEventListener('click', async () => {
        if (!currentSelectedEnvId) {
            showAppToast('Please select an environment first.', 'info');
            return;
        }

        showAddVariableModal();
    });

    // --- Filter Function ---
    function applyFilter() {
        const filterValue = filterInput.value.toLowerCase();
        const rows = environmentVariablesTableBody.querySelectorAll('tr');

        rows.forEach(row => {
            if (row.children.length === 1) return; // Skip empty state rows

            const variableName = row.children[0].textContent.toLowerCase();
            const initialValue = row.children[1].textContent.toLowerCase();
            const currentValue = row.children[2].textContent.toLowerCase();

            const matches = variableName.includes(filterValue) ||
                          initialValue.includes(filterValue) ||
                          currentValue.includes(filterValue);

            row.style.display = matches ? '' : 'none';
        });
    }

    filterInput.addEventListener('input', applyFilter);

    // --- Modal Functions ---
    function showAddEnvironmentModal() {
        document.getElementById('environmentModalTitle').textContent = 'Add New Environment';
        document.getElementById('environmentName').value = '';
        document.getElementById('environmentId').value = '';
        const modal = new bootstrap.Modal(document.getElementById('environmentModal'));
        modal.show();
    }

    function showAddVariableModal() {
        document.getElementById('variableModalTitle').textContent = 'Add New Variable';
        document.getElementById('variableName').value = '';
        document.getElementById('variableInitialValue').value = '';
        document.getElementById('variableCurrentValue').value = '';
        document.getElementById('variableId').value = '';
        const modal = new bootstrap.Modal(document.getElementById('variableModal'));
        modal.show();
    }

    function showEditVariableModal(variable) {
        document.getElementById('variableModalTitle').textContent = 'Edit Variable';
        document.getElementById('variableName').value = variable.name;
        document.getElementById('variableInitialValue').value = variable.initial_value;
        document.getElementById('variableCurrentValue').value = variable.current_value;
        document.getElementById('variableId').value = variable.id;
        const modal = new bootstrap.Modal(document.getElementById('variableModal'));
        modal.show();
    }

    function showDeleteConfirmModal(message, onConfirm) {
        document.getElementById('deleteConfirmMessage').textContent = message;
        const modal = new bootstrap.Modal(document.getElementById('deleteConfirmModal'));

        // Remove any existing event listeners
        const confirmBtn = document.getElementById('confirmDeleteBtn');
        const newConfirmBtn = confirmBtn.cloneNode(true);
        confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);

        // Add new event listener
        newConfirmBtn.addEventListener('click', () => {
            modal.hide();
            onConfirm();
        });

        modal.show();
    }

    // --- Modal Event Handlers ---
    document.getElementById('saveEnvironmentBtn').addEventListener('click', async () => {
        const name = document.getElementById('environmentName').value.trim();
        if (!name) {
            showAppToast('Environment name is required.', 'error');
            return;
        }

        const result = await apiCall('/api/environments', 'POST', { name: name });
        if (result.success) {
            showAppToast(`Environment '${name}' created successfully.`, 'success');
            await loadEnvironments();
            if (result.data && result.data.environment && result.data.environment.id) {
                await selectEnvironmentUI(result.data.environment.id, false);
            }
            const modal = bootstrap.Modal.getInstance(document.getElementById('environmentModal'));
            modal.hide();
        }
    });

    document.getElementById('saveVariableBtn').addEventListener('click', async () => {
        const name = document.getElementById('variableName').value.trim();
        const initialValue = document.getElementById('variableInitialValue').value.trim();
        const currentValue = document.getElementById('variableCurrentValue').value.trim();
        const variableId = document.getElementById('variableId').value;

        if (!name) {
            showAppToast('Variable name is required.', 'error');
            return;
        }

        if (!currentSelectedEnvId) {
            showAppToast('Please select an environment first.', 'info');
            return;
        }

        const newVariable = {
            name: name,
            initial_value: initialValue,
            current_value: currentValue || initialValue
        };

        const result = await apiCall(`/api/environments/${currentSelectedEnvId}/variables`, 'POST', newVariable);
        if (result.success) {
            showAppToast(`Variable '${name}' created successfully.`, 'success');
            await loadEnvironmentVariables(currentSelectedEnvId);
            const modal = bootstrap.Modal.getInstance(document.getElementById('variableModal'));
            modal.hide();
        }
    });

    // --- Initialize ---
    loadEnvironments();
});
</script>