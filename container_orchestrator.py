#!/usr/bin/env python3
"""
Container Orchestrator for iOS and Android Application Isolation
Handles dynamic creation, management, and cleanup of isolated container instances
"""

import docker
import redis
import json
import uuid
import time
import logging
import subprocess
import os
from typing import Dict, Optional, List
from dataclasses import dataclass
from threading import Lock
from utils.port_manager import PortManager, PortAllocation

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

@dataclass
class ContainerInstance:
    """Represents an isolated container instance"""
    container_id: str
    session_id: str
    user_id: str
    platform: str  # 'ios' or 'android'
    port: int
    appium_port: int
    created_at: float
    last_accessed: float
    status: str  # 'starting', 'running', 'stopping', 'stopped'

class ContainerOrchestrator:
    """Manages isolated container instances for iOS and Android applications"""
    
    def __init__(self, redis_host='localhost', redis_port=6380, redis_db=1):
        # Use macOS Docker socket path
        import os
        docker_socket = os.path.expanduser('~/.docker/run/docker.sock')
        self.docker_client = docker.DockerClient(base_url=f'unix://{docker_socket}')
        self.redis_client = redis.Redis(host=redis_host, port=redis_port, db=redis_db, decode_responses=True)
        self.instances: Dict[str, ContainerInstance] = {}
        self.port_manager = PortManager()
        self.lock = Lock()
        
        # Load existing instances from Redis
        self._load_instances_from_redis()
        
    def _load_instances_from_redis(self):
        """Load existing instances from Redis on startup"""
        try:
            instance_keys = self.redis_client.keys('instance:*')
            for key in instance_keys:
                instance_data = self.redis_client.hgetall(key)
                if instance_data:
                    session_id = instance_data['session_id']
                    self.instances[session_id] = ContainerInstance(
                        container_id=instance_data['container_id'],
                        session_id=session_id,
                        user_id=instance_data['user_id'],
                        platform=instance_data['platform'],
                        port=int(instance_data['port']),
                        appium_port=int(instance_data['appium_port']),
                        created_at=float(instance_data['created_at']),
                        last_accessed=float(instance_data['last_accessed']),
                        status=instance_data['status']
                    )
        except Exception as e:
            logger.error(f"Error loading instances from Redis: {e}")
    
    def create_instance(self, user_id: str, platform: str) -> Optional[ContainerInstance]:
        """Create a new isolated container instance"""
        with self.lock:
            try:
                session_id = str(uuid.uuid4())
                
                # Get available ports using proper PortManager API
                flask_port = self.port_manager.find_available_port('flask')
                appium_port = self.port_manager.find_available_port('appium')
                
                if not flask_port or not appium_port:
                    logger.error("No available ports for new instance")
                    return None
                
                # Register port allocations
                from datetime import datetime
                now = datetime.now().isoformat()
                
                self.port_manager.allocations[flask_port] = PortAllocation(
                    port=flask_port,
                    service_type='flask',
                    platform=platform,
                    allocated_at=now,
                    last_checked=now,
                    status='allocated'
                )
                
                self.port_manager.allocations[appium_port] = PortAllocation(
                    port=appium_port,
                    service_type='appium',
                    platform=platform,
                    allocated_at=now,
                    last_checked=now,
                    status='allocated'
                )
                
                self.port_manager.save_state()
                
                # Prepare environment variables
                env_vars = {
                    'SESSION_ID': session_id,
                    'USER_ID': user_id,
                    'FLASK_PORT': str(flask_port),
                    'APPIUM_PORT': str(appium_port),
                    'FLASK_ENV': 'production'
                }
                
                if platform == 'android':
                    env_vars['JAVA_HOME'] = '/usr/lib/jvm/default-java'
                
                # Create container
                image_name = f'mobileappautomation-{platform}-template'
                container_name = f'{platform}-{session_id[:8]}'
                
                # Check if image exists locally first
                try:
                    self.docker_client.images.get(image_name)
                except docker.errors.ImageNotFound:
                    logger.error(f"Image {image_name} not found locally. Please build it first.")
                    raise
                
                container = self.docker_client.containers.run(
                    image=image_name,
                    name=container_name,
                    environment=env_vars,
                    ports={
                        str(flask_port): flask_port,
                        str(appium_port): appium_port
                    },
                    # volumes={
                    #     f'{os.getcwd()}/logs': {'bind': '/app/logs', 'mode': 'rw'},
                    #     f'{os.getcwd()}/uploads': {'bind': '/app/uploads', 'mode': 'rw'}
                    # },
                    # privileged=True,
                    detach=True,
                    network='mobileappautomation_isolation-network'
                )
                
                # Create instance object
                instance = ContainerInstance(
                    container_id=container.id,
                    session_id=session_id,
                    user_id=user_id,
                    platform=platform,
                    port=flask_port,
                    appium_port=appium_port,
                    created_at=time.time(),
                    last_accessed=time.time(),
                    status='starting'
                )
                
                # Store in memory and Redis
                self.instances[session_id] = instance
                self._save_instance_to_redis(instance)
                
                # Wait for container to be ready
                self._wait_for_container_ready(instance)
                
                logger.info(f"Created {platform} instance {session_id} for user {user_id}")
                return instance
                
            except Exception as e:
                logger.error(f"Error creating {platform} instance: {e}")
                return None
    
    def _wait_for_container_ready(self, instance: ContainerInstance, timeout: int = 60):
        """Wait for container to be ready and update status"""
        start_time = time.time()
        while time.time() - start_time < timeout:
            try:
                container = self.docker_client.containers.get(instance.container_id)
                if container.status == 'running':
                    # Check if Flask app is responding
                    import requests
                    try:
                        response = requests.get(f'http://localhost:{instance.port}/health', timeout=5)
                        if response.status_code == 200:
                            instance.status = 'running'
                            self._save_instance_to_redis(instance)
                            return
                    except requests.RequestException:
                        pass
            except docker.errors.NotFound:
                instance.status = 'stopped'
                self._save_instance_to_redis(instance)
                return
            
            time.sleep(2)
        
        # Timeout reached
        instance.status = 'failed'
        self._save_instance_to_redis(instance)
    
    def get_instance(self, session_id: str) -> Optional[ContainerInstance]:
        """Get instance by session ID"""
        instance = self.instances.get(session_id)
        if instance:
            instance.last_accessed = time.time()
            self._save_instance_to_redis(instance)
        return instance
    
    def stop_instance(self, session_id: str) -> bool:
        """Stop and remove an instance"""
        with self.lock:
            try:
                instance = self.instances.get(session_id)
                if not instance:
                    return False
                
                # Stop container
                try:
                    container = self.docker_client.containers.get(instance.container_id)
                    container.stop(timeout=10)
                    container.remove()
                except docker.errors.NotFound:
                    pass
                
                # Release ports by removing from allocations
                if instance.port in self.port_manager.allocations:
                    del self.port_manager.allocations[instance.port]
                if instance.appium_port in self.port_manager.allocations:
                    del self.port_manager.allocations[instance.appium_port]
                self.port_manager.save_state()
                
                # Remove from memory and Redis
                del self.instances[session_id]
                self.redis_client.delete(f'instance:{session_id}')
                
                logger.info(f"Stopped instance {session_id}")
                return True
                
            except Exception as e:
                logger.error(f"Error stopping instance {session_id}: {e}")
                return False
    
    def cleanup_inactive_instances(self, max_idle_time: int = 3600):
        """Clean up instances that have been idle for too long"""
        current_time = time.time()
        to_remove = []
        
        for session_id, instance in self.instances.items():
            if current_time - instance.last_accessed > max_idle_time:
                to_remove.append(session_id)
        
        for session_id in to_remove:
            self.stop_instance(session_id)
    
    def _save_instance_to_redis(self, instance: ContainerInstance):
        """Save instance data to Redis"""
        try:
            self.redis_client.hset(
                f'instance:{instance.session_id}',
                mapping={
                    'container_id': instance.container_id,
                    'session_id': instance.session_id,
                    'user_id': instance.user_id,
                    'platform': instance.platform,
                    'port': instance.port,
                    'appium_port': instance.appium_port,
                    'created_at': instance.created_at,
                    'last_accessed': instance.last_accessed,
                    'status': instance.status
                }
            )
        except Exception as e:
            logger.error(f"Error saving instance to Redis: {e}")
    
    def list_instances(self, user_id: Optional[str] = None) -> List[ContainerInstance]:
        """List all instances, optionally filtered by user ID"""
        instances = list(self.instances.values())
        if user_id:
            instances = [i for i in instances if i.user_id == user_id]
        return instances



# Global orchestrator instance
orchestrator = ContainerOrchestrator()

if __name__ == '__main__':
    # Test the orchestrator
    print("Testing Container Orchestrator...")
    
    # Create iOS instance
    ios_instance = orchestrator.create_instance('test_user_1', 'ios')
    if ios_instance:
        print(f"Created iOS instance: {ios_instance.session_id}")
    
    # Create Android instance
    android_instance = orchestrator.create_instance('test_user_2', 'android')
    if android_instance:
        print(f"Created Android instance: {android_instance.session_id}")
    
    # List instances
    instances = orchestrator.list_instances()
    print(f"Total instances: {len(instances)}")
    
    # Cleanup
    time.sleep(5)
    if ios_instance:
        orchestrator.stop_instance(ios_instance.session_id)
    if android_instance:
        orchestrator.stop_instance(android_instance.session_id)
    
    print("Test completed.")