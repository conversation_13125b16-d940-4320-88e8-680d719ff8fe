# Mobile App Auto-Test SaaS Platform

A comprehensive SaaS platform for automated mobile application testing with support for both Android and iOS platforms.

## 🚀 Quick Start

### Prerequisites
- Docker 20.10+
- Docker Compose 2.0+
- Git
- 4GB+ RAM (8GB+ recommended)

### 1. <PERSON>lone and Setup
```bash
git clone <repository-url>
cd MobileApp-AutoTest
```

### 2. Development Deployment
```bash
# Quick development setup with test data
./deploy.sh development

# Or manually with development tools
docker-compose -f docker-compose.yml -f docker-compose.dev.yml up -d
docker-compose -f docker-compose.yml -f docker-compose.dev.yml --profile dev-tools up -d
```

### 3. Production Deployment
```bash
# Automated production deployment
./deploy.sh production

# Or follow the comprehensive guide
# See DEPLOYMENT_GUIDE_COMPREHENSIVE.md
```

### 4. Access the Platform
- **Application**: http://localhost:8080
- **Admin Panel**: Login with admin credentials from `.env`
- **API Documentation**: http://localhost:8080/api/docs (if enabled)

## 📁 Project Structure

```
MobileApp-AutoTest/
├── 🐳 Docker Configuration
│   ├── Dockerfile                     # Production Docker image
│   ├── Dockerfile.dev                 # Development Docker image
│   ├── docker-compose.yml             # Main services configuration
│   ├── docker-compose.dev.yml         # Development overrides
│   └── nginx.conf                     # Nginx reverse proxy config
│
├── 🚀 Deployment
│   ├── deploy.sh                      # Automated deployment script
│   ├── .env.template                  # Environment variables template
│   ├── init.sql                       # Database initialization
│   └── setup_test_user.py             # Test user and data setup
│
├── 📖 Documentation
│   ├── README_SAAS_DEPLOYMENT.md       # This file
│   ├── DEPLOYMENT_GUIDE_COMPREHENSIVE.md # Detailed deployment guide
│   └── SAAS_READINESS_ASSESSMENT.md   # SaaS architecture overview
│
├── 🛠️ Agent Installation
│   ├── install_agent_cross_platform.py # Cross-platform installer
│   ├── install_agent_windows.bat      # Windows-specific installer
│   └── install_agent.sh               # Original Unix installer
│
├── 🖥️ Server Application
│   ├── saas_unified_server.py          # Main SaaS server application
│   ├── local_device_agent.py          # Local device agent
│   └── requirements_saas.txt          # Python dependencies
│
└── 📱 Mobile Testing Components
    ├── app/                           # Android testing utilities
    ├── app_android/                   # Android-specific components
    ├── app_ios/                       # iOS-specific components
    └── saas_infrastructure/           # Additional infrastructure
```

## 🔧 Configuration

### Environment Variables
Copy `.env.template` to `.env` and customize:

```bash
cp .env.template .env
# Edit .env with your specific configuration
```

Key variables:
- `POSTGRES_PASSWORD`: Database password
- `REDIS_PASSWORD`: Redis password
- `SECRET_KEY`: Flask secret key
- `JWT_SECRET_KEY`: JWT signing key
- `ADMIN_EMAIL`: Initial admin email
- `ADMIN_PASSWORD`: Initial admin password

### Development vs Production

| Feature | Development | Production |
|---------|-------------|------------|
| Database | PostgreSQL (dev_password) | PostgreSQL (secure password) |
| Redis | Redis (dev_redis) | Redis (secure password) |
| SSL | Self-signed | Let's Encrypt/Commercial |
| Debug | Enabled | Disabled |
| Hot Reload | Enabled | Disabled |
| Test Data | Auto-created | Manual setup |
| Monitoring Tools | Included | Optional |

## 🏗️ Architecture

### Core Components

1. **SaaS Unified Server** (`saas_unified_server.py`)
   - Flask-based REST API
   - JWT authentication
   - Multi-tenant support
   - WebSocket communication
   - Rate limiting

2. **Database Layer**
   - PostgreSQL for persistent data
   - Redis for caching and sessions
   - Automated migrations

3. **Local Device Agents** (`local_device_agent.py`)
   - Cross-platform agent installation
   - Device management
   - Test execution

4. **Reverse Proxy** (Nginx)
   - SSL termination
   - Load balancing
   - Security headers
   - Rate limiting

### API Endpoints

#### Authentication
- `POST /api/auth/register` - User registration
- `POST /api/auth/login` - User login
- `POST /api/auth/refresh` - Token refresh

#### Test Management
- `GET /api/test-suites` - List test suites
- `POST /api/test-suites` - Create test suite
- `GET /api/test-suites/{id}` - Get test suite
- `PUT /api/test-suites/{id}` - Update test suite
- `DELETE /api/test-suites/{id}` - Delete test suite

#### Device Management
- `GET /api/device-agents` - List device agents
- `POST /api/device-agents` - Register device agent
- `GET /api/device-agents/{id}` - Get device agent
- `PUT /api/device-agents/{id}` - Update device agent

#### System
- `GET /health` - Health check
- `GET /api/status` - System status

## 🔐 Security Features

### Authentication & Authorization
- JWT-based authentication
- Role-based access control (Admin/User)
- Multi-tenant isolation
- Session management

### Security Headers
- HTTPS enforcement
- HSTS (HTTP Strict Transport Security)
- CSP (Content Security Policy)
- X-Frame-Options
- X-Content-Type-Options

### Rate Limiting
- API endpoint rate limiting
- Authentication attempt limiting
- DDoS protection

### Data Protection
- Password hashing (bcrypt)
- Secure session storage
- Input validation
- SQL injection prevention

## 🛠️ Development

### Local Development Setup

1. **Start Development Environment**
   ```bash
   # Start with development tools
   docker-compose -f docker-compose.yml -f docker-compose.dev.yml --profile dev-tools up -d
   ```

2. **Access Development Tools**
   - **Application**: http://localhost:8080
   - **pgAdmin**: http://localhost:5050 (<EMAIL> / admin123)
   - **Redis Commander**: http://localhost:8081
   - **Mailhog**: http://localhost:8025

3. **Test User Credentials**
   - **Email**: <EMAIL>
   - **Password**: testpass123
   - **Admin**: <EMAIL> / demoadmin123

### Code Changes
With development setup, code changes are automatically reloaded:
```bash
# Edit files in your IDE
# Changes are reflected immediately
```

### Running Tests
```bash
# Run inside the container
docker-compose exec saas_server python -m pytest

# Or install dependencies locally and run
pip install -r requirements_saas.txt
python -m pytest
```

### Database Management
```bash
# Access database
docker-compose exec postgres psql -U saas_user -d saas_platform_dev

# Run migrations
docker-compose exec saas_server python -c "from saas_unified_server import db; db.create_all()"

# Create test data
docker-compose exec saas_server python setup_test_user.py
```

## 🚀 Production Deployment

### Server Requirements
- **Minimum**: 4GB RAM, 2 CPU cores, 50GB storage
- **Recommended**: 8GB+ RAM, 4+ CPU cores, 100GB+ SSD
- **High Load**: 32GB RAM, 8+ CPU cores, 500GB+ SSD

### Deployment Steps

1. **Server Preparation**
   ```bash
   # Update system
   sudo apt update && sudo apt upgrade -y
   
   # Install Docker
   curl -fsSL https://get.docker.com -o get-docker.sh
   sudo sh get-docker.sh
   
   # Install Docker Compose
   sudo curl -L "https://github.com/docker/compose/releases/latest/download/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose
   ```

2. **Application Deployment**
   ```bash
   # Clone repository
   git clone <repository-url>
   cd MobileApp-AutoTest
   
   # Run automated deployment
   ./deploy.sh production
   ```

3. **SSL Certificate Setup**
   ```bash
   # Using Let's Encrypt
   sudo apt install certbot
   sudo certbot certonly --standalone -d yourdomain.com
   
   # Copy certificates
   sudo cp /etc/letsencrypt/live/yourdomain.com/fullchain.pem ssl/cert.pem
   sudo cp /etc/letsencrypt/live/yourdomain.com/privkey.pem ssl/key.pem
   sudo chown $USER:$USER ssl/*.pem
   ```

4. **Start Production Services**
   ```bash
   # Start with Nginx
   docker-compose --profile production up -d
   ```

### Monitoring

```bash
# View logs
docker-compose logs -f saas_server

# Monitor resources
docker stats

# Check health
curl https://yourdomain.com/health
```

## 📊 Monitoring & Maintenance

### Health Checks
- Application: `GET /health`
- Database: `docker-compose exec postgres pg_isready`
- Redis: `docker-compose exec redis redis-cli ping`

### Backup
```bash
# Database backup
docker-compose exec postgres pg_dump -U saas_user saas_platform > backup.sql

# Full backup
./backup-db.sh  # Created by deployment script
```

### Updates
```bash
# Update application
git pull origin main
docker-compose build --no-cache
docker-compose up -d

# Update system
sudo apt update && sudo apt upgrade -y
```

### Log Management
```bash
# View application logs
docker-compose logs saas_server

# View Nginx logs
docker-compose logs nginx

# Export logs
docker-compose logs saas_server > app.log
```

## 🔧 Agent Installation

### Cross-Platform Agent
```bash
# Python-based installer (recommended)
python install_agent_cross_platform.py

# Windows batch installer
install_agent_windows.bat

# Unix shell installer
./install_agent.sh
```

### Agent Configuration
After installation, configure the agent:
```json
{
  "server_url": "https://yourdomain.com",
  "agent_token": "your-agent-token",
  "platform": "android|ios|both",
  "capabilities": {
    "platformName": "Android",
    "platformVersion": "11.0",
    "deviceName": "Test Device"
  }
}
```

## 🐛 Troubleshooting

### Common Issues

1. **Database Connection Failed**
   ```bash
   # Check database status
   docker-compose ps postgres
   docker-compose logs postgres
   ```

2. **Application Won't Start**
   ```bash
   # Check application logs
   docker-compose logs saas_server
   
   # Restart services
   docker-compose restart
   ```

3. **SSL Certificate Issues**
   ```bash
   # Check certificate
   openssl x509 -in ssl/cert.pem -text -noout
   
   # Test SSL
   openssl s_client -connect yourdomain.com:443
   ```

4. **Performance Issues**
   ```bash
   # Monitor resources
   docker stats
   
   # Check database performance
   docker-compose exec postgres psql -U saas_user -d saas_platform -c "SELECT * FROM pg_stat_activity;"
   ```

### Debug Mode
```bash
# Enable debug mode
echo "DEBUG=true" >> .env
echo "FLASK_ENV=development" >> .env
docker-compose restart saas_server
```

### Getting Help
- Check logs: `docker-compose logs`
- Review configuration: `cat .env`
- Test connectivity: `curl http://localhost:8080/health`
- Database access: `docker-compose exec postgres psql -U saas_user`

## 📚 Additional Resources

- [Comprehensive Deployment Guide](DEPLOYMENT_GUIDE_COMPREHENSIVE.md)
- [SaaS Architecture Overview](SAAS_READINESS_ASSESSMENT.md)
- [Docker Documentation](https://docs.docker.com/)
- [Flask Documentation](https://flask.palletsprojects.com/)
- [PostgreSQL Documentation](https://www.postgresql.org/docs/)

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test thoroughly
5. Submit a pull request

## 📄 License

[Add your license information here]

---

**Note**: This platform is designed for production use. Always follow security best practices, use strong passwords, keep systems updated, and monitor your deployment regularly.