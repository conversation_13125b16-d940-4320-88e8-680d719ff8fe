# Dockerfile for Device Testing Dashboard
# Provides a unified interface for monitoring device connectivity and testing
FROM python:3.10-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV DASHBOARD_PORT=8090
ENV FLASK_ENV=production

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js for frontend build tools
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - && \
    apt-get install -y nodejs

# Create app directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Install additional dashboard-specific packages
RUN pip install --no-cache-dir \
    flask-socketio \
    eventlet \
    requests \
    websockets \
    plotly \
    dash \
    dash-bootstrap-components

# Copy dashboard application code
COPY dashboard/ ./dashboard/
COPY utils/ ./utils/
COPY config.py .
COPY static/ ./static/
COPY templates/ ./templates/

# Create necessary directories
RUN mkdir -p logs temp static/css static/js

# Copy dashboard-specific files
COPY dashboard_app.py .

# Install frontend dependencies and build
COPY package.json package-lock.json ./
RUN npm install
RUN npm run build:dashboard

# Expose port
EXPOSE 8090

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=15s --retries=3 \
    CMD curl -f http://localhost:${DASHBOARD_PORT:-8090}/health || exit 1

# Start the dashboard
CMD ["python", "dashboard_app.py", "--port", "8090"]