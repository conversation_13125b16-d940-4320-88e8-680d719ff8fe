# Development Dockerfile for SaaS server with hot reload
FROM python:3.9-slim

# Set environment variables
ENV PYTHONDONTWRITEBYTECODE=1 \
    PYTHONUNBUFFERED=1 \
    DEBIAN_FRONTEND=noninteractive \
    FLASK_ENV=development \
    DEBUG=1

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    postgresql-client \
    redis-tools \
    vim \
    htop \
    && rm -rf /var/lib/apt/lists/*

# Create app user (but we'll run as root in dev for easier debugging)
RUN groupadd -r appuser && useradd -r -g appuser appuser

# Set work directory
WORKDIR /app

# Install Python dependencies
COPY requirements_saas.txt .
RUN pip install --no-cache-dir -r requirements_saas.txt

# Install development dependencies
RUN pip install --no-cache-dir \
    flask-debugtoolbar \
    watchdog \
    pytest \
    pytest-flask \
    pytest-cov \
    black \
    flake8 \
    ipython

# Create necessary directories
RUN mkdir -p /app/uploads /app/logs && \
    chown -R appuser:appuser /app

# Copy application code (this will be overridden by volume mount in dev)
COPY . .

# Make scripts executable
RUN chmod +x setup_test_user.py deploy.sh

# Expose port
EXPOSE 8080

# Health check
HEALTHCHECK --interval=30s --timeout=30s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:8080/health || exit 1

# Development command with auto-reload
CMD ["python", "-u", "saas_unified_server.py"]