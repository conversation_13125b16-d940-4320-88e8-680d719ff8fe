version: '3.8'

# Development override for docker-compose.yml
# Usage: docker-compose -f docker-compose.yml -f docker-compose.dev.yml up

services:
  # PostgreSQL Database (Development)
  postgres:
    environment:
      POSTGRES_DB: saas_platform_dev
      POSTGRES_USER: saas_user
      POSTGRES_PASSWORD: dev_password_123
    ports:
      - "5433:5432"  # Different port to avoid conflicts
    volumes:
      - postgres_dev_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    
  # Redis Cache (Development)
  redis:
    command: redis-server --appendonly yes --requirepass dev_redis_123
    ports:
      - "6380:6379"  # Different port to avoid conflicts
    volumes:
      - redis_dev_data:/data
    
  # SaaS Application Server (Development)
  saas_server:
    build:
      context: .
      dockerfile: Dockerfile.dev
    environment:
      # Database Configuration
      DATABASE_URL: *****************************************************/saas_platform_dev
      
      # Redis Configuration
      REDIS_URL: redis://:dev_redis_123@redis:6379/0
      
      # Development Security Configuration
      SECRET_KEY: dev-secret-key-not-for-production
      JWT_SECRET_KEY: dev-jwt-secret-key-not-for-production
      
      # Server Configuration
      SERVER_HOST: 0.0.0.0
      SERVER_PORT: 8080
      FLASK_ENV: development
      DEBUG: "true"
      
      # File Upload Configuration
      UPLOAD_FOLDER: /app/uploads
      
      # Test User Configuration
      CREATE_TEST_USER: "true"
      TEST_USER_EMAIL: <EMAIL>
      TEST_USER_PASSWORD: testpass123
      
      # Admin User (for development)
      ADMIN_EMAIL: <EMAIL>
      ADMIN_PASSWORD: admin123
    volumes:
      - .:/app  # Mount source code for hot reload
      - app_dev_uploads:/app/uploads
      - app_dev_logs:/app/logs
    ports:
      - "8080:8080"
    command: >
      sh -c "python setup_test_user.py && 
             python saas_unified_server.py"
    
  # Development Database Admin (pgAdmin)
  pgadmin:
    image: dpage/pgadmin4:latest
    container_name: saas_pgadmin_dev
    environment:
      PGADMIN_DEFAULT_EMAIL: <EMAIL>
      PGADMIN_DEFAULT_PASSWORD: admin123
      PGADMIN_CONFIG_SERVER_MODE: 'False'
    volumes:
      - pgadmin_dev_data:/var/lib/pgadmin
    ports:
      - "5050:80"
    networks:
      - saas_network
    depends_on:
      - postgres
    profiles:
      - dev-tools
    
  # Redis Commander (Redis GUI)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: saas_redis_commander_dev
    environment:
      REDIS_HOSTS: local:redis:6379:0:dev_redis_123
    ports:
      - "8081:8081"
    networks:
      - saas_network
    depends_on:
      - redis
    profiles:
      - dev-tools
    
  # Android Application Service (Development)
  android_app:
    build:
      context: .
      dockerfile: Dockerfile.android
    container_name: android_automation_app_dev
    environment:
      FLASK_ENV: development
      FLASK_PORT: 8083
      APPIUM_PORT: 4726
      DEBUG: "true"
      DATABASE_URL: *****************************************************/saas_platform_dev
      REDIS_URL: redis://:dev_redis_123@redis:6379/0
    volumes:
      - ./app_android:/app/app_android  # Mount source code for hot reload
      - ./utils:/app/utils
      - ./config.py:/app/config.py
      - app_dev_uploads:/app/uploads
      - app_dev_logs:/app/logs
      - /dev/bus/usb:/dev/bus/usb  # USB device access for Android devices
    ports:
      - "8083:8083"  # Flask app
      - "4726:4726"  # Appium server
    networks:
      - saas_network
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    privileged: true  # Required for USB device access
    devices:
      - /dev/bus/usb:/dev/bus/usb  # USB device mapping
    command: >
      sh -c "python run_android.py --port 8083 --appium-port 4726"

  # iOS Application Service (Development)
  ios_app:
    build:
      context: .
      dockerfile: Dockerfile.ios
    container_name: ios_automation_app_dev
    environment:
      FLASK_ENV: development
      FLASK_PORT: 8088
      APPIUM_PORT: 4723
      DEBUG: "true"
      DATABASE_URL: *****************************************************/saas_platform_dev
      REDIS_URL: redis://:dev_redis_123@redis:6379/0
    volumes:
      - ./app:/app/app  # Mount source code for hot reload
      - ./utils:/app/utils
      - ./config.py:/app/config.py
      - app_dev_uploads:/app/uploads
      - app_dev_logs:/app/logs
    ports:
      - "8088:8088"  # Flask app
      - "4723:4723"  # Appium server
    networks:
      - saas_network
    depends_on:
      - postgres
      - redis
    restart: unless-stopped
    command: >
      sh -c "python run.py --flask-port 8088"

  # Mailhog (Email testing)
  mailhog:
    image: mailhog/mailhog:latest
    container_name: saas_mailhog_dev
    ports:
      - "1025:1025"  # SMTP server
      - "8025:8025"  # Web UI
    networks:
      - saas_network
    profiles:
      - dev-tools

volumes:
  postgres_dev_data:
    driver: local
  redis_dev_data:
    driver: local
  app_dev_uploads:
    driver: local
  app_dev_logs:
    driver: local
  pgadmin_dev_data:
    driver: local

networks:
  saas_network:
    driver: bridge