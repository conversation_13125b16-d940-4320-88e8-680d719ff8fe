#!/usr/bin/env python3
"""
Test Final GUI Fixes

This script tests the fixes for:
1. Duplicate GUI windows
2. Blank window issue
3. Proper window visibility and sizing
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
from pathlib import Path
import time

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

# Suppress tkinter deprecation warning
os.environ['TK_SILENCE_DEPRECATION'] = '1'

# Load environment
def load_environment():
    """Load environment variables from .env file"""
    try:
        env_file = Path('.env')
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            print("✅ Environment loaded")
    except Exception as e:
        print(f"❌ Failed to load .env: {e}")

load_environment()

def test_single_root_window():
    """Test that only one root window is created"""
    print("🔍 Testing Single Root Window...")
    
    try:
        # Count existing Tk instances before
        initial_count = len(tk._default_root.children) if tk._default_root else 0
        
        # Create main application components
        from gui.login_window import LoginWindow
        from gui.system_tray import SystemTrayManager, is_system_tray_available
        
        # Create single root window
        root = tk.Tk()
        root.title("Test Root")
        root.geometry("200x100")
        root.withdraw()  # Hide root
        
        print("✅ Single root window created")
        
        # Mock session manager
        class MockSessionManager:
            def __init__(self):
                self.supabase = None
            
            def login(self, email, password, license):
                return {"success": True, "user_data": {"email": email}}
            
            def register(self, email, password, first_name, last_name, license_number):
                return {"success": True, "user_data": {"email": email}}
        
        session_manager = MockSessionManager()
        
        # Create system tray with parent root
        if is_system_tray_available():
            system_tray = SystemTrayManager(
                session_manager=session_manager,
                app_launcher=None,
                parent_root=root  # Pass the root window
            )
            print("✅ SystemTray created with parent root")
        
        # Create login window
        def on_login_success(user_data):
            print(f"Login success: {user_data}")
        
        def on_register_success(user_data):
            print(f"Register success: {user_data}")
        
        login_window = LoginWindow(
            parent=root,
            session_manager=session_manager,
            on_login_success=on_login_success,
            on_register_success=on_register_success
        )
        
        print("✅ LoginWindow created")
        
        # Verify window properties
        if hasattr(login_window, 'window') and login_window.window:
            print(f"   Title: {login_window.window.title()}")
            print(f"   Geometry: {login_window.window.geometry()}")
            print(f"   Visible: {login_window.window.winfo_viewable()}")
            
            # Check if widgets exist and are visible
            widgets_exist = all([
                hasattr(login_window, 'email_entry') and login_window.email_entry,
                hasattr(login_window, 'password_entry') and login_window.password_entry,
                hasattr(login_window, 'license_entry') and login_window.license_entry,
            ])
            
            if widgets_exist:
                print("✅ All form widgets exist")
            else:
                print("❌ Some form widgets missing")
                return False
        
        # Test that only one window is visible
        root.update_idletasks()
        
        print("🎉 Single root window test completed!")
        print("📋 The login window should be visible and properly sized.")
        
        # Keep window open for manual verification
        print("⏰ Window will stay open for 5 seconds for verification...")
        root.after(5000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Single root window test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_window_visibility():
    """Test that the login window is properly visible"""
    print("🔍 Testing Window Visibility...")
    
    try:
        from gui.login_window import LoginWindow
        
        # Create root window
        root = tk.Tk()
        root.title("Visibility Test")
        root.geometry("200x100")
        root.withdraw()
        
        # Mock session manager
        class MockSessionManager:
            def __init__(self):
                self.supabase = None
        
        session_manager = MockSessionManager()
        
        # Create login window
        def on_login_success(user_data):
            print(f"Login success: {user_data}")
        
        def on_register_success(user_data):
            print(f"Register success: {user_data}")
        
        login_window = LoginWindow(
            parent=root,
            session_manager=session_manager,
            on_login_success=on_login_success,
            on_register_success=on_register_success
        )
        
        # Force update and check visibility
        root.update_idletasks()
        root.update()
        
        # Check window properties
        geometry = login_window.window.geometry()
        print(f"✅ Window geometry: {geometry}")
        
        # Parse geometry to check size
        if 'x' in geometry and '+' in geometry:
            size_part = geometry.split('+')[0]
            if 'x' in size_part:
                width, height = size_part.split('x')
                width, height = int(width), int(height)
                
                if width >= 400 and height >= 500:
                    print(f"✅ Window size is correct: {width}x{height}")
                else:
                    print(f"❌ Window size is too small: {width}x{height}")
                    return False
        
        # Check if window is on screen
        x_pos = login_window.window.winfo_x()
        y_pos = login_window.window.winfo_y()
        print(f"✅ Window position: {x_pos}, {y_pos}")
        
        # Pre-fill test data
        login_window.email_var.set("<EMAIL>")
        login_window.password_var.set("password123")
        login_window.license_var.set("TEST-LICENSE-001")
        print("✅ Test data pre-filled")
        
        print("🎉 Window visibility test completed!")
        print("📋 The window should be visible, properly sized, and contain form fields.")
        
        # Keep window open for manual verification
        print("⏰ Window will stay open for 5 seconds for verification...")
        root.after(5000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Window visibility test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 Final GUI Fixes Test")
    print("=" * 30)
    
    tests = [
        ("Single Root Window", test_single_root_window),
        ("Window Visibility", test_window_visibility),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
            results.append((test_name, False))
        
        # Small delay between tests
        time.sleep(1)
    
    # Summary
    print(f"\n📊 Test Results Summary")
    print("=" * 30)
    
    passed = 0
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All fixes are working correctly!")
        print("📋 The GUI should now show a single, visible login window.")
    else:
        print("⚠️ Some fixes still need work. Check the output above.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
