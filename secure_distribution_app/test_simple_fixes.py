#!/usr/bin/env python3
"""
Simple Test for GUI Fixes

This script creates a simple test to verify the GUI fixes work.
"""

import sys
import os
import tkinter as tk
from tkinter import ttk
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

# Suppress tkinter deprecation warning
os.environ['TK_SILENCE_DEPRECATION'] = '1'

def test_login_window_direct():
    """Test LoginWindow directly"""
    print("🔍 Testing LoginWindow directly...")
    
    try:
        # Load environment
        env_file = Path('.env')
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
        
        from gui.login_window import LoginWindow
        
        # Create root window
        root = tk.Tk()
        root.title("Test Root")
        root.geometry("200x100")
        root.withdraw()
        
        print("✅ Root window created")
        
        # Mock session manager
        class MockSessionManager:
            def __init__(self):
                self.supabase = None
            
            def login(self, email, password, license):
                print(f"Mock login: {email}")
                return {"success": True, "user_data": {"email": email}}
            
            def register(self, email, password, first_name, last_name, license_number):
                print(f"Mock register: {email}")
                return {"success": True, "user_data": {"email": email}}
        
        session_manager = MockSessionManager()
        print("✅ Mock session manager created")
        
        # Callback functions
        def on_login_success(user_data):
            print(f"✅ Login success: {user_data}")
        
        def on_register_success(user_data):
            print(f"✅ Register success: {user_data}")
        
        # Create LoginWindow
        print("🔧 Creating LoginWindow...")
        login_window = LoginWindow(
            parent=root,
            session_manager=session_manager,
            on_login_success=on_login_success,
            on_register_success=on_register_success
        )
        
        print("✅ LoginWindow created!")
        
        # Check window properties
        if hasattr(login_window, 'window') and login_window.window:
            print(f"   Title: {login_window.window.title()}")
            print(f"   Geometry: {login_window.window.geometry()}")
            
            # Force update
            login_window.window.update_idletasks()
            login_window.window.update()
            
            # Check actual size
            actual_width = login_window.window.winfo_width()
            actual_height = login_window.window.winfo_height()
            print(f"   Actual size: {actual_width}x{actual_height}")
            
            if actual_width >= 400 and actual_height >= 500:
                print("✅ Window size is correct")
            else:
                print(f"⚠️ Window size might be too small: {actual_width}x{actual_height}")
            
            # Check if widgets exist
            widgets = ['email_entry', 'password_entry', 'license_entry']
            for widget_name in widgets:
                if hasattr(login_window, widget_name):
                    widget = getattr(login_window, widget_name)
                    if widget and widget.winfo_exists():
                        print(f"   ✅ {widget_name} exists and is valid")
                    else:
                        print(f"   ❌ {widget_name} is invalid")
                else:
                    print(f"   ❌ {widget_name} missing")
            
            # Pre-fill test data
            try:
                login_window.email_var.set("<EMAIL>")
                login_window.password_var.set("password123")
                login_window.license_var.set("TEST-LICENSE-001")
                print("✅ Test data filled")
            except Exception as e:
                print(f"⚠️ Could not fill test data: {e}")
        
        print("🎉 LoginWindow test completed successfully!")
        print("📋 The window should be visible with form fields.")
        
        # Run for a short time
        root.after(3000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ LoginWindow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("🧪 Simple GUI Fixes Test")
    print("=" * 25)
    
    try:
        success = test_login_window_direct()
        
        if success:
            print("\n✅ GUI fixes test PASSED!")
            print("🎉 The LoginWindow should be working correctly!")
        else:
            print("\n❌ GUI fixes test FAILED!")
            print("⚠️ There are still issues with the GUI.")
        
        return success
        
    except Exception as e:
        print(f"\n❌ Test crashed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n🎯 Test result: {'PASS' if success else 'FAIL'}")
    sys.exit(0 if success else 1)
