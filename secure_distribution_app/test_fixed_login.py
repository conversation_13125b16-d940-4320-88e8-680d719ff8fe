#!/usr/bin/env python3
"""
Test Fixed LoginWindow

This script tests the fixed LoginWindow class.
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

# Suppress tkinter deprecation warning
os.environ['TK_SILENCE_DEPRECATION'] = '1'

# Load environment
def load_environment():
    """Load environment variables from .env file"""
    try:
        env_file = Path('.env')
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            print("✅ Environment loaded")
    except Exception as e:
        print(f"❌ Failed to load .env: {e}")

load_environment()

def test_fixed_login_window():
    """Test the fixed LoginWindow class"""
    print("🔍 Testing Fixed LoginWindow Class...")
    
    try:
        from gui.login_window import LoginWindow
        
        root = tk.Tk()
        root.title("Root Window")
        root.geometry("200x100")
        root.withdraw()  # Hide root
        
        print("✅ Root window created")
        
        # Mock session manager
        class MockSessionManager:
            def __init__(self):
                self.supabase = None
            
            def login(self, email, password, license):
                print(f"Mock login called: {email}, {license}")
                return {"success": True, "user_data": {"email": email, "license": license}}
            
            def register(self, email, password, first_name, last_name, license_number):
                print(f"Mock register called: {email}, {first_name} {last_name}, {license_number}")
                return {"success": True, "user_data": {"email": email, "name": f"{first_name} {last_name}"}}
        
        session_manager = MockSessionManager()
        print("✅ Mock session manager created")
        
        # Callback functions
        def on_login_success(user_data):
            print(f"✅ Login success callback: {user_data}")
            messagebox.showinfo("Success", f"Login successful!\nUser: {user_data.get('email', 'Unknown')}")
        
        def on_register_success(user_data):
            print(f"✅ Register success callback: {user_data}")
            messagebox.showinfo("Success", f"Registration successful!\nUser: {user_data.get('email', 'Unknown')}")
        
        print("✅ Callback functions defined")
        
        # Create LoginWindow
        print("🔧 Creating LoginWindow...")
        login_window = LoginWindow(
            parent=root,
            session_manager=session_manager,
            on_login_success=on_login_success,
            on_register_success=on_register_success
        )
        
        print("✅ LoginWindow created successfully!")
        
        # Check if window exists and is visible
        if hasattr(login_window, 'window') and login_window.window:
            print("✅ LoginWindow.window exists")
            print(f"   Title: {login_window.window.title()}")
            print(f"   Geometry: {login_window.window.geometry()}")
            
            # Check if widgets exist
            widgets_to_check = [
                ('email_entry', 'Email Entry'),
                ('password_entry', 'Password Entry'),
                ('license_entry', 'License Entry'),
                ('first_name_entry', 'First Name Entry'),
                ('last_name_entry', 'Last Name Entry'),
                ('confirm_password_entry', 'Confirm Password Entry'),
            ]
            
            for attr_name, display_name in widgets_to_check:
                if hasattr(login_window, attr_name):
                    widget = getattr(login_window, attr_name)
                    if widget:
                        print(f"   ✅ {display_name} exists")
                    else:
                        print(f"   ❌ {display_name} is None")
                else:
                    print(f"   ❌ {display_name} attribute missing")
            
            # Force update and make visible
            login_window.window.update_idletasks()
            login_window.window.update()
            print("✅ Window updated")
            
            # Pre-fill some test data
            try:
                login_window.email_var.set("<EMAIL>")
                login_window.password_var.set("password123")
                login_window.license_var.set("TEST-LICENSE-001")
                print("✅ Test data pre-filled")
            except Exception as e:
                print(f"⚠️ Could not pre-fill data: {e}")
            
        else:
            print("❌ LoginWindow.window does not exist")
            return False
        
        print("🎉 Fixed LoginWindow test completed!")
        print("📋 The login window should now be visible with all form fields.")
        print("📋 You can test login/register functionality.")
        
        # Keep window open for manual testing
        print("⏰ Window will stay open for 10 seconds for manual testing...")
        root.after(10000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Fixed LoginWindow test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 Fixed LoginWindow Test")
    print("=" * 30)
    
    try:
        success = test_fixed_login_window()
        
        if success:
            print("\n✅ Fixed LoginWindow test PASSED!")
            print("🎉 The GUI should now be working correctly!")
        else:
            print("\n❌ Fixed LoginWindow test FAILED!")
            print("⚠️ There are still issues with the GUI.")
        
        return success
        
    except Exception as e:
        print(f"\n❌ Test crashed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
