from supabase import create_client, Client

# Replace these with your actual Supabase project URL and API key
SUPABASE_URL = "https://onuqryetexwoqscozfnb.supabase.co"
SUPABASE_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6Im9udXFyeWV0ZXh3b3FzY296Zm5iIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTUyODk1NDgsImV4cCI6MjA3MDg2NTU0OH0.N0NaAxxHon2Cm_gXaYRkI31kq9gNwxiY8pN3TTdwLw0"

# Initialize Supabase client
supabase: Client = create_client(SUPABASE_URL, SUPABASE_KEY)

# Choose your table name
table_name = "user_profiles"

# Fetch all rows from the table
response = supabase.table(table_name).select("*").execute()
print("Table data:", response.data)

# Optionally: fetch with a filter (for example, where id=1)
filtered = supabase.table(table_name).select("*").eq("id", 1).execute()
print("Filtered data (id=1):", filtered.data)
