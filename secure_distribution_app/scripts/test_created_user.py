#!/usr/bin/env python3
"""
Test Created User Script

This script tests the user created via direct SQL insertion
to verify the complete authentication flow works.
"""

import os
import sys
import json
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def load_environment():
    """Load environment variables from .env file"""
    try:
        env_file = Path(__file__).parent.parent / '.env'
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            return True
    except Exception as e:
        print(f"❌ Failed to load environment: {e}")
    return False

def test_user_authentication():
    """Test authentication with the SQL-created user"""
    try:
        from supabase import create_client
        
        # Load environment
        if not load_environment():
            print("❌ Failed to load environment variables")
            return False
        
        supabase_url = os.getenv('SUPABASE_URL')
        anon_key = os.getenv('SUPABASE_ANON_KEY')
        
        if not supabase_url or not anon_key:
            print("❌ Missing Supabase credentials")
            return False
        
        # Create client (same as Flask app would use)
        supabase = create_client(supabase_url, anon_key)
        
        print("🧪 Testing SQL-created user authentication...")
        print("=" * 50)
        
        # Test 1: Sign in
        print("1. Testing sign-<NAME_EMAIL> / test123...")
        
        try:
            response = supabase.auth.sign_in_with_password({
                "email": "<EMAIL>",
                "password": "test123"
            })
            
            if response.user and response.session:
                print("✅ Sign-in successful!")
                print(f"   User ID: {response.user.id}")
                print(f"   Email: {response.user.email}")
                print(f"   Email Confirmed: {response.user.email_confirmed_at is not None}")
                print(f"   Access Token: {response.session.access_token[:20]}...")
                
                user = response.user
                session = response.session
            else:
                print("❌ Sign-in failed - no user or session returned")
                return False
                
        except Exception as signin_error:
            print(f"❌ Sign-in failed: {signin_error}")
            return False
        
        # Test 2: Profile access
        print("\n2. Testing user profile access...")
        
        try:
            profile_result = supabase.table('user_profiles').select('*').eq('user_id', user.id).execute()
            
            if profile_result.data:
                print("✅ Profile access successful!")
                profile = profile_result.data[0]
                print(f"   Profile ID: {profile['id']}")
                print(f"   Device Fingerprint: {profile['device_fingerprint']}")
                print(f"   License Number: {profile['license_number']}")
                print(f"   Metadata: {json.dumps(profile['metadata'], indent=4)}")
            else:
                print("❌ No profile found for user")
                return False
                
        except Exception as profile_error:
            print(f"❌ Profile access failed: {profile_error}")
            return False
        
        # Test 3: Session validation (like Flask app would do)
        print("\n3. Testing session validation...")
        
        try:
            # Test getting user with access token
            user_check = supabase.auth.get_user(session.access_token)
            
            if user_check and user_check.user:
                print("✅ Session validation successful!")
                print(f"   Validated User ID: {user_check.user.id}")
            else:
                print("⚠️ Session validation unclear")
                
        except Exception as session_error:
            print(f"⚠️ Session validation: {session_error}")
            # This might not work with anonymous key, but that's OK
        
        # Test 4: Profile update (test write permissions)
        print("\n4. Testing profile update...")
        
        try:
            # Update login count
            current_metadata = profile['metadata']
            current_metadata['login_count'] = current_metadata.get('login_count', 0) + 1
            current_metadata['last_test'] = 'SQL user test successful'
            
            update_result = supabase.table('user_profiles').update({
                'metadata': current_metadata,
                'updated_at': 'now()'
            }).eq('user_id', user.id).execute()
            
            if update_result.data:
                print("✅ Profile update successful!")
                print(f"   Updated login count: {current_metadata['login_count']}")
            else:
                print("⚠️ Profile update returned no data")
                
        except Exception as update_error:
            print(f"⚠️ Profile update: {update_error}")
            # This might fail due to RLS, but that's OK for testing
        
        print("\n" + "=" * 50)
        print("🎉 ALL TESTS PASSED!")
        print("📋 SQL-created user is fully functional!")
        print("🌐 Ready to test Flask app at: http://localhost:8080/login")
        print("📝 Credentials: <EMAIL> / test123")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        print("💡 Make sure supabase-py is installed: pip install supabase")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def test_flask_integration():
    """Test that Flask app components work with the created user"""
    try:
        print("\n🔧 Testing Flask integration components...")
        
        # Test license manager
        try:
            from auth.license_manager import LicenseManager
            from supabase import create_client
            
            supabase_url = os.getenv('SUPABASE_URL')
            service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
            
            if service_key:
                supabase = create_client(supabase_url, service_key)
                license_manager = LicenseManager(supabase)
                fingerprint = license_manager.generate_hardware_fingerprint()
                print(f"✅ License Manager works: {fingerprint}")
            else:
                print("⚠️ No service key for license manager test")
                
        except Exception as lm_error:
            print(f"⚠️ License Manager test: {lm_error}")
        
        # Test that we can import Flask app components
        try:
            sys.path.insert(0, str(Path(__file__).parent.parent / 'web_server'))
            # Don't actually import app.py as it might start the server
            print("✅ Flask app components accessible")
        except Exception as flask_error:
            print(f"⚠️ Flask components: {flask_error}")
        
        return True
        
    except Exception as e:
        print(f"⚠️ Flask integration test: {e}")
        return False

def main():
    """Main function"""
    print("🧪 SQL-Created User Test Suite")
    print("=" * 35)
    
    # Test authentication
    auth_success = test_user_authentication()
    
    if auth_success:
        # Test Flask integration
        flask_success = test_flask_integration()
        
        print("\n🎯 FINAL RESULTS:")
        print(f"   Authentication: {'✅ PASS' if auth_success else '❌ FAIL'}")
        print(f"   Flask Integration: {'✅ PASS' if flask_success else '⚠️ PARTIAL'}")
        
        if auth_success:
            print("\n🚀 READY FOR FLASK TESTING!")
            print("Run: python secure_distribution_app/main_browser_auth.py")
            print("Then visit: http://localhost:8080/login")
        
        return True
    else:
        print("\n❌ Authentication test failed!")
        print("💡 Check that you ran the SQL script successfully")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
