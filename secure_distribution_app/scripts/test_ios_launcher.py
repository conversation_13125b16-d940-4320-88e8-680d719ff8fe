#!/usr/bin/env python3
"""
Test iOS Launcher Functionality

This script tests the iOS app launcher by simulating the authentication
and then testing the iOS app route.
"""

import os
import sys
import json
import urllib.request
import urllib.parse
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def load_environment():
    """Load environment variables from .env file"""
    try:
        env_file = Path(__file__).parent.parent / '.env'
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            return True
    except Exception as e:
        print(f"❌ Failed to load environment: {e}")
    return False

def test_flask_server():
    """Test if Flask server is running"""
    try:
        response = urllib.request.urlopen('http://localhost:8080', timeout=5)
        print("✅ Flask server is running")
        return True
    except Exception as e:
        print(f"❌ Flask server not accessible: {e}")
        return False

def authenticate_user():
    """Authenticate user and get session"""
    try:
        print("🔐 Authenticating user...")
        
        # Prepare login data
        login_data = {
            "email": "<EMAIL>",
            "password": "test123"
        }
        
        # Convert to JSON and encode
        data = json.dumps(login_data).encode('utf-8')
        
        # Create request
        req = urllib.request.Request(
            'http://localhost:8080/api/auth/login',
            data=data,
            headers={'Content-Type': 'application/json'}
        )
        
        # Send request
        response = urllib.request.urlopen(req, timeout=10)
        response_data = json.loads(response.read().decode('utf-8'))
        
        if response_data.get('success'):
            print("✅ Authentication successful!")
            return response_data.get('access_token')
        else:
            print(f"❌ Authentication failed: {response_data.get('error')}")
            return None
            
    except Exception as e:
        print(f"❌ Authentication error: {e}")
        return None

def test_ios_launcher(access_token):
    """Test the iOS launcher route"""
    try:
        print("🚀 Testing iOS launcher...")
        
        # Create request with session cookie
        req = urllib.request.Request('http://localhost:8080/app_ios')
        
        # Add session cookie (simplified - in real scenario would need proper cookie handling)
        # For now, we'll test if the route is accessible
        
        try:
            response = urllib.request.urlopen(req, timeout=10)
            response_text = response.read().decode('utf-8')
            
            if 'iOS App Launched Successfully' in response_text:
                print("✅ iOS launcher working - launch page displayed!")
                return True
            elif 'Please log in' in response_text:
                print("⚠️ iOS launcher requires authentication (expected)")
                return True
            elif 'iOS automation tools not found' in response_text:
                print("⚠️ iOS launcher accessible but run.py not found")
                return False
            else:
                print("✅ iOS launcher route accessible")
                return True
                
        except urllib.error.HTTPError as e:
            if e.code == 302:  # Redirect (likely to login)
                print("✅ iOS launcher route working (redirects to login)")
                return True
            else:
                print(f"❌ iOS launcher HTTP error: {e.code}")
                return False
                
    except Exception as e:
        print(f"❌ iOS launcher test error: {e}")
        return False

def check_ios_script():
    """Check if the iOS script exists and is executable"""
    try:
        ios_script = Path(__file__).parent.parent.parent / "run.py"
        
        print(f"🔍 Checking iOS script at: {ios_script}")
        
        if ios_script.exists():
            print("✅ iOS script (run.py) exists")
            
            # Check if it's executable
            if os.access(ios_script, os.X_OK):
                print("✅ iOS script is executable")
            else:
                print("⚠️ iOS script exists but may not be executable")
            
            # Check first few lines to verify it's the right script
            with open(ios_script, 'r') as f:
                first_lines = f.read(200)
                if 'Mobile App Automation' in first_lines:
                    print("✅ iOS script appears to be the correct automation tool")
                    return True
                else:
                    print("⚠️ iOS script exists but may not be the automation tool")
                    return False
        else:
            print("❌ iOS script (run.py) not found")
            return False
            
    except Exception as e:
        print(f"❌ Error checking iOS script: {e}")
        return False

def check_ports():
    """Check if the expected ports are available"""
    import socket
    
    ports_to_check = [8081, 4723, 8100]  # iOS app port, Appium, WDA
    
    print("🔍 Checking port availability...")
    
    for port in ports_to_check:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(1)
        result = sock.connect_ex(('localhost', port))
        sock.close()
        
        if result == 0:
            print(f"⚠️ Port {port} is already in use")
        else:
            print(f"✅ Port {port} is available")

def main():
    """Main test function"""
    print("🧪 iOS LAUNCHER TEST")
    print("=" * 40)
    
    # Load environment
    if not load_environment():
        return False
    
    # Test Flask server
    if not test_flask_server():
        print("\n💡 Start Flask server: python secure_distribution_app/web_server/app.py")
        return False
    
    # Check iOS script
    ios_script_ok = check_ios_script()
    
    # Check ports
    check_ports()
    
    # Test authentication
    access_token = authenticate_user()
    
    # Test iOS launcher
    launcher_ok = test_ios_launcher(access_token)
    
    # Summary
    print("\n" + "=" * 40)
    print("📋 TEST SUMMARY")
    print("=" * 40)
    print(f"✅ Flask Server: RUNNING")
    print(f"{'✅' if ios_script_ok else '❌'} iOS Script: {'FOUND' if ios_script_ok else 'MISSING'}")
    print(f"{'✅' if access_token else '❌'} Authentication: {'WORKING' if access_token else 'FAILED'}")
    print(f"{'✅' if launcher_ok else '❌'} iOS Launcher: {'WORKING' if launcher_ok else 'FAILED'}")
    
    if all([ios_script_ok, launcher_ok]):
        print("\n🎉 iOS LAUNCHER IS READY!")
        print("🌐 Test at: http://localhost:8080/login")
        print("📝 Login → Dashboard → Click iOS App")
        return True
    else:
        print("\n❌ ISSUES FOUND - Check the details above")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
