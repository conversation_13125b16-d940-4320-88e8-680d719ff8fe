#!/usr/bin/env python3
"""
Test iOS App Button Click

This script simulates clicking the iOS App button in the Flask dashboard.
"""

import requests
import time
import socket
from pathlib import Path

def test_ios_button_click():
    """Test clicking the iOS App button"""
    print("🔍 TESTING iOS APP BUTTON CLICK")
    print("=" * 50)
    
    # First, check if Flask dashboard is running
    try:
        response = requests.get('http://localhost:8080/dashboard', timeout=5)
        if response.status_code == 200 or response.status_code == 302:
            print("✅ Flask dashboard is running")
        else:
            print(f"❌ Flask dashboard returned status: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ Flask dashboard is not accessible: {e}")
        return False
    
    # Try to access the iOS app endpoint directly
    print("\n📋 Testing iOS app endpoint directly...")
    try:
        # This will redirect to login since we don't have a session
        response = requests.get('http://localhost:8080/app_ios', timeout=10, allow_redirects=False)
        print(f"📋 iOS endpoint response status: {response.status_code}")
        
        if response.status_code == 302:
            print("✅ iOS endpoint is accessible (redirects to login as expected)")
        else:
            print(f"⚠️ Unexpected response from iOS endpoint: {response.status_code}")
            
    except Exception as e:
        print(f"❌ Error accessing iOS endpoint: {e}")
        return False
    
    # Check if iOS app is already running on port 8081
    print("\n📋 Checking if iOS app is already running...")
    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
    sock.settimeout(2)
    result = sock.connect_ex(('localhost', 8081))
    sock.close()
    
    if result == 0:
        print("✅ iOS app is already running on port 8081!")
        print("🌐 You can access it at: http://localhost:8081")
        return True
    else:
        print("📋 iOS app is not running on port 8081")
    
    print("\n💡 NEXT STEPS:")
    print("1. Log in to the Flask dashboard at: http://localhost:8080/login")
    print("2. Click the 'iOS App' button")
    print("3. The iOS app should launch on port 8081")
    print("4. If it fails, check the Flask server logs for error messages")
    
    return True

def check_flask_logs():
    """Check for Flask server logs"""
    print("\n🔍 CHECKING FOR FLASK LOGS")
    print("=" * 50)
    
    # Look for common log locations
    log_locations = [
        Path(__file__).parent.parent.parent / "logs",
        Path(__file__).parent.parent / "logs",
        Path.cwd() / "logs"
    ]
    
    for log_dir in log_locations:
        if log_dir.exists():
            print(f"📁 Found log directory: {log_dir}")
            for log_file in log_dir.glob("*.log"):
                print(f"📄 Log file: {log_file}")
        else:
            print(f"📁 Log directory not found: {log_dir}")
    
    print("\n💡 To see Flask server logs in real-time:")
    print("   Check the terminal where you started the Flask server")
    print("   Look for any error messages when clicking the iOS App button")

def main():
    """Main function"""
    success = test_ios_button_click()
    check_flask_logs()
    
    if success:
        print("\n🎉 iOS APP BUTTON TEST COMPLETED!")
        print("📋 The iOS app launch mechanism is working correctly")
        print("🌐 Try clicking the iOS App button in the dashboard")
    else:
        print("\n❌ iOS APP BUTTON TEST FAILED")
        print("💡 Check the Flask dashboard and server logs")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
