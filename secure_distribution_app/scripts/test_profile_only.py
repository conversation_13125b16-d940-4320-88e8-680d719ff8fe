#!/usr/bin/env python3
"""
Test Profile Only Setup

This script tests the user profile created in the database
and helps with the auth user creation process.
"""

import os
import sys
import json
from pathlib import Path

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

def load_environment():
    """Load environment variables from .env file"""
    try:
        env_file = Path(__file__).parent.parent / '.env'
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            return True
    except Exception as e:
        print(f"❌ Failed to load environment: {e}")
    return False

def check_profile_exists():
    """Check if the user profile was created successfully"""
    try:
        from supabase import create_client
        
        # Load environment
        if not load_environment():
            print("❌ Failed to load environment variables")
            return False, None
        
        supabase_url = os.getenv('SUPABASE_URL')
        service_key = os.getenv('SUPABASE_SERVICE_ROLE_KEY')
        
        if not supabase_url or not service_key:
            print("❌ Missing Supabase credentials")
            return False, None
        
        # Use service role to check profile
        supabase = create_client(supabase_url, service_key)
        
        print("🔍 Checking for test user profile...")
        
        # Look for profile with test email
        result = supabase.table('user_profiles').select('*').eq('metadata->>email', '<EMAIL>').execute()
        
        if result.data:
            print("✅ User profile found!")
            profile = result.data[0]
            print(f"   Profile ID: {profile['id']}")
            print(f"   User ID: {profile['user_id']}")
            print(f"   Device Fingerprint: {profile['device_fingerprint']}")
            print(f"   License: {profile['license_number']}")
            print(f"   Email in metadata: {profile['metadata'].get('email')}")
            print(f"   Placeholder: {profile['metadata'].get('placeholder_user_id', False)}")
            
            return True, profile
        else:
            print("❌ No user profile found")
            print("💡 Run the profile creation SQL script first")
            return False, None
            
    except Exception as e:
        print(f"❌ Error checking profile: {e}")
        return False, None

def check_auth_user_exists():
    """Check if auth user exists for test email"""
    try:
        from supabase import create_client
        
        supabase_url = os.getenv('SUPABASE_URL')
        anon_key = os.getenv('SUPABASE_ANON_KEY')
        
        # Use anonymous client to test sign-in
        supabase = create_client(supabase_url, anon_key)
        
        print("🔍 Checking if auth user exists...")
        
        try:
            # Try to sign in
            response = supabase.auth.sign_in_with_password({
                "email": "<EMAIL>",
                "password": "test123"
            })
            
            if response.user:
                print("✅ Auth user exists and can sign in!")
                print(f"   Auth User ID: {response.user.id}")
                print(f"   Email: {response.user.email}")
                print(f"   Email Confirmed: {response.user.email_confirmed_at is not None}")
                return True, response.user
            else:
                print("❌ Sign-in failed - no user returned")
                return False, None
                
        except Exception as signin_error:
            error_str = str(signin_error)
            if 'invalid' in error_str.lower() or 'credentials' in error_str.lower():
                print("❌ Auth user doesn't exist or wrong credentials")
                return False, None
            else:
                print(f"❌ Sign-in error: {signin_error}")
                return False, None
        
    except Exception as e:
        print(f"❌ Error checking auth user: {e}")
        return False, None

def provide_instructions(profile_exists, auth_user_exists, profile_data=None, auth_user_data=None):
    """Provide next steps based on current state"""
    print("\n" + "=" * 50)
    print("📋 CURRENT STATUS AND NEXT STEPS")
    print("=" * 50)
    
    if not profile_exists:
        print("❌ User profile: NOT FOUND")
        print("🔧 Next step: Run the profile creation SQL script")
        print("   1. Copy contents of: database/create_test_user_direct.sql")
        print("   2. Paste in Supabase SQL Editor")
        print("   3. Click 'Run'")
        print("   4. Run this script again")
        return
    
    print("✅ User profile: EXISTS")
    
    if not auth_user_exists:
        print("❌ Auth user: NOT FOUND")
        print("🔧 Next step: Create auth user in Supabase dashboard")
        print("   1. Go to Supabase Dashboard → Authentication → Users")
        print("   2. Click 'Add User'")
        print("   3. Email: <EMAIL>")
        print("   4. Password: test123")
        print("   5. ✅ Check 'Email Confirm'")
        print("   6. Click 'Create User'")
        print("   7. Run the linking SQL script")
        print("   8. Run this script again")
        return
    
    print("✅ Auth user: EXISTS")
    
    # Check if they're linked
    if profile_data and auth_user_data:
        if profile_data['user_id'] == auth_user_data.id:
            print("✅ Profile and auth user: LINKED")
            print("🎉 SETUP COMPLETE!")
            print("🌐 Ready to test at: http://localhost:8080/login")
            print("📝 Credentials: <EMAIL> / test123")
        else:
            print("⚠️ Profile and auth user: NOT LINKED")
            print("🔧 Next step: Link profile to auth user")
            print("   1. Copy contents of: database/link_profile_to_auth_user.sql")
            print("   2. Paste in Supabase SQL Editor")
            print("   3. Click 'Run'")
            print("   4. Run this script again")

def test_complete_flow():
    """Test the complete authentication flow if everything is set up"""
    try:
        from supabase import create_client
        
        supabase_url = os.getenv('SUPABASE_URL')
        anon_key = os.getenv('SUPABASE_ANON_KEY')
        
        supabase = create_client(supabase_url, anon_key)
        
        print("\n🧪 Testing complete authentication flow...")
        
        # Test sign-in
        response = supabase.auth.sign_in_with_password({
            "email": "<EMAIL>",
            "password": "test123"
        })
        
        if not response.user:
            print("❌ Authentication failed")
            return False
        
        user = response.user
        
        # Test profile access
        profile_result = supabase.table('user_profiles').select('*').eq('user_id', user.id).execute()
        
        if not profile_result.data:
            print("❌ No profile found for authenticated user")
            return False
        
        profile = profile_result.data[0]
        
        print("✅ Complete authentication flow working!")
        print(f"   User ID: {user.id}")
        print(f"   Profile ID: {profile['id']}")
        print(f"   Device Fingerprint: {profile['device_fingerprint']}")
        print(f"   License: {profile['license_number']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Flow test error: {e}")
        return False

def main():
    """Main function"""
    print("🧪 Profile-Only Setup Test")
    print("=" * 30)
    
    # Check profile
    profile_exists, profile_data = check_profile_exists()
    
    # Check auth user
    auth_user_exists, auth_user_data = check_auth_user_exists()
    
    # Provide instructions
    provide_instructions(profile_exists, auth_user_exists, profile_data, auth_user_data)
    
    # If both exist, test complete flow
    if profile_exists and auth_user_exists:
        flow_success = test_complete_flow()
        
        if flow_success:
            print("\n🚀 READY FOR FLASK TESTING!")
            print("Start Flask app: python secure_distribution_app/main_browser_auth.py")
        
        return flow_success
    
    return profile_exists

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
