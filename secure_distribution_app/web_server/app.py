"""
Flask Web Server for Custom Template Authentication

Serves the existing HTML templates and handles authentication through custom forms.
"""

import os
import sys
import logging
import subprocess
from pathlib import Path
from flask import Flask, render_template, render_template_string, request, jsonify, session, redirect, url_for, flash
import secrets
from datetime import datetime, timezone

# Add parent directory to path
sys.path.insert(0, str(Path(__file__).parent.parent))

# Import Supabase and authentication modules
try:
    from supabase import create_client, Client
    from auth.license_manager import LicenseManager
except ImportError as e:
    print(f"❌ Import error: {e}")
    print("💡 Make sure supabase-py is installed: pip install supabase")
    sys.exit(1)

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Load environment variables
def load_environment():
    """Load environment variables from .env file"""
    try:
        env_file = Path(__file__).parent.parent / '.env'
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            logger.info("Environment variables loaded")
            return True
    except Exception as e:
        logger.error(f"Failed to load environment: {e}")
    return False

# Load environment
load_environment()

# Initialize Flask app
app = Flask(__name__,
           template_folder=str(Path(__file__).parent.parent.parent / "templates"),
           static_folder=str(Path(__file__).parent.parent.parent / "templates"))

# Configure secret key for sessions
app.secret_key = secrets.token_hex(32)

# Initialize Supabase client
SUPABASE_URL = os.getenv('SUPABASE_URL')
SUPABASE_ANON_KEY = os.getenv('SUPABASE_ANON_KEY')

if not SUPABASE_URL or not SUPABASE_ANON_KEY:
    logger.error("Missing Supabase credentials in environment")
    print("❌ Missing SUPABASE_URL or SUPABASE_ANON_KEY in .env file")
    sys.exit(1)

try:
    supabase: Client = create_client(SUPABASE_URL, SUPABASE_ANON_KEY)
    logger.info("Supabase client initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize Supabase client: {e}")
    print(f"❌ Supabase initialization failed: {e}")
    sys.exit(1)

# Initialize License Manager for device fingerprinting
try:
    license_manager = LicenseManager(supabase)
    logger.info("License manager initialized successfully")
except Exception as e:
    logger.error(f"Failed to initialize license manager: {e}")
    license_manager = None

# Supabase authentication functions
def create_user_profile(user_id: str, email: str, device_fingerprint: str = None) -> bool:
    """Create user profile in Supabase"""
    try:
        # Generate device fingerprint if not provided
        if not device_fingerprint and license_manager:
            device_fingerprint = license_manager.generate_hardware_fingerprint()

        profile_data = {
            'user_id': user_id,
            'device_fingerprint': device_fingerprint,
            'created_at': datetime.now(timezone.utc).isoformat(),
            'updated_at': datetime.now(timezone.utc).isoformat(),
            'metadata': {
                'email': email,
                'login_count': 1
            }
        }

        result = supabase.table('user_profiles').insert(profile_data).execute()
        logger.info(f"User profile created for {email}")
        return True

    except Exception as e:
        logger.error(f"Failed to create user profile: {e}")
        return False

def get_user_profile(user_id: str) -> dict:
    """Get user profile from Supabase"""
    try:
        result = supabase.table('user_profiles').select('*').eq('user_id', user_id).execute()
        if result.data:
            return result.data[0]
        return None
    except Exception as e:
        logger.error(f"Failed to get user profile: {e}")
        return None

def update_user_profile(user_id: str, updates: dict) -> bool:
    """Update user profile in Supabase"""
    try:
        updates['updated_at'] = datetime.now(timezone.utc).isoformat()
        result = supabase.table('user_profiles').update(updates).eq('user_id', user_id).execute()
        return True
    except Exception as e:
        logger.error(f"Failed to update user profile: {e}")
        return False

@app.route('/')
def index():
    """Home page"""
    return render_template('index.html')

@app.route('/login')
def login_page():
    """Login page"""
    return render_template('login.html')

@app.route('/register')
def register_page():
    """Register page"""
    return render_template('register.html')

@app.route('/dashboard')
def dashboard():
    """Dashboard page - requires authentication"""
    if 'access_token' not in session:
        flash('Please log in to access the dashboard.', 'warning')
        return redirect(url_for('login_page'))

    # Validate Supabase session
    try:
        user = supabase.auth.get_user(session['access_token'])
        if not user:
            flash('Session expired. Please log in again.', 'warning')
            session.clear()
            return redirect(url_for('login_page'))

        # Enforce license compliance before dashboard access
        if license_manager:
            try:
                license_valid = license_manager.enforce_license_compliance(user.user.id)
                if not license_valid:
                    validation_result = license_manager.validate_license(user.user.id)
                    error_msg = validation_result.get('error', 'License validation failed')
                    flash(f'Access denied: {error_msg}', 'error')
                    session.clear()
                    return redirect(url_for('login_page'))

                # Update last validation timestamp
                license_manager.update_last_validation(user.user.id)

            except Exception as e:
                logger.error(f"License enforcement error in dashboard: {e}")
                flash('License validation error. Please contact support.', 'error')
                session.clear()
                return redirect(url_for('login_page'))

        # Get user profile
        profile = get_user_profile(user.user.id)

        return render_template('dashboard.html', user=user.user, profile=profile)

    except Exception as e:
        logger.error(f"Dashboard access error: {e}")
        flash('Session validation failed. Please log in again.', 'error')
        session.clear()
        return redirect(url_for('login_page'))

@app.route('/api/auth/login', methods=['POST'])
def api_login():
    """Handle login API request with Supabase authentication"""
    try:
        data = request.get_json()
        email = data.get('email', '').lower().strip()
        password = data.get('password', '')

        logger.info(f"Supabase login attempt for email: {email}")

        if not email or not password:
            return jsonify({'error': 'Email and password are required'}), 400

        # Authenticate with Supabase
        try:
            auth_response = supabase.auth.sign_in_with_password({
                "email": email,
                "password": password
            })

            if not auth_response.user:
                logger.warning(f"Supabase authentication failed for: {email}")
                return jsonify({'error': 'Invalid email or password'}), 401

            user = auth_response.user
            session_data = auth_response.session

            # Create or update user profile
            profile = get_user_profile(user.id)
            if not profile:
                # Create new profile
                device_fingerprint = license_manager.generate_hardware_fingerprint() if license_manager else None
                create_user_profile(user.id, email, device_fingerprint)
                profile = get_user_profile(user.id)
            else:
                # Update login count
                metadata = profile.get('metadata', {})
                metadata['login_count'] = metadata.get('login_count', 0) + 1
                metadata['last_login'] = datetime.now(timezone.utc).isoformat()
                update_user_profile(user.id, {'metadata': metadata})

            # Validate license before allowing login
            if license_manager:
                try:
                    license_valid = license_manager.enforce_license_compliance(user.id)
                    if not license_valid:
                        validation_result = license_manager.validate_license(user.id)
                        error_msg = validation_result.get('error', 'License validation failed')
                        logger.warning(f"License validation failed for user {email}: {error_msg}")
                        return jsonify({'error': f'Access denied: {error_msg}'}), 403

                    # Bind license to hardware if not already bound
                    if not profile.get('device_fingerprint'):
                        license_manager.bind_license_to_hardware(user.id)

                    # Update last validation timestamp
                    license_manager.update_last_validation(user.id)

                except Exception as e:
                    logger.error(f"License enforcement error during login: {e}")
                    return jsonify({'error': 'License validation error. Please contact support.'}), 500

            # Store session data
            session['access_token'] = session_data.access_token
            session['refresh_token'] = session_data.refresh_token
            session['user_id'] = user.id
            session['user_email'] = email
            session['authenticated'] = True

            logger.info(f"Supabase login successful for user: {email}")

            return jsonify({
                'success': True,
                'message': 'Login successful',
                'access_token': session_data.access_token,
                'user': {
                    'id': user.id,
                    'email': email,
                    'created_at': user.created_at,
                    'profile': profile
                }
            })

        except Exception as auth_error:
            logger.error(f"Supabase authentication error: {auth_error}")
            return jsonify({'error': 'Invalid email or password'}), 401

    except Exception as e:
        logger.error(f"Login error: {e}")
        return jsonify({'error': 'An error occurred during login'}), 500

@app.route('/api/auth/register', methods=['POST'])
def api_register():
    """Handle registration API request with Supabase"""
    try:
        data = request.get_json()
        email = data.get('email', '').lower().strip()
        password = data.get('password', '')
        first_name = data.get('first_name', '').strip()
        last_name = data.get('last_name', '').strip()
        license_number = data.get('license_number', '').strip()

        logger.info(f"Supabase registration attempt for email: {email}")

        if not email or not password:
            return jsonify({'error': 'Email and password are required'}), 400

        if len(password) < 6:
            return jsonify({'error': 'Password must be at least 6 characters long'}), 400

        # Register with Supabase
        try:
            auth_response = supabase.auth.sign_up({
                "email": email,
                "password": password,
                "options": {
                    "data": {
                        "first_name": first_name,
                        "last_name": last_name
                    }
                }
            })

            if not auth_response.user:
                return jsonify({'error': 'Registration failed'}), 400

            user = auth_response.user

            # Create user profile
            device_fingerprint = license_manager.generate_hardware_fingerprint() if license_manager else None
            profile_created = create_user_profile(user.id, email, device_fingerprint)

            if profile_created and license_number:
                # Update profile with license number
                update_user_profile(user.id, {'license_number': license_number})

            logger.info(f"Supabase registration successful for user: {email}")

            return jsonify({
                'success': True,
                'message': 'Registration successful. Please check your email for verification.',
                'user': {
                    'id': user.id,
                    'email': email,
                    'created_at': user.created_at
                }
            })

        except Exception as auth_error:
            logger.error(f"Supabase registration error: {auth_error}")
            error_msg = str(auth_error)
            if 'already registered' in error_msg.lower():
                return jsonify({'error': 'Email already registered'}), 409
            return jsonify({'error': 'Registration failed'}), 400

    except Exception as e:
        logger.error(f"Registration error: {e}")
        return jsonify({'error': 'An error occurred during registration'}), 500

@app.route('/api/auth/logout', methods=['POST'])
def api_logout():
    """Handle logout API request with Supabase"""
    try:
        # Sign out from Supabase if we have a session
        if 'access_token' in session:
            try:
                supabase.auth.sign_out()
            except Exception as e:
                logger.warning(f"Supabase logout error: {e}")

        session.clear()
        return jsonify({'success': True, 'message': 'Logged out successfully'})
    except Exception as e:
        logger.error(f"Logout error: {e}")
        return jsonify({'error': 'An error occurred during logout'}), 500

@app.route('/api/session/create', methods=['POST'])
def api_create_session():
    """Create session for platform launch"""
    try:
        if 'access_token' not in session:
            return jsonify({'error': 'Not authenticated'}), 401

        # Validate Supabase session
        try:
            user = supabase.auth.get_user(session['access_token'])
            if not user:
                return jsonify({'error': 'Invalid session'}), 401
        except Exception:
            return jsonify({'error': 'Session validation failed'}), 401

        data = request.get_json()
        platform = data.get('platform')

        if platform not in ['android', 'ios']:
            return jsonify({'error': 'Invalid platform'}), 400

        # Generate session ID
        session_id = secrets.token_hex(16)

        logger.info(f"Created session {session_id} for platform {platform} (user: {user.user.email})")

        return jsonify({
            'success': True,
            'session_id': session_id,
            'platform': platform
        })

    except Exception as e:
        logger.error(f"Session creation error: {e}")
        return jsonify({'error': 'Failed to create session'}), 500

@app.route('/app_android')
@app.route('/app_android/<session_id>')
def app_android(session_id=None):
    """Launch Android automation app"""
    try:
        if 'access_token' not in session:
            flash('Please log in to access applications.', 'warning')
            return redirect(url_for('login_page'))

        # Validate Supabase session
        try:
            user = supabase.auth.get_user(session['access_token'])
            if not user:
                flash('Session expired. Please log in again.', 'warning')
                session.clear()
                return redirect(url_for('login_page'))
        except Exception:
            flash('Session validation failed. Please log in again.', 'error')
            session.clear()
            return redirect(url_for('login_page'))

        logger.info(f"Launching Android app for user: {user.user.email}")
        
        # In a real implementation, this would launch the Android automation tools
        # For now, we'll show a placeholder or launch the actual script
        
        # Find the main Android automation script
        android_script = Path(__file__).parent.parent.parent / "run_android.py"

        if android_script.exists():
            try:
                # Launch the Android automation script with specific parameters
                # Use a different port to avoid conflicts with the auth server
                android_port = 8082  # Different from auth server (8080) and iOS (8081)

                # Launch with specific Android configuration
                # Use virtual environment Python if available
                venv_python = Path(__file__).parent.parent.parent / "venv" / "bin" / "python"
                python_executable = str(venv_python) if venv_python.exists() else sys.executable

                launch_command = [
                    python_executable,
                    str(android_script),
                    '--flask-port', str(android_port),
                    '--appium-port', '4724',  # Different from iOS Appium port
                ]

                logger.info(f"Launching Android app with command: {' '.join(launch_command)}")

                # Launch the process
                process = subprocess.Popen(
                    launch_command,
                    cwd=str(android_script.parent),
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )

                # Give it a moment to start
                import time
                time.sleep(2)

                # Check if process is still running
                if process.poll() is None:
                    flash(f'Android automation tools launched successfully on port {android_port}!', 'success')
                    logger.info(f"Android app launched successfully, PID: {process.pid}")

                    # Create a launch confirmation page
                    return render_template_string("""
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>Android App Launched</title>
                        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
                        <meta http-equiv="refresh" content="3;url=http://localhost:{{ android_port }}">
                    </head>
                    <body class="bg-light">
                        <div class="container mt-5">
                            <div class="row justify-content-center">
                                <div class="col-md-6">
                                    <div class="card">
                                        <div class="card-body text-center">
                                            <div class="mb-4">
                                                <i class="bi bi-android2" style="font-size: 4rem; color: #3DDC84;"></i>
                                            </div>
                                            <h3 class="card-title text-success">Android App Launched Successfully!</h3>
                                            <p class="card-text">The Android automation tool is starting up...</p>
                                            <div class="alert alert-info">
                                                <strong>Port:</strong> {{ android_port }}<br>
                                                <strong>User:</strong> {{ user_email }}
                                            </div>
                                            <div class="mb-3">
                                                <div class="spinner-border text-success" role="status">
                                                    <span class="visually-hidden">Loading...</span>
                                                </div>
                                            </div>
                                            <p class="text-muted">You will be redirected automatically in 3 seconds...</p>
                                            <div class="d-grid gap-2">
                                                <a href="http://localhost:{{ android_port }}" class="btn btn-success">
                                                    Open Android App Now
                                                </a>
                                                <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                                                    Back to Dashboard
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </body>
                    </html>
                    """, android_port=android_port, user_email=user.user.email)
                else:
                    # Process failed to start
                    stdout, stderr = process.communicate()
                    error_msg = f"Process failed to start. Exit code: {process.returncode}"
                    if stderr:
                        error_msg += f"\nError: {stderr}"
                    logger.error(f"Android app launch failed: {error_msg}")
                    flash(f'Failed to launch Android tools: {error_msg}', 'error')

            except Exception as e:
                logger.error(f"Failed to launch Android script: {e}")
                flash(f'Failed to launch Android tools: {e}', 'error')
        else:
            logger.error(f"Android script not found at: {android_script}")
            flash('Android automation tools not found. Please check the installation.', 'warning')

        # If we get here, something went wrong, redirect back to dashboard
        return redirect(url_for('dashboard'))
        
    except Exception as e:
        logger.error(f"Android app launch error: {e}")
        flash(f'Error launching Android app: {e}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/app_ios')
@app.route('/app_ios/<session_id>')
def app_ios(session_id=None):
    """Launch iOS automation app"""
    try:
        if 'access_token' not in session:
            flash('Please log in to access applications.', 'warning')
            return redirect(url_for('login_page'))

        # Validate Supabase session
        try:
            user = supabase.auth.get_user(session['access_token'])
            if not user:
                flash('Session expired. Please log in again.', 'warning')
                session.clear()
                return redirect(url_for('login_page'))
        except Exception:
            flash('Session validation failed. Please log in again.', 'error')
            session.clear()
            return redirect(url_for('login_page'))

        # Enforce license compliance before allowing access
        if license_manager:
            try:
                license_valid = license_manager.enforce_license_compliance(user.user.id)
                if not license_valid:
                    validation_result = license_manager.validate_license(user.user.id)
                    error_msg = validation_result.get('error', 'License validation failed')
                    flash(f'Access denied: {error_msg}', 'error')
                    session.clear()
                    return redirect(url_for('login_page'))

                # Update last validation timestamp
                license_manager.update_last_validation(user.user.id)

            except Exception as e:
                logger.error(f"License enforcement error: {e}")
                flash('License validation error. Please contact support.', 'error')
                session.clear()
                return redirect(url_for('login_page'))

        logger.info(f"Launching iOS app for user: {user.user.email}")

        # Find the main iOS automation script
        ios_script = Path(__file__).parent.parent.parent / "run.py"

        if ios_script.exists():
            try:
                # Launch the iOS automation script with specific parameters
                # Use a different port to avoid conflicts with the auth server
                ios_port = 8090  # Different from auth server port (8080) and avoid common conflicts

                # Launch with specific iOS configuration
                # Use virtual environment Python if available
                venv_python = Path(__file__).parent.parent.parent / "venv" / "bin" / "python"

                if venv_python.exists():
                    # Use virtual environment with proper activation
                    launch_command = [
                        'bash', '-c',
                        f'cd {ios_script.parent} && source venv/bin/activate && python run.py --flask-port {ios_port} --appium-port 4723 --wda-port 8100'
                    ]
                else:
                    # Fallback to system Python
                    launch_command = [
                        sys.executable,
                        str(ios_script),
                        '--flask-port', str(ios_port),
                        '--appium-port', '4723',
                        '--wda-port', '8100'
                    ]

                logger.info(f"Launching iOS app with command: {' '.join(launch_command)}")

                # Launch the process
                process = subprocess.Popen(
                    launch_command,
                    cwd=str(ios_script.parent),
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )

                # Give it a moment to start
                import time
                time.sleep(2)

                # Check if process is still running
                if process.poll() is None:
                    flash(f'iOS automation tools launched successfully on port {ios_port}!', 'success')
                    logger.info(f"iOS app launched successfully, PID: {process.pid}")

                    # Wait a bit longer for the iOS app to fully start
                    import time
                    time.sleep(3)

                    # Check if iOS app is responding
                    import socket
                    sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                    sock.settimeout(2)
                    ios_app_ready = sock.connect_ex(('localhost', ios_port)) == 0
                    sock.close()

                    if ios_app_ready:
                        # iOS app is ready, redirect immediately
                        logger.info(f"iOS app is ready on port {ios_port}, redirecting user")

                        # Store session info for iOS app to access
                        session['ios_app_launched'] = True
                        session['ios_app_port'] = ios_port
                        session['launch_timestamp'] = datetime.now(timezone.utc).isoformat()

                        # Create session token for iOS app
                        access_token = session.get('access_token')
                        dashboard_url = f"http://localhost:{request.environ.get('SERVER_PORT', '8080')}/dashboard"

                        # Direct redirect to iOS app with session token
                        ios_url = f'http://localhost:{ios_port}/?session_token={access_token}&return_url={dashboard_url}&user_email={user.user.email}'
                        return redirect(ios_url)
                    else:
                        # iOS app not ready yet, show loading page with auto-redirect
                        logger.info(f"iOS app starting on port {ios_port}, showing loading page")

                        return render_template_string("""
                        <!DOCTYPE html>
                        <html>
                        <head>
                            <title>Launching iOS Automation Platform</title>
                            <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.2/dist/css/bootstrap.min.css" rel="stylesheet">
                            <link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/bootstrap-icons@1.11.3/font/bootstrap-icons.css">
                            <script>
                                let checkCount = 0;
                                const maxChecks = 20; // 20 seconds max

                                function checkiOSApp() {
                                    checkCount++;
                                    fetch('http://localhost:{{ ios_port }}', {mode: 'no-cors'})
                                        .then(() => {
                                            // iOS app is ready, redirect with session token
                                            const iosUrl = 'http://localhost:{{ ios_port }}/?session_token={{ access_token }}&return_url={{ dashboard_url }}&user_email={{ user_email }}';
                                            window.location.href = iosUrl;
                                        })
                                        .catch(() => {
                                            // iOS app not ready yet
                                            if (checkCount < maxChecks) {
                                                setTimeout(checkiOSApp, 1000);
                                            } else {
                                                // Timeout, show manual link
                                                document.getElementById('loading').style.display = 'none';
                                                document.getElementById('manual').style.display = 'block';
                                            }
                                        });
                                }

                                // Start checking after 2 seconds
                                setTimeout(checkiOSApp, 2000);
                            </script>
                        </head>
                        <body class="bg-light">
                            <div class="container mt-5">
                                <div class="row justify-content-center">
                                    <div class="col-md-8">
                                        <div class="card">
                                            <div class="card-body text-center">
                                                <div id="loading">
                                                    <div class="mb-4">
                                                        <i class="bi bi-apple" style="font-size: 4rem; color: #007AFF;"></i>
                                                    </div>
                                                    <h3 class="card-title text-primary">Launching iOS Automation Platform</h3>
                                                    <p class="card-text">Initializing iOS testing environment...</p>
                                                    <div class="alert alert-info">
                                                        <strong>Port:</strong> {{ ios_port }}<br>
                                                        <strong>User:</strong> {{ user_email }}<br>
                                                        <strong>Status:</strong> Starting up...
                                                    </div>
                                                    <div class="mb-3">
                                                        <div class="spinner-border text-primary" role="status">
                                                            <span class="visually-hidden">Loading...</span>
                                                        </div>
                                                    </div>
                                                    <p class="text-muted">You will be redirected automatically when ready...</p>
                                                </div>

                                                <div id="manual" style="display: none;">
                                                    <div class="mb-4">
                                                        <i class="bi bi-exclamation-triangle" style="font-size: 3rem; color: #ffc107;"></i>
                                                    </div>
                                                    <h4 class="card-title text-warning">iOS App Taking Longer Than Expected</h4>
                                                    <p class="card-text">The iOS automation platform is still starting up.</p>
                                                    <div class="d-grid gap-2">
                                                        <a href="http://localhost:{{ ios_port }}/?session_token={{ access_token }}&return_url={{ dashboard_url }}&user_email={{ user_email }}" class="btn btn-primary" target="_blank">
                                                            <i class="bi bi-box-arrow-up-right"></i> Open iOS App Manually
                                                        </a>
                                                        <button onclick="location.reload()" class="btn btn-outline-primary">
                                                            <i class="bi bi-arrow-clockwise"></i> Check Again
                                                        </button>
                                                        <a href="{{ url_for('dashboard') }}" class="btn btn-outline-secondary">
                                                            <i class="bi bi-arrow-left"></i> Back to Dashboard
                                                        </a>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </body>
                        </html>
                        """,
                        ios_port=ios_port,
                        user_email=user.user.email,
                        access_token=session.get('access_token'),
                        dashboard_url=f"http://localhost:{request.environ.get('SERVER_PORT', '8080')}/dashboard")
                else:
                    # Process failed to start
                    stdout, stderr = process.communicate()
                    error_msg = f"Process failed to start. Exit code: {process.returncode}"
                    if stderr:
                        error_msg += f"\nError: {stderr}"
                    logger.error(f"iOS app launch failed: {error_msg}")
                    flash(f'Failed to launch iOS tools: {error_msg}', 'error')

            except Exception as e:
                logger.error(f"Failed to launch iOS script: {e}")
                flash(f'Failed to launch iOS tools: {e}', 'error')
        else:
            logger.error(f"iOS script not found at: {ios_script}")
            flash('iOS automation tools not found. Please check the installation.', 'warning')

        # If we get here, something went wrong, redirect back to dashboard
        return redirect(url_for('dashboard'))

    except Exception as e:
        logger.error(f"iOS app launch error: {e}")
        flash(f'Error launching iOS app: {e}', 'error')
        return redirect(url_for('dashboard'))

@app.route('/logout')
def logout():
    """Logout and redirect to home"""
    session.clear()
    flash('You have been logged out successfully.', 'info')
    return redirect(url_for('index'))

# Error handlers
@app.errorhandler(404)
def not_found(error):
    return render_template('index.html'), 404

@app.errorhandler(500)
def internal_error(error):
    logger.error(f"Internal server error: {error}")
    return jsonify({'error': 'Internal server error'}), 500

def run_server(host='localhost', port=8080, debug=False):
    """Run the Flask server"""
    try:
        logger.info(f"Starting web server on http://{host}:{port}")
        app.run(host=host, port=port, debug=debug, threaded=True)
    except Exception as e:
        logger.error(f"Failed to start web server: {e}")
        raise

if __name__ == '__main__':
    # Get host and port from environment or use defaults
    host = os.getenv('FLASK_HOST', 'localhost')
    port = int(os.getenv('FLASK_PORT', 8080))
    debug = os.getenv('FLASK_DEBUG', 'False').lower() == 'true'

    print(f"🌐 Starting Flask web server...")
    print(f"📍 URL: http://{host}:{port}")
    print(f"📋 Login page: http://{host}:{port}/login")
    print(f"🎯 Dashboard: http://{host}:{port}/dashboard")

    run_server(host=host, port=port, debug=debug)
