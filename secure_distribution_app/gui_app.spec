# -*- mode: python ; coding: utf-8 -*-
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(SPEC)))

a = Analysis(
    ['main.py'],
    pathex=[os.path.dirname(os.path.abspath(SPEC))],
    binaries=[],
    datas=[
        ('gui', 'gui'),
        ('auth', 'auth'),
        ('security', 'security'),
        ('downloader', 'downloader'),
        ('launcher', 'launcher'),
        ('.env', '.'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'tkinter.font',
        'tkinter.scrolledtext',
        '_tkinter',
        'supabase',
        'cryptography',
        'cryptography.fernet',
        'cryptography.hazmat.primitives',
        'cryptography.hazmat.primitives.kdf.pbkdf2',
        'cryptography.hazmat.primitives.hashes',
        'pystray',
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        'psutil',
        'threading',
        'urllib.request',
        'urllib.parse',
        'base64',
        'hashlib',
        'tempfile',
        'zipfile',
        'shutil',
        'platform',
        'webbrowser',
        'subprocess',
        'json',
        'pathlib',
        'os',
        'sys',
        'time',
        'logging',
        'uuid',
        'ctypes',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

# Filter out any problematic binaries
a.binaries = [x for x in a.binaries if not x[0].startswith('tk') or 'tcl' not in x[0].lower()]

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='MobileAppAutomation',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=True,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

# Create macOS app bundle
app = BUNDLE(
    exe,
    name='MobileAppAutomation.app',
    icon=None,
    bundle_identifier='com.mobileautomation.app',
    info_plist={
        'CFBundleName': 'Mobile App Automation',
        'CFBundleDisplayName': 'Mobile App Automation',
        'CFBundleVersion': '2.0.0',
        'CFBundleShortVersionString': '2.0.0',
        'NSHighResolutionCapable': True,
        'LSUIElement': False,
    },
)
