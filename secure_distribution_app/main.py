#!/usr/bin/env python3
"""
Secure Mobile App Distribution Client

This is the main entry point for the secure distribution client that allows
subscribers to access mobile app automation tools through a secure, encrypted
distribution system.

Features:
- Secure authentication with cloud server
- Encrypted file download and storage
- Anti-reverse engineering protection
- Cross-platform compatibility
- Automatic app launching
"""

import sys
import os
import logging
import tkinter as tk
from tkinter import messagebox
import threading
import signal
from pathlib import Path

# Add the current directory to Python path for imports
current_dir = Path(__file__).parent
sys.path.insert(0, str(current_dir))

# Load environment variables from .env file
def load_environment():
    """Load environment variables from .env file"""
    try:
        # For PyInstaller, check multiple locations
        possible_locations = [
            current_dir / '.env',  # Source directory
            Path(sys.executable).parent / '.env',  # Executable directory
            Path(os.getcwd()) / '.env',  # Current working directory
        ]

        # If running as PyInstaller bundle, check the bundle directory
        if getattr(sys, 'frozen', False):
            bundle_dir = Path(sys._MEIPASS)
            possible_locations.insert(0, bundle_dir / '.env')
            print(f"🔍 Running as PyInstaller bundle, checking: {bundle_dir}")

        env_loaded = False
        for env_file in possible_locations:
            if env_file.exists():
                print(f"📁 Loading environment from: {env_file}")
                with open(env_file, 'r') as f:
                    for line in f:
                        line = line.strip()
                        if line and not line.startswith('#') and '=' in line:
                            key, value = line.split('=', 1)
                            os.environ[key.strip()] = value.strip()
                print("✅ Environment variables loaded successfully")
                env_loaded = True
                break

        if not env_loaded:
            print("⚠️ No .env file found in any location")
            print("📍 Searched locations:")
            for loc in possible_locations:
                print(f"   - {loc}")

        # Verify critical environment variables
        supabase_url = os.environ.get('SUPABASE_URL')
        supabase_key = os.environ.get('SUPABASE_ANON_KEY')

        if supabase_url and supabase_key:
            print(f"✅ SUPABASE_URL: {supabase_url[:30]}...")
            print(f"✅ SUPABASE_ANON_KEY: {supabase_key[:30]}...")
        else:
            print("❌ Missing Supabase credentials in environment")

    except Exception as e:
        print(f"❌ Failed to load .env file: {e}")
        import traceback
        traceback.print_exc()

# Load environment variables before importing other modules
load_environment()

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('secure_app.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Import application modules
try:
    from gui.login_window import LoginWindow
    from gui.download_interface import DownloadInterface
    from gui.platform_selector import PlatformSelector
    from gui.system_tray import SystemTrayManager, is_system_tray_available
    from auth.session_manager import SessionManager
    from auth.license_manager import LicenseManager
    from security.integrity import IntegrityChecker
    from security.obfuscation import RuntimeProtection
    from downloader.hidden_link_downloader import HiddenLinkDownloader
except ImportError as e:
    logger.error(f"Failed to import required modules: {e}")
    messagebox.showerror("Error", f"Application initialization failed: {e}")
    sys.exit(1)

class SecureDistributionApp:
    """Main application class for the secure distribution client"""
    
    def __init__(self):
        self.root = None
        self.current_window = None
        self.session_manager = None
        self.license_manager = None
        self.integrity_checker = None
        self.runtime_protection = None
        self.downloader = None
        self.system_tray = None
        self.is_running = False
        self.is_minimized_to_tray = False

        # Package state
        self.package_path = None
        
    def initialize_security(self):
        """Initialize security components"""
        try:
            # Initialize integrity checker
            self.integrity_checker = IntegrityChecker()
            if not self.integrity_checker.verify_application_integrity():
                logger.error("Application integrity check failed")
                messagebox.showerror("Security Error", 
                    "Application integrity verification failed. Please reinstall the application.")
                return False
            
            # Initialize runtime protection
            self.runtime_protection = RuntimeProtection()
            if not self.runtime_protection.initialize():
                logger.error("Runtime protection initialization failed")
                messagebox.showerror("Security Error", 
                    "Security initialization failed. Application cannot continue.")
                return False
            
            logger.info("Security components initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"Security initialization failed: {e}")
            messagebox.showerror("Security Error", 
                f"Security initialization failed: {e}")
            return False
    
    def initialize_gui(self):
        """Initialize the GUI components"""
        try:
            # Create main window
            self.root = tk.Tk()
            self.root.withdraw()  # Hide main window initially
            
            # Configure window properties
            self.root.title("Mobile App Automation - Secure Access")
            self.root.geometry("800x600")
            self.root.resizable(True, True)
            
            # Set window icon (if available)
            try:
                icon_path = current_dir / "assets" / "icon.ico"
                if icon_path.exists():
                    self.root.iconbitmap(str(icon_path))
            except Exception:
                pass  # Icon not critical
            
            # Initialize session manager
            try:
                self.session_manager = SessionManager()
                logger.info("Session manager initialized successfully")
            except Exception as e:
                logger.error(f"Failed to initialize session manager: {e}")
                # Create a mock session manager for debugging
                self.session_manager = None
                logger.warning("Continuing without session manager for debugging")

            # Initialize license manager
            try:
                if self.session_manager and self.session_manager.supabase:
                    self.license_manager = LicenseManager(self.session_manager.supabase)
                else:
                    self.license_manager = None
                    logger.warning("License manager not initialized - no Supabase connection")
            except Exception as e:
                logger.error(f"Failed to initialize license manager: {e}")
                self.license_manager = None

            # Initialize downloader
            try:
                self.downloader = HiddenLinkDownloader(self.session_manager)
            except Exception as e:
                logger.error(f"Failed to initialize downloader: {e}")
                self.downloader = None

            # Initialize system tray if available
            if is_system_tray_available():
                self.system_tray = SystemTrayManager(
                    session_manager=self.session_manager,
                    app_launcher=None,  # No app launcher needed
                    on_show_callback=self.show_from_tray,
                    on_exit_callback=self.exit_from_tray,
                    parent_root=self.root  # Pass the main root window
                )

            # Show login window
            self.show_login_window()
            
            logger.info("GUI initialized successfully")
            return True
            
        except Exception as e:
            logger.error(f"GUI initialization failed: {e}")
            messagebox.showerror("Error", f"GUI initialization failed: {e}")
            return False
    
    def show_login_window(self):
        """Show the login window"""
        try:
            if self.current_window:
                self.current_window.destroy()
            
            self.current_window = LoginWindow(
                parent=self.root,
                session_manager=self.session_manager,
                on_login_success=self.on_login_success,
                on_register_success=self.on_register_success
            )
            
            # Center the login window
            if hasattr(self.current_window, 'window'):
                self.center_window(self.current_window.window, 400, 500)
            else:
                self.center_window(self.current_window, 400, 500)
            
        except Exception as e:
            logger.error(f"Failed to show login window: {e}")
            messagebox.showerror("Error", f"Failed to show login window: {e}")
    
    def show_download_interface(self):
        """Show the download interface"""
        try:
            if self.current_window:
                self.current_window.destroy()

            self.current_window = DownloadInterface(
                parent=self.root,
                session_manager=self.session_manager,
                on_download_complete=self.on_download_complete
            )

            # Show and center the download interface
            self.root.deiconify()
            self.center_window(self.root, 600, 400)

        except Exception as e:
            logger.error(f"Failed to show download interface: {e}")
            messagebox.showerror("Error", f"Failed to show download interface: {e}")

    def show_platform_selector(self):
        """Show the platform selector"""
        try:
            if self.current_window:
                self.current_window.destroy()

            self.current_window = PlatformSelector(
                parent=self.root,
                session_manager=self.session_manager,
                package_path=self.package_path,
                on_minimize_to_tray=self.minimize_to_tray
            )

            # Show and center the platform selector
            self.root.deiconify()
            self.center_window(self.root, 700, 500)

        except Exception as e:
            logger.error(f"Failed to show platform selector: {e}")
            messagebox.showerror("Error", f"Failed to show platform selector: {e}")

    def on_download_complete(self, package_path: str):
        """Handle download completion"""
        try:
            logger.info(f"Download completed: {package_path}")
            self.package_path = package_path

            # Show platform selector
            self.show_platform_selector()

        except Exception as e:
            logger.error(f"Download completion handler failed: {e}")
            messagebox.showerror("Error", f"Download completion handler failed: {e}")
    
    def center_window(self, window, width, height):
        """Center a window on the screen"""
        try:
            # Get screen dimensions
            screen_width = window.winfo_screenwidth()
            screen_height = window.winfo_screenheight()
            
            # Calculate position
            x = (screen_width - width) // 2
            y = (screen_height - height) // 2
            
            # Set window geometry
            window.geometry(f"{width}x{height}+{x}+{y}")
            
        except Exception as e:
            logger.warning(f"Failed to center window: {e}")
    
    def on_login_success(self, user_data):
        """Handle successful login"""
        try:
            logger.info(f"Login successful for user: {user_data.get('email', 'Unknown')}")

            # Check if package is already downloaded
            if self.downloader.is_package_downloaded():
                # Package exists - go directly to platform selector
                self.package_path = self.downloader.get_package_path()
                self.show_platform_selector()
            else:
                # Package not downloaded - show download interface
                self.show_download_interface()

        except Exception as e:
            logger.error(f"Login success handler failed: {e}")
            messagebox.showerror("Error", f"Login success handler failed: {e}")
    
    def on_register_success(self, user_data):
        """Handle successful registration"""
        try:
            logger.info(f"Registration successful for user: {user_data.get('email', 'Unknown')}")
            messagebox.showinfo("Success", 
                "Registration successful! Please log in with your new account.")
            self.show_login_window()
            
        except Exception as e:
            logger.error(f"Registration success handler failed: {e}")
            messagebox.showerror("Error", f"Registration success handler failed: {e}")
    
    def on_logout(self):
        """Handle user logout"""
        try:
            logger.info("User logged out")

            # Clean up current window if it has running processes
            if hasattr(self.current_window, 'stop_all_applications'):
                self.current_window.stop_all_applications()

            # Clear session
            self.session_manager.clear_session()

            # Reset package path
            self.package_path = None

            # Show login window
            self.root.withdraw()
            self.show_login_window()

        except Exception as e:
            logger.error(f"Logout handler failed: {e}")
            messagebox.showerror("Error", f"Logout handler failed: {e}")

    def show_from_tray(self):
        """Show application from system tray"""
        try:
            if self.root:
                self.root.deiconify()
                self.root.lift()
                self.root.focus_force()
                self.is_minimized_to_tray = False

        except Exception as e:
            logger.error(f"Failed to show from tray: {e}")

    def exit_from_tray(self):
        """Exit application from system tray"""
        try:
            self.shutdown()

        except Exception as e:
            logger.error(f"Failed to exit from tray: {e}")

    def minimize_to_tray(self):
        """Minimize application to system tray"""
        try:
            if self.system_tray and is_system_tray_available():
                self.root.withdraw()
                self.is_minimized_to_tray = True
                logger.info("Application minimized to system tray")
                return True
            else:
                logger.warning("System tray not available")
                return False

        except Exception as e:
            logger.error(f"Failed to minimize to tray: {e}")
            return False
    
    def setup_signal_handlers(self):
        """Setup signal handlers for graceful shutdown"""
        def signal_handler(signum, frame):
            logger.info(f"Received signal {signum}, shutting down...")
            self.shutdown()
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def shutdown(self):
        """Graceful shutdown of the application"""
        try:
            logger.info("Shutting down application...")
            self.is_running = False

            # Stop all running processes in current window
            if hasattr(self.current_window, 'stop_all_applications'):
                self.current_window.stop_all_applications()

            # Clean up downloader
            if self.downloader:
                self.downloader.cleanup_downloads()

            # Stop system tray
            if self.system_tray:
                self.system_tray.stop()

            # Clear session data
            if self.session_manager:
                self.session_manager.clear_session()

            # Clean up security components
            if self.runtime_protection:
                self.runtime_protection.cleanup()

            # Destroy GUI
            if self.root:
                self.root.quit()
                self.root.destroy()

            logger.info("Application shutdown complete")

        except Exception as e:
            logger.error(f"Error during shutdown: {e}")
        finally:
            sys.exit(0)
    
    def run(self):
        """Main application entry point"""
        try:
            logger.info("Starting Secure Distribution Application...")
            
            # Initialize security components
            if not self.initialize_security():
                return False
            
            # Initialize GUI
            if not self.initialize_gui():
                return False
            
            # Setup signal handlers
            self.setup_signal_handlers()
            
            # Start the application
            self.is_running = True
            logger.info("Application started successfully")
            
            # Run the main event loop
            self.root.mainloop()
            
        except KeyboardInterrupt:
            logger.info("Application interrupted by user")
            self.shutdown()
        except Exception as e:
            logger.error(f"Application error: {e}")
            messagebox.showerror("Critical Error", f"Application error: {e}")
            self.shutdown()
            return False
        
        return True

def main():
    """Application entry point - immediately start GUI"""
    try:
        # Hide console window on Windows
        if os.name == 'nt':
            try:
                import ctypes
                ctypes.windll.user32.ShowWindow(ctypes.windll.kernel32.GetConsoleWindow(), 0)
            except:
                pass

        # Create and run the application
        app = SecureDistributionApp()
        success = app.run()

        if not success:
            sys.exit(1)

    except Exception as e:
        logger.error(f"Critical application error: {e}")
        try:
            messagebox.showerror("Critical Error",
                f"A critical error occurred: {e}\n\nThe application will now exit.")
        except:
            pass  # GUI might not be available
        sys.exit(1)

if __name__ == "__main__":
    main()
