# -*- mode: python ; coding: utf-8 -*-

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('gui', 'gui'),
        ('auth', 'auth'),
        ('security', 'security'),
        ('downloader', 'downloader'),
        ('launcher', 'launcher'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'tkinter.font',
        'tkinter.scrolledtext',
        'supabase',
        'cryptography',
        'pystray',
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        'psutil',
        'threading',
        'urllib.request',
        'urllib.parse',
        'base64',
        'hashlib',
        'tempfile',
        'zipfile',
        'shutil',
        'platform',
        'webbrowser',
        'subprocess',
        'json',
        'pathlib',
        'os',
        'sys',
        'time',
        'logging',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

pyz = PYZ(a.pure)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='MobileAppAutomation',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,  # Disable UPX as it can cause issues with tkinter
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=True,  # Enable for macOS
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

app = BUNDLE(
    exe,
    name='MobileAppAutomation.app',
    icon=None,
    bundle_identifier='com.mobileautomation.app',
)
