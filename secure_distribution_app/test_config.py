#!/usr/bin/env python3
"""
Test Configuration Loading

This script tests if the configuration is loading correctly.
"""

import sys
import os
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

def test_environment_loading():
    """Test environment variable loading"""
    print("🔍 Testing Environment Loading")
    print("=" * 40)
    
    # Test 1: Check if .env file exists
    env_file = Path('.env')
    print(f"📁 .env file exists: {env_file.exists()}")
    if env_file.exists():
        print(f"📍 .env file location: {env_file.absolute()}")
    
    # Test 2: Load environment manually
    if env_file.exists():
        print("\n📋 .env file contents:")
        with open(env_file, 'r') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#'):
                    if '=' in line:
                        key, value = line.split('=', 1)
                        print(f"   {line_num}: {key.strip()} = {value.strip()[:30]}...")
                        # Set in environment
                        os.environ[key.strip()] = value.strip()
    
    # Test 3: Check environment variables
    print("\n🔧 Environment Variables:")
    supabase_url = os.environ.get('SUPABASE_URL')
    supabase_key = os.environ.get('SUPABASE_ANON_KEY')
    
    if supabase_url:
        print(f"✅ SUPABASE_URL: {supabase_url[:30]}...")
    else:
        print("❌ SUPABASE_URL: Not found")
    
    if supabase_key:
        print(f"✅ SUPABASE_ANON_KEY: {supabase_key[:30]}...")
    else:
        print("❌ SUPABASE_ANON_KEY: Not found")
    
    return supabase_url and supabase_key

def test_config_module():
    """Test the config module"""
    print("\n🔍 Testing Config Module")
    print("=" * 40)
    
    try:
        from config import get_config, get_supabase_credentials
        
        config = get_config()
        print(f"✅ Config module loaded successfully")
        print(f"📍 Supabase URL: {config.supabase_url[:30]}...")
        print(f"📍 Supabase Key: {config.supabase_anon_key[:30]}...")
        print(f"📍 App Environment: {config.app_env}")
        print(f"📍 Development Mode: {config.development_mode}")
        
        credentials = get_supabase_credentials()
        print(f"✅ Credentials: URL={credentials['url'][:30]}..., Key={credentials['key'][:30]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Config module failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_session_manager():
    """Test the session manager initialization"""
    print("\n🔍 Testing Session Manager")
    print("=" * 40)
    
    try:
        from auth.session_manager import SessionManager
        
        session_manager = SessionManager()
        print(f"✅ SessionManager initialized successfully")
        print(f"📍 Supabase URL: {session_manager.supabase_url[:30]}...")
        print(f"📍 Supabase Key: {session_manager.supabase_key[:30]}...")
        
        if session_manager.supabase:
            print("✅ Supabase client created successfully")
        else:
            print("❌ Supabase client is None")
        
        return True
        
    except Exception as e:
        print(f"❌ SessionManager failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_supabase_connection():
    """Test actual Supabase connection"""
    print("\n🔍 Testing Supabase Connection")
    print("=" * 40)
    
    try:
        from auth.session_manager import SessionManager
        
        session_manager = SessionManager()
        
        # Try a simple query
        result = session_manager.supabase.table('user_profiles').select('*').limit(1).execute()
        print(f"✅ Supabase connection successful")
        print(f"📊 Query result: {len(result.data)} rows")
        
        return True
        
    except Exception as e:
        print(f"❌ Supabase connection failed: {e}")
        return False

def main():
    """Main test function"""
    print("🧪 Configuration and Connection Test")
    print("=" * 50)
    
    tests = [
        ("Environment Loading", test_environment_loading),
        ("Config Module", test_config_module),
        ("Session Manager", test_session_manager),
        ("Supabase Connection", test_supabase_connection),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 Test Results Summary")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Configuration is working correctly.")
        return True
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
