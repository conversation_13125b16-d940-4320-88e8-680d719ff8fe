#!/usr/bin/env python3
"""
Debug tkinter Installation

This script tests tkinter functionality step by step.
"""

import sys
import os

def test_tkinter_import():
    """Test tkinter import"""
    print("🔍 Testing tkinter import...")
    try:
        import tkinter as tk
        from tkinter import ttk
        print("✅ tkinter imported successfully")
        print(f"tkinter version: {tk.TkVersion}")
        print(f"tcl version: {tk.TclVersion}")
        return True
    except Exception as e:
        print(f"❌ tkinter import failed: {e}")
        return False

def test_basic_window():
    """Test basic window creation"""
    print("🔍 Testing basic window creation...")
    try:
        import tkinter as tk
        
        # Suppress deprecation warning
        os.environ['TK_SILENCE_DEPRECATION'] = '1'
        
        root = tk.Tk()
        root.title("Basic Test")
        root.geometry("300x200")
        
        label = tk.Label(root, text="Basic Window Test", font=("Arial", 12))
        label.pack(pady=20)
        
        print("✅ Basic window created")
        
        # Close immediately
        root.after(100, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Basic window test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ttk_widgets():
    """Test ttk widgets"""
    print("🔍 Testing ttk widgets...")
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # Suppress deprecation warning
        os.environ['TK_SILENCE_DEPRECATION'] = '1'
        
        root = tk.Tk()
        root.title("TTK Test")
        root.geometry("400x300")
        
        # Create ttk frame
        frame = ttk.Frame(root, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        # Create ttk widgets
        ttk.Label(frame, text="TTK Label Test").pack(pady=5)
        ttk.Entry(frame, width=30).pack(pady=5)
        ttk.Button(frame, text="TTK Button").pack(pady=5)
        
        print("✅ TTK widgets created")
        
        # Close immediately
        root.after(100, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ TTK widgets test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_toplevel_window():
    """Test Toplevel window"""
    print("🔍 Testing Toplevel window...")
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # Suppress deprecation warning
        os.environ['TK_SILENCE_DEPRECATION'] = '1'
        
        root = tk.Tk()
        root.title("Root")
        root.geometry("200x100")
        root.withdraw()  # Hide root
        
        # Create toplevel
        toplevel = tk.Toplevel(root)
        toplevel.title("Toplevel Test")
        toplevel.geometry("300x200")
        
        # Add content
        frame = ttk.Frame(toplevel, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(frame, text="Toplevel Window Working!").pack(pady=10)
        ttk.Button(frame, text="Close", command=root.destroy).pack(pady=10)
        
        print("✅ Toplevel window created")
        
        # Close after short delay
        root.after(500, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Toplevel window test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_modal_window():
    """Test modal window"""
    print("🔍 Testing modal window...")
    try:
        import tkinter as tk
        from tkinter import ttk
        
        # Suppress deprecation warning
        os.environ['TK_SILENCE_DEPRECATION'] = '1'
        
        root = tk.Tk()
        root.title("Root")
        root.geometry("200x100")
        root.withdraw()  # Hide root
        
        # Create modal window
        modal = tk.Toplevel(root)
        modal.title("Modal Test")
        modal.geometry("350x250")
        modal.resizable(False, False)
        
        # Make modal
        modal.transient(root)
        modal.grab_set()
        
        # Add content
        frame = ttk.Frame(modal, padding="20")
        frame.pack(fill=tk.BOTH, expand=True)
        
        ttk.Label(frame, text="Modal Window Test", font=("Arial", 14, "bold")).pack(pady=10)
        ttk.Label(frame, text="This is a modal window").pack(pady=5)
        
        # Form elements
        ttk.Label(frame, text="Test Field:").pack(anchor=tk.W, pady=(10, 2))
        entry = ttk.Entry(frame, width=25)
        entry.pack(pady=(0, 10))
        entry.insert(0, "Test value")
        
        ttk.Button(frame, text="Close", command=root.destroy).pack(pady=10)
        
        print("✅ Modal window created")
        
        # Close after delay
        root.after(1000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Modal window test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 tkinter Debug Tests")
    print("=" * 30)
    
    # Suppress deprecation warning globally
    os.environ['TK_SILENCE_DEPRECATION'] = '1'
    
    tests = [
        ("tkinter Import", test_tkinter_import),
        ("Basic Window", test_basic_window),
        ("TTK Widgets", test_ttk_widgets),
        ("Toplevel Window", test_toplevel_window),
        ("Modal Window", test_modal_window),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            result = test_func()
            results.append((test_name, result))
            status = "✅ PASS" if result else "❌ FAIL"
            print(f"{status} {test_name}")
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n📊 Test Results")
    print("=" * 20)
    
    passed = 0
    for test_name, result in results:
        status = "✅" if result else "❌"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 tkinter is working correctly!")
    else:
        print("⚠️ tkinter has issues. Check the output above.")
    
    return passed == len(results)

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
