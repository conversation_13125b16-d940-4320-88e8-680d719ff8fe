"""
Secure Download Manager for Distribution Application

Handles secure downloading and decryption of mobile app automation packages from Google Drive.
"""

import os
import json
import tempfile
import logging
from pathlib import Path
from typing import Dict, Any, List, Optional, Callable
import hashlib
import zipfile
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import base64
import shutil
import threading
import time
from .google_drive_client import GoogleDriveClient, get_file_id_from_url

logger = logging.getLogger(__name__)

class SecureDownloader:
    """Handles secure downloading and decryption of app packages from Google Drive"""

    def __init__(self, session_manager, google_drive_config: Dict[str, str] = None):
        self.session_manager = session_manager
        self.download_dir = Path(tempfile.gettempdir()) / 'secure_app_downloads'
        self.download_dir.mkdir(exist_ok=True)

        # Google Drive configuration
        self.google_drive_config = google_drive_config or {}
        self.drive_client = None

        # Initialize Google Drive client
        self.initialize_drive_client()

        # Cache for downloaded apps
        self.app_cache = {}

        # Download tracking
        self.active_downloads = {}
        self.download_callbacks = {}

    def initialize_drive_client(self):
        """Initialize Google Drive client"""
        try:
            credentials_file = self.google_drive_config.get('credentials_file') or os.environ.get('GOOGLE_CREDENTIALS_FILE')
            service_account_file = self.google_drive_config.get('service_account_file') or os.environ.get('GOOGLE_SERVICE_ACCOUNT_FILE')

            self.drive_client = GoogleDriveClient(credentials_file=credentials_file)

            # Try service account authentication if available
            if service_account_file and os.path.exists(service_account_file):
                if self.drive_client.authenticate_with_service_account(service_account_file):
                    logger.info("Google Drive client initialized with service account")
                    return

            # Try regular OAuth authentication
            if self.drive_client.initialize_service():
                logger.info("Google Drive client initialized successfully")
            else:
                logger.warning("Google Drive client initialization failed")

        except Exception as e:
            logger.error(f"Failed to initialize Google Drive client: {e}")
            self.drive_client = None

    def get_available_apps(self) -> List[Dict[str, Any]]:
        """Get list of available apps from configuration"""
        try:
            if not self.session_manager.is_authenticated():
                raise Exception("User not authenticated")

            # Check license validity
            if not self.session_manager.is_license_valid():
                raise Exception("License expired or invalid")

            # Load app configuration from file
            config_file = Path(__file__).parent.parent / 'config' / 'apps_config.json'

            if not config_file.exists():
                # Create default configuration
                default_apps = self._create_default_apps_config()
                config_file.parent.mkdir(parents=True, exist_ok=True)
                with open(config_file, 'w') as f:
                    json.dump({'apps': default_apps}, f, indent=2)

                logger.info("Created default apps configuration")
                return default_apps

            # Load existing configuration
            with open(config_file, 'r') as f:
                config = json.load(f)

            apps = config.get('apps', [])
            logger.info(f"Retrieved {len(apps)} available apps from configuration")
            return apps

        except Exception as e:
            logger.error(f"Failed to get available apps: {e}")
            raise

    def _create_default_apps_config(self) -> List[Dict[str, Any]]:
        """Create default apps configuration"""
        return [
            {
                'id': 'ios_automation',
                'name': 'iOS Mobile App Automation',
                'description': 'Complete iOS mobile app testing and automation platform with Appium integration, element detection, and test recording capabilities.',
                'platform': 'ios',
                'version': '1.0.0',
                'google_drive_file_id': os.environ.get('IOS_APP_DRIVE_FILE_ID', ''),
                'file_size': 50 * 1024 * 1024,  # 50MB estimate
                'is_active': True
            },
            {
                'id': 'android_automation',
                'name': 'Android Mobile App Automation',
                'description': 'Complete Android mobile app testing and automation platform with Appium integration, element detection, and test recording capabilities.',
                'platform': 'android',
                'version': '1.0.0',
                'google_drive_file_id': os.environ.get('ANDROID_APP_DRIVE_FILE_ID', ''),
                'file_size': 50 * 1024 * 1024,  # 50MB estimate
                'is_active': True
            },
            {
                'id': 'unified_automation',
                'name': 'Mobile App Automation Suite',
                'description': 'Complete mobile app testing and automation platform supporting both iOS and Android with Appium integration, element detection, test recording, and comprehensive reporting.',
                'platform': 'both',
                'version': '1.0.0',
                'google_drive_file_id': os.environ.get('UNIFIED_APP_DRIVE_FILE_ID', ''),
                'file_size': 100 * 1024 * 1024,  # 100MB estimate
                'is_active': True
            }
        ]
    
    def download_app(self, app_id: str, progress_callback: Callable[[int, int], None] = None) -> Optional[str]:
        """Download and extract an app package from Google Drive"""
        try:
            if not self.session_manager.is_authenticated():
                raise Exception("User not authenticated")

            # Check license validity
            if not self.session_manager.is_license_valid():
                raise Exception("License expired or invalid")

            # Check if app is already downloaded and cached
            if app_id in self.app_cache:
                cached_path = self.app_cache[app_id]
                if os.path.exists(cached_path):
                    logger.info(f"Using cached app: {app_id}")
                    return cached_path

            # Get app configuration
            apps = self.get_available_apps()
            app_config = next((app for app in apps if app['id'] == app_id), None)

            if not app_config:
                raise Exception(f"App configuration not found: {app_id}")

            if not app_config.get('is_active', True):
                raise Exception(f"App is not available: {app_id}")

            google_drive_file_id = app_config.get('google_drive_file_id')
            if not google_drive_file_id:
                raise Exception(f"Google Drive file ID not configured for app: {app_id}")

            logger.info(f"Downloading app from Google Drive: {app_config['name']}")

            # Download from Google Drive
            downloaded_file_path = self._download_from_google_drive(
                google_drive_file_id, app_id, progress_callback
            )

            if not downloaded_file_path:
                return None

            # Extract and process package
            extracted_path = self._extract_and_process_package(downloaded_file_path, app_id)

            if extracted_path:
                # Cache the extracted path
                self.app_cache[app_id] = extracted_path

                # Clean up downloaded file
                try:
                    os.remove(downloaded_file_path)
                except:
                    pass

                logger.info(f"Successfully downloaded and processed app: {app_id}")
                return extracted_path

            return None

        except Exception as e:
            logger.error(f"Failed to download app {app_id}: {e}")
            raise

    def _download_from_google_drive(self, file_id: str, app_id: str,
                                   progress_callback: Callable[[int, int], None] = None) -> Optional[str]:
        """Download file from Google Drive"""
        try:
            if not self.drive_client:
                raise Exception("Google Drive client not initialized")

            if not self.drive_client.is_service_available():
                raise Exception("Google Drive service not available")

            # Create download path
            download_path = self.download_dir / f"{app_id}_package.zip"

            # Track download
            download_id = f"{app_id}_{int(time.time())}"
            self.active_downloads[download_id] = {
                'app_id': app_id,
                'status': 'downloading',
                'progress': 0
            }

            # Wrapper for progress callback
            def drive_progress_callback(downloaded: int, total: int):
                if download_id in self.active_downloads:
                    self.active_downloads[download_id]['progress'] = downloaded

                if progress_callback:
                    progress_callback(downloaded, total)

            # Download file
            success = self.drive_client.download_file(
                file_id, str(download_path), drive_progress_callback
            )

            if success:
                self.active_downloads[download_id]['status'] = 'completed'
                logger.info(f"Successfully downloaded from Google Drive: {download_path}")
                return str(download_path)
            else:
                self.active_downloads[download_id]['status'] = 'failed'
                raise Exception("Google Drive download failed")

        except Exception as e:
            logger.error(f"Google Drive download error: {e}")
            if download_id in self.active_downloads:
                self.active_downloads[download_id]['status'] = 'failed'
            raise

    def _extract_and_process_package(self, package_path: str, app_id: str) -> Optional[str]:
        """Extract and process downloaded package"""
        try:
            package_file = Path(package_path)

            # Create extraction directory
            extract_dir = self.download_dir / f"{app_id}_extracted"
            if extract_dir.exists():
                shutil.rmtree(extract_dir)
            extract_dir.mkdir()

            # Extract package
            logger.info(f"Extracting package: {package_file}")

            if package_file.suffix.lower() == '.zip':
                with zipfile.ZipFile(package_file, 'r') as zip_file:
                    zip_file.extractall(extract_dir)
            else:
                # Handle other archive formats if needed
                raise Exception(f"Unsupported package format: {package_file.suffix}")

            # Apply security measures to extracted files
            self._apply_security_measures(extract_dir)

            logger.info(f"Package extracted and processed: {extract_dir}")
            return str(extract_dir)

        except Exception as e:
            logger.error(f"Package extraction error: {e}")
            raise

    def _apply_security_measures(self, extract_dir: Path):
        """Apply security measures to extracted files"""
        try:
            # Hide directory (make it less visible)
            if os.name == 'nt':  # Windows
                import ctypes
                ctypes.windll.kernel32.SetFileAttributesW(str(extract_dir), 2)  # Hidden attribute

            # Set restrictive permissions
            for root, dirs, files in os.walk(extract_dir):
                for dir_name in dirs:
                    dir_path = Path(root) / dir_name
                    os.chmod(dir_path, 0o700)  # Owner read/write/execute only

                for file_name in files:
                    file_path = Path(root) / file_name
                    if file_path.suffix == '.py':
                        os.chmod(file_path, 0o600)  # Owner read/write only
                    else:
                        os.chmod(file_path, 0o644)  # Owner read/write, others read

            logger.info("Security measures applied to extracted files")

        except Exception as e:
            logger.warning(f"Failed to apply security measures: {e}")

    def get_download_progress(self, app_id: str) -> Dict[str, Any]:
        """Get download progress for an app"""
        for download_id, download_info in self.active_downloads.items():
            if download_info['app_id'] == app_id:
                return download_info
        return {}

    def cancel_download(self, app_id: str):
        """Cancel an ongoing download"""
        for download_id, download_info in self.active_downloads.items():
            if download_info['app_id'] == app_id:
                download_info['status'] = 'cancelled'
                if self.drive_client:
                    self.drive_client.cancel_download(download_id)
    
    def _download_encrypted_package(self, app_id: str) -> Optional[str]:
        """Download the encrypted app package"""
        try:
            response = requests.post(
                f"{self.session_manager.server_url}/api/apps/{app_id}/download",
                headers=self.session_manager.get_auth_headers(),
                timeout=300,  # 5 minutes for large files
                stream=True
            )
            
            if response.status_code == 200:
                # Get filename from headers or use default
                filename = f"app_{app_id}.encrypted"
                file_path = self.download_dir / filename
                
                # Download file with progress tracking
                total_size = int(response.headers.get('content-length', 0))
                downloaded_size = 0
                
                with open(file_path, 'wb') as f:
                    for chunk in response.iter_content(chunk_size=8192):
                        if chunk:
                            f.write(chunk)
                            downloaded_size += len(chunk)
                            
                            # Log progress for large files
                            if total_size > 0 and downloaded_size % (1024 * 1024) == 0:  # Every MB
                                progress = (downloaded_size / total_size) * 100
                                logger.info(f"Download progress: {progress:.1f}%")
                
                logger.info(f"Downloaded encrypted package: {file_path}")
                return str(file_path)
            
            elif response.status_code == 401:
                # Try to refresh token
                if self.session_manager.refresh_access_token():
                    return self._download_encrypted_package(app_id)  # Retry
                else:
                    raise Exception("Authentication expired. Please login again.")
            
            elif response.status_code == 403:
                raise Exception("Access denied. You don't have permission to download this app.")
            
            elif response.status_code == 404:
                raise Exception("App not found or no longer available.")
            
            else:
                raise Exception(f"Download failed: HTTP {response.status_code}")
                
        except Exception as e:
            logger.error(f"Failed to download encrypted package: {e}")
            raise
    
    def _get_decryption_key(self, app_id: str) -> Optional[Dict[str, str]]:
        """Get decryption key for the app"""
        try:
            # Prompt user for password for additional security
            import tkinter as tk
            from tkinter import simpledialog
            
            password = simpledialog.askstring(
                "Security Verification",
                "Please enter your password to decrypt the application:",
                show='*'
            )
            
            if not password:
                raise Exception("Password required for decryption")
            
            response = requests.post(
                f"{self.session_manager.server_url}/api/apps/{app_id}/key",
                json={'password': password},
                headers=self.session_manager.get_auth_headers(),
                timeout=30
            )
            
            if response.status_code == 200:
                key_data = response.json()
                logger.info("Decryption key retrieved successfully")
                return key_data
            
            elif response.status_code == 401:
                raise Exception("Invalid password")
            
            else:
                raise Exception(f"Failed to get decryption key: HTTP {response.status_code}")
                
        except Exception as e:
            logger.error(f"Failed to get decryption key: {e}")
            raise
    
    def _decrypt_and_extract_package(self, encrypted_file_path: str, 
                                   key_data: Dict[str, str], app_id: str) -> Optional[str]:
        """Decrypt and extract the app package"""
        try:
            # Derive decryption key from key data
            key_bytes = base64.b64decode(key_data['key_data'])
            salt = bytes.fromhex(key_data['salt'])
            
            # Create Fernet cipher
            fernet = Fernet(key_bytes)
            
            # Decrypt the file
            with open(encrypted_file_path, 'rb') as encrypted_file:
                encrypted_data = encrypted_file.read()
            
            logger.info("Decrypting package...")
            decrypted_data = fernet.decrypt(encrypted_data)
            
            # Create temporary decrypted file
            decrypted_file_path = self.download_dir / f"app_{app_id}_decrypted.zip"
            with open(decrypted_file_path, 'wb') as decrypted_file:
                decrypted_file.write(decrypted_data)
            
            # Extract the package
            extract_dir = self.download_dir / f"app_{app_id}_extracted"
            extract_dir.mkdir(exist_ok=True)
            
            logger.info("Extracting package...")
            with zipfile.ZipFile(decrypted_file_path, 'r') as zip_file:
                zip_file.extractall(extract_dir)
            
            # Clean up decrypted zip file
            try:
                os.remove(decrypted_file_path)
            except:
                pass
            
            logger.info(f"Package extracted to: {extract_dir}")
            return str(extract_dir)
            
        except Exception as e:
            logger.error(f"Failed to decrypt and extract package: {e}")
            raise
    
    def verify_package_integrity(self, package_path: str, expected_hash: str) -> bool:
        """Verify the integrity of a downloaded package"""
        try:
            # Calculate hash of the package directory
            package_hash = self._calculate_directory_hash(package_path)
            
            if package_hash == expected_hash:
                logger.info("Package integrity verification passed")
                return True
            else:
                logger.error("Package integrity verification failed")
                return False
                
        except Exception as e:
            logger.error(f"Package integrity verification error: {e}")
            return False
    
    def _calculate_directory_hash(self, directory_path: str) -> str:
        """Calculate hash of all files in a directory"""
        try:
            sha256_hash = hashlib.sha256()
            
            for root, dirs, files in os.walk(directory_path):
                # Sort to ensure consistent ordering
                dirs.sort()
                files.sort()
                
                for file in files:
                    file_path = os.path.join(root, file)
                    with open(file_path, 'rb') as f:
                        for chunk in iter(lambda: f.read(4096), b""):
                            sha256_hash.update(chunk)
            
            return sha256_hash.hexdigest()
            
        except Exception as e:
            logger.error(f"Failed to calculate directory hash: {e}")
            return ""
    
    def cleanup_downloads(self):
        """Clean up downloaded files"""
        try:
            import shutil
            
            if self.download_dir.exists():
                shutil.rmtree(self.download_dir)
                logger.info("Download directory cleaned up")
            
            self.app_cache.clear()
            
        except Exception as e:
            logger.warning(f"Failed to cleanup downloads: {e}")
    
    def get_cached_apps(self) -> List[str]:
        """Get list of cached app IDs"""
        return list(self.app_cache.keys())
    
    def is_app_cached(self, app_id: str) -> bool:
        """Check if an app is cached locally"""
        if app_id in self.app_cache:
            cached_path = self.app_cache[app_id]
            return os.path.exists(cached_path)
        return False
    
    def get_app_path(self, app_id: str) -> Optional[str]:
        """Get the local path of a cached app"""
        if self.is_app_cached(app_id):
            return self.app_cache[app_id]
        return None
