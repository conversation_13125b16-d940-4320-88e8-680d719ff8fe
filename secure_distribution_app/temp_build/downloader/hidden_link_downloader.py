"""
Hidden Link Downloader for Secure Distribution Application

Downloads packages from obfuscated/encrypted Google Drive direct links without using the API.
Links are hidden from network traffic inspection through encryption and obfuscation.
"""

import os
import logging
import hashlib
import tempfile
import urllib.request
import urllib.parse
from pathlib import Path
from typing import Dict, Any, Optional, Callable
import base64
import zipfile
import shutil
from cryptography.fernet import <PERSON><PERSON><PERSON>
from cryptography.hazmat.primitives import hashes
from cryptography.hazmat.primitives.kdf.pbkdf2 import PBKDF2HMAC
import threading
import time

logger = logging.getLogger(__name__)

class HiddenLinkDownloader:
    """Downloads packages from encrypted/obfuscated Google Drive direct links"""
    
    def __init__(self, session_manager):
        self.session_manager = session_manager
        self.download_dir = Path(tempfile.gettempdir()) / '.secure_app_cache'
        self.download_dir.mkdir(exist_ok=True)
        
        # Hide the download directory on Windows
        if os.name == 'nt':
            try:
                import ctypes
                ctypes.windll.kernel32.SetFileAttributesW(str(self.download_dir), 2)  # Hidden
            except:
                pass
        
        # Download tracking
        self.active_downloads = {}
        
        # Initialize encryption key for link obfuscation
        self.link_key = self._generate_link_key()
        
        # Encrypted/obfuscated download links (these would be your actual Google Drive direct links)
        self.encrypted_links = self._get_encrypted_links()
    
    def _generate_link_key(self) -> bytes:
        """Generate encryption key for link obfuscation"""
        try:
            # Use a combination of application-specific data to generate key
            key_material = "MobileAppAutomation2024SecureDistribution"
            
            # Add hardware-specific component for additional security
            import platform
            key_material += platform.machine() + platform.system()
            
            # Generate key using PBKDF2
            kdf = PBKDF2HMAC(
                algorithm=hashes.SHA256(),
                length=32,
                salt=b'secure_distribution_salt_2024',
                iterations=100000,
            )
            key = base64.urlsafe_b64encode(kdf.derive(key_material.encode()))
            return key
            
        except Exception as e:
            logger.error(f"Failed to generate link key: {e}")
            # Fallback key (in production, this should be more secure)
            return base64.urlsafe_b64encode(b'fallback_key_for_link_encryption_32')
    
    def _get_encrypted_links(self) -> Dict[str, str]:
        """Get encrypted/obfuscated download links"""
        # These are encrypted Google Drive direct download links
        # In production, replace these with your actual encrypted links
        
        # Example of how to encrypt a Google Drive direct link:
        # 1. Get the direct download link from Google Drive (e.g., https://drive.google.com/uc?export=download&id=FILE_ID)
        # 2. Encrypt it using the link key
        # 3. Store the encrypted version here
        
        encrypted_links = {
            # This is an example encrypted link - replace with your actual encrypted Google Drive links
            'unified_package': 'gAAAAABon64V7UbPvgwD0sKMLz3EI9YY4rTFY7FkozMoiQmGJwq4ieFeWQJ2en7dk-rPrj0d286fpDI-HoIS9ZgxbA69c3A5oFliZ-B1w8B_By1eEwxoJc2a-foHSu1vSwyIJkeZmORa4XRMhMQbKIabcLlHN4eQyNxyrgLazPeM9DY1kGKJiiZ5j0J_KU1pwDnsuM9O9JhI',
        }
        
        return encrypted_links
    
    def _decrypt_link(self, encrypted_link: str) -> str:
        """Decrypt an obfuscated download link"""
        try:
            fernet = Fernet(self.link_key)
            decrypted_bytes = fernet.decrypt(encrypted_link.encode())
            return decrypted_bytes.decode()
            
        except Exception as e:
            logger.error(f"Failed to decrypt link: {e}")
            # Return a placeholder link for testing
            return "https://example.com/placeholder_download_link"
    
    def _encrypt_link(self, plain_link: str) -> str:
        """Encrypt a download link for storage (utility function)"""
        try:
            fernet = Fernet(self.link_key)
            encrypted_bytes = fernet.encrypt(plain_link.encode())
            return encrypted_bytes.decode()
            
        except Exception as e:
            logger.error(f"Failed to encrypt link: {e}")
            return ""
    
    def get_available_packages(self) -> Dict[str, Any]:
        """Get information about available packages"""
        return {
            'unified_package': {
                'id': 'unified_automation',
                'name': 'Mobile App Automation Suite',
                'description': 'Complete iOS and Android mobile app testing and automation platform',
                'platforms': ['ios', 'android'],
                'version': '1.0.0',
                'estimated_size': 100 * 1024 * 1024,  # 100MB estimate
                'is_active': True
            }
        }
    
    def download_package(self, package_id: str = 'unified_package', 
                        progress_callback: Callable[[int, int], None] = None) -> Optional[str]:
        """Download the unified package from hidden Google Drive link"""
        try:
            if not self.session_manager.is_authenticated():
                raise Exception("User not authenticated")
            
            # Check license validity
            if not self.session_manager.is_license_valid():
                raise Exception("License expired or invalid")
            
            # Get encrypted link
            if package_id not in self.encrypted_links:
                raise Exception(f"Package not found: {package_id}")
            
            encrypted_link = self.encrypted_links[package_id]
            
            # Decrypt the download link
            download_url = self._decrypt_link(encrypted_link)
            
            logger.info(f"Starting download for package: {package_id}")
            
            # Create download path
            download_path = self.download_dir / f"{package_id}_package.zip"
            
            # Track download
            download_id = f"{package_id}_{int(time.time())}"
            self.active_downloads[download_id] = {
                'package_id': package_id,
                'status': 'downloading',
                'progress': 0,
                'total_size': 0,
                'downloaded': 0
            }
            
            # Download with progress tracking
            success = self._download_with_progress(
                download_url, str(download_path), download_id, progress_callback
            )
            
            if success:
                self.active_downloads[download_id]['status'] = 'completed'
                logger.info(f"Successfully downloaded package: {download_path}")
                return str(download_path)
            else:
                self.active_downloads[download_id]['status'] = 'failed'
                raise Exception("Download failed")
                
        except Exception as e:
            logger.error(f"Package download error: {e}")
            if download_id in self.active_downloads:
                self.active_downloads[download_id]['status'] = 'failed'
            raise
    
    def _download_with_progress(self, url: str, destination: str, download_id: str,
                               progress_callback: Callable[[int, int], None] = None) -> bool:
        """Download file with progress tracking"""
        try:
            # Create custom opener to handle redirects and avoid detection
            opener = urllib.request.build_opener()
            opener.addheaders = [
                ('User-Agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'),
                ('Accept', 'text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8'),
                ('Accept-Language', 'en-US,en;q=0.5'),
                ('Accept-Encoding', 'gzip, deflate'),
                ('Connection', 'keep-alive'),
            ]
            
            # Open URL
            response = opener.open(url)
            
            # Get file size
            total_size = int(response.headers.get('Content-Length', 0))
            self.active_downloads[download_id]['total_size'] = total_size
            
            # Download in chunks
            chunk_size = 8192
            downloaded = 0
            
            with open(destination, 'wb') as f:
                while True:
                    chunk = response.read(chunk_size)
                    if not chunk:
                        break
                    
                    f.write(chunk)
                    downloaded += len(chunk)
                    
                    # Update progress
                    self.active_downloads[download_id]['downloaded'] = downloaded
                    
                    if progress_callback and total_size > 0:
                        progress_callback(downloaded, total_size)
                    
                    # Log progress for large files
                    if total_size > 0 and downloaded % (1024 * 1024) == 0:  # Every MB
                        progress_percent = (downloaded / total_size) * 100
                        logger.info(f"Download progress: {progress_percent:.1f}%")
            
            # Verify download completed
            if total_size > 0 and downloaded != total_size:
                logger.warning(f"Download size mismatch: expected {total_size}, got {downloaded}")
            
            return True
            
        except Exception as e:
            logger.error(f"Download error: {e}")
            return False
    
    def extract_package(self, package_path: str) -> Optional[str]:
        """Extract downloaded package to secure location"""
        try:
            package_file = Path(package_path)
            
            # Create extraction directory in user's app data
            if os.name == 'nt':  # Windows
                app_data = Path(os.environ.get('APPDATA', '')) / 'MobileAppAutomation'
            else:  # macOS/Linux
                app_data = Path.home() / '.mobile_app_automation'
            
            app_data.mkdir(parents=True, exist_ok=True)
            
            # Hide directory on Windows
            if os.name == 'nt':
                try:
                    import ctypes
                    ctypes.windll.kernel32.SetFileAttributesW(str(app_data), 2)  # Hidden
                except:
                    pass
            
            extract_dir = app_data / 'automation_tools'
            
            # Remove existing installation
            if extract_dir.exists():
                shutil.rmtree(extract_dir)
            
            extract_dir.mkdir()
            
            # Extract package
            logger.info(f"Extracting package to: {extract_dir}")
            
            with zipfile.ZipFile(package_file, 'r') as zip_file:
                zip_file.extractall(extract_dir)
            
            # Verify extraction
            if not any(extract_dir.iterdir()):
                raise Exception("Package extraction failed - no files extracted")
            
            # Set appropriate permissions
            self._set_secure_permissions(extract_dir)
            
            # Create integrity marker
            self._create_integrity_marker(extract_dir)
            
            logger.info(f"Package extracted successfully to: {extract_dir}")
            return str(extract_dir)
            
        except Exception as e:
            logger.error(f"Package extraction error: {e}")
            raise
    
    def _set_secure_permissions(self, directory: Path):
        """Set secure permissions on extracted files"""
        try:
            for root, dirs, files in os.walk(directory):
                # Set directory permissions
                for dir_name in dirs:
                    dir_path = Path(root) / dir_name
                    os.chmod(dir_path, 0o700)  # Owner only
                
                # Set file permissions
                for file_name in files:
                    file_path = Path(root) / file_name
                    if file_path.suffix in ['.py', '.sh']:
                        os.chmod(file_path, 0o700)  # Executable for owner only
                    else:
                        os.chmod(file_path, 0o600)  # Read/write for owner only
            
            logger.info("Secure permissions applied to extracted files")
            
        except Exception as e:
            logger.warning(f"Failed to set secure permissions: {e}")
    
    def _create_integrity_marker(self, directory: Path):
        """Create integrity marker for extracted package"""
        try:
            # Calculate directory hash
            dir_hash = self._calculate_directory_hash(directory)
            
            # Create integrity file
            integrity_file = directory / '.integrity'
            with open(integrity_file, 'w') as f:
                f.write(f"{dir_hash}\n{time.time()}")
            
            # Hide integrity file
            if os.name == 'nt':
                try:
                    import ctypes
                    ctypes.windll.kernel32.SetFileAttributesW(str(integrity_file), 2)  # Hidden
                except:
                    pass
            
            logger.info("Integrity marker created")
            
        except Exception as e:
            logger.warning(f"Failed to create integrity marker: {e}")
    
    def _calculate_directory_hash(self, directory: Path) -> str:
        """Calculate hash of directory contents"""
        try:
            sha256_hash = hashlib.sha256()
            
            for root, dirs, files in os.walk(directory):
                dirs.sort()  # Ensure consistent ordering
                files.sort()
                
                for file in files:
                    if file == '.integrity':  # Skip integrity file itself
                        continue
                    
                    file_path = Path(root) / file
                    with open(file_path, 'rb') as f:
                        for chunk in iter(lambda: f.read(4096), b""):
                            sha256_hash.update(chunk)
            
            return sha256_hash.hexdigest()
            
        except Exception as e:
            logger.error(f"Failed to calculate directory hash: {e}")
            return ""
    
    def verify_package_integrity(self, directory: Path) -> bool:
        """Verify integrity of extracted package"""
        try:
            integrity_file = directory / '.integrity'
            if not integrity_file.exists():
                logger.warning("Integrity marker not found")
                return False
            
            # Read stored hash
            with open(integrity_file, 'r') as f:
                stored_hash = f.readline().strip()
            
            # Calculate current hash
            current_hash = self._calculate_directory_hash(directory)
            
            if current_hash == stored_hash:
                logger.info("Package integrity verification passed")
                return True
            else:
                logger.error("Package integrity verification failed")
                return False
                
        except Exception as e:
            logger.error(f"Integrity verification error: {e}")
            return False
    
    def is_package_downloaded(self) -> bool:
        """Check if package is already downloaded and extracted"""
        try:
            if os.name == 'nt':  # Windows
                app_data = Path(os.environ.get('APPDATA', '')) / 'MobileAppAutomation'
            else:  # macOS/Linux
                app_data = Path.home() / '.mobile_app_automation'
            
            extract_dir = app_data / 'automation_tools'
            
            if not extract_dir.exists():
                return False
            
            # Check if directory has content
            if not any(extract_dir.iterdir()):
                return False
            
            # Verify integrity
            return self.verify_package_integrity(extract_dir)
            
        except Exception as e:
            logger.error(f"Error checking package status: {e}")
            return False
    
    def get_package_path(self) -> Optional[str]:
        """Get path to extracted package"""
        try:
            if os.name == 'nt':  # Windows
                app_data = Path(os.environ.get('APPDATA', '')) / 'MobileAppAutomation'
            else:  # macOS/Linux
                app_data = Path.home() / '.mobile_app_automation'
            
            extract_dir = app_data / 'automation_tools'
            
            if self.is_package_downloaded():
                return str(extract_dir)
            
            return None
            
        except Exception as e:
            logger.error(f"Error getting package path: {e}")
            return None
    
    def cleanup_downloads(self):
        """Clean up temporary download files"""
        try:
            if self.download_dir.exists():
                for file in self.download_dir.glob('*_package.zip'):
                    file.unlink()
                
            self.active_downloads.clear()
            logger.info("Download cleanup completed")
            
        except Exception as e:
            logger.warning(f"Download cleanup error: {e}")

# Utility function to encrypt Google Drive links for storage
def encrypt_google_drive_link(plain_link: str) -> str:
    """Utility function to encrypt Google Drive direct download links"""
    try:
        downloader = HiddenLinkDownloader(None)
        return downloader._encrypt_link(plain_link)
    except Exception as e:
        logger.error(f"Link encryption error: {e}")
        return ""

# Instructions for setting up encrypted links:
"""
To set up your encrypted Google Drive links:

1. Get Google Drive direct download links:
   - Upload your package to Google Drive
   - Right-click -> Get link -> Change to "Anyone with the link can view"
   - Convert sharing link to direct download link:
     From: https://drive.google.com/file/d/FILE_ID/view?usp=sharing
     To: https://drive.google.com/uc?export=download&id=FILE_ID

2. Encrypt the links:
   python -c "
   from secure_distribution_app.downloader.hidden_link_downloader import encrypt_google_drive_link
   link = 'https://drive.google.com/uc?export=download&id=YOUR_FILE_ID'
   encrypted = encrypt_google_drive_link(link)
   print(f'Encrypted link: {encrypted}')
   "

3. Replace the encrypted_links in _get_encrypted_links() method with your encrypted links
"""
