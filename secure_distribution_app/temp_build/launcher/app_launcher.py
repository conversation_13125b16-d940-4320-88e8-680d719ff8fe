"""
App Launcher for Secure Distribution Application

Handles launching of downloaded mobile app automation tools.
"""

import os
import sys
import subprocess
import logging
import time
import webbrowser
import threading
from pathlib import Path
from typing import Dict, Any, Optional
import json
import signal
import psutil

logger = logging.getLogger(__name__)

class AppLauncher:
    """Handles launching of mobile app automation applications"""
    
    def __init__(self):
        self.running_processes = {}
        self.app_configs = {}
        
    def launch_app(self, app_data: Dict[str, Any]) -> bool:
        """Launch an app using cached data"""
        try:
            app_id = app_data.get('id')
            if not app_id:
                raise Exception("App ID not provided")
            
            # Check if app is already running
            if self.is_app_running(app_id):
                logger.info(f"App {app_id} is already running")
                return True
            
            # Get app path from downloader cache
            from downloader.secure_download import SecureDownloader
            downloader = SecureDownloader(None)  # No session manager needed for cached apps
            
            app_path = downloader.get_app_path(app_id)
            if not app_path:
                raise Exception("App not found in cache. Please download first.")
            
            return self.launch_downloaded_app(app_path, app_data)
            
        except Exception as e:
            logger.error(f"Failed to launch app: {e}")
            return False
    
    def launch_downloaded_app(self, app_path: str, app_data: Dict[str, Any]) -> bool:
        """Launch a downloaded and extracted app"""
        try:
            app_id = app_data.get('id')
            app_name = app_data.get('name', 'Unknown App')
            platform = app_data.get('platform', 'unknown')
            
            logger.info(f"Launching app: {app_name} ({platform})")
            
            # Find the main application file
            main_script = self._find_main_script(app_path, platform)
            if not main_script:
                raise Exception("Main application script not found")
            
            # Prepare launch configuration
            launch_config = self._prepare_launch_config(app_path, platform, app_data)
            
            # Launch the application
            process = self._launch_process(main_script, launch_config, app_path)
            
            if process:
                # Store process information
                self.running_processes[app_id] = {
                    'process': process,
                    'app_data': app_data,
                    'launch_time': time.time(),
                    'app_path': app_path
                }
                
                # Wait a moment for the app to start
                time.sleep(3)
                
                # Open browser to the app URL
                self._open_app_in_browser(launch_config)
                
                logger.info(f"Successfully launched {app_name}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Failed to launch downloaded app: {e}")
            return False
    
    def _find_main_script(self, app_path: str, platform: str) -> Optional[str]:
        """Find the main script to launch the app"""
        try:
            app_dir = Path(app_path)
            
            # Common main script names
            main_script_names = [
                'run.py',
                'main.py',
                'app.py',
                'start.py',
                f'run_{platform}.py'
            ]
            
            # Look for main script
            for script_name in main_script_names:
                script_path = app_dir / script_name
                if script_path.exists():
                    logger.info(f"Found main script: {script_path}")
                    return str(script_path)
            
            # Look in subdirectories
            for subdir in ['app', 'src', platform]:
                subdir_path = app_dir / subdir
                if subdir_path.exists():
                    for script_name in main_script_names:
                        script_path = subdir_path / script_name
                        if script_path.exists():
                            logger.info(f"Found main script in subdirectory: {script_path}")
                            return str(script_path)
            
            logger.error("Main script not found")
            return None
            
        except Exception as e:
            logger.error(f"Error finding main script: {e}")
            return None
    
    def _prepare_launch_config(self, app_path: str, platform: str, app_data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare launch configuration for the app"""
        try:
            # Find available ports
            flask_port = self._find_available_port(8090)
            appium_port = self._find_available_port(4730)
            
            # Prepare configuration
            config = {
                'flask_port': flask_port,
                'appium_port': appium_port,
                'platform': platform,
                'app_name': app_data.get('name', 'Mobile App'),
                'app_path': app_path,
                'url': f"http://localhost:{flask_port}"
            }
            
            # Platform-specific configuration
            if platform.lower() == 'ios':
                config['wda_port'] = self._find_available_port(8100)
            
            logger.info(f"Launch configuration: {config}")
            return config
            
        except Exception as e:
            logger.error(f"Failed to prepare launch config: {e}")
            return {}
    
    def _find_available_port(self, start_port: int) -> int:
        """Find an available port starting from the given port"""
        import socket
        
        for port in range(start_port, start_port + 100):
            try:
                with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
                    s.bind(('localhost', port))
                    return port
            except OSError:
                continue
        
        # Fallback to a random port
        with socket.socket(socket.AF_INET, socket.SOCK_STREAM) as s:
            s.bind(('localhost', 0))
            return s.getsockname()[1]
    
    def _launch_process(self, main_script: str, config: Dict[str, Any], app_path: str) -> Optional[subprocess.Popen]:
        """Launch the application process"""
        try:
            # Prepare command
            cmd = [
                sys.executable,
                main_script,
                '--flask-port', str(config['flask_port']),
                '--appium-port', str(config['appium_port'])
            ]
            
            # Add platform-specific arguments
            if config.get('wda_port'):
                cmd.extend(['--wda-port', str(config['wda_port'])])
            
            # Prepare environment
            env = os.environ.copy()
            env['PYTHONPATH'] = app_path
            
            # Set working directory to app path
            working_dir = app_path
            
            logger.info(f"Launching command: {' '.join(cmd)}")
            logger.info(f"Working directory: {working_dir}")
            
            # Launch process
            process = subprocess.Popen(
                cmd,
                cwd=working_dir,
                env=env,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                bufsize=1,
                universal_newlines=True
            )
            
            # Start output monitoring in background
            self._start_output_monitoring(process, config['app_name'])
            
            return process
            
        except Exception as e:
            logger.error(f"Failed to launch process: {e}")
            return None
    
    def _start_output_monitoring(self, process: subprocess.Popen, app_name: str):
        """Start monitoring process output"""
        def monitor_output():
            try:
                while process.poll() is None:
                    output = process.stdout.readline()
                    if output:
                        logger.info(f"[{app_name}] {output.strip()}")
                
                # Log any remaining output
                remaining_output = process.stdout.read()
                if remaining_output:
                    logger.info(f"[{app_name}] {remaining_output.strip()}")
                
            except Exception as e:
                logger.error(f"Output monitoring error for {app_name}: {e}")
        
        threading.Thread(target=monitor_output, daemon=True).start()
    
    def _open_app_in_browser(self, config: Dict[str, Any]):
        """Open the app URL in the default browser"""
        try:
            url = config.get('url')
            if url:
                logger.info(f"Opening browser to: {url}")
                
                # Wait a bit more for the server to start
                time.sleep(2)
                
                # Open in browser
                webbrowser.open(url)
                
        except Exception as e:
            logger.error(f"Failed to open browser: {e}")
    
    def is_app_running(self, app_id: str) -> bool:
        """Check if an app is currently running"""
        if app_id not in self.running_processes:
            return False
        
        process_info = self.running_processes[app_id]
        process = process_info['process']
        
        # Check if process is still running
        if process.poll() is None:
            return True
        else:
            # Process has terminated, remove from tracking
            del self.running_processes[app_id]
            return False
    
    def stop_app(self, app_id: str) -> bool:
        """Stop a running app"""
        try:
            if app_id not in self.running_processes:
                logger.warning(f"App {app_id} is not running")
                return False
            
            process_info = self.running_processes[app_id]
            process = process_info['process']
            app_name = process_info['app_data'].get('name', 'Unknown App')
            
            logger.info(f"Stopping app: {app_name}")
            
            # Try graceful shutdown first
            process.terminate()
            
            # Wait for graceful shutdown
            try:
                process.wait(timeout=10)
            except subprocess.TimeoutExpired:
                # Force kill if graceful shutdown fails
                logger.warning(f"Force killing app: {app_name}")
                process.kill()
                process.wait()
            
            # Remove from tracking
            del self.running_processes[app_id]
            
            logger.info(f"Successfully stopped app: {app_name}")
            return True
            
        except Exception as e:
            logger.error(f"Failed to stop app {app_id}: {e}")
            return False
    
    def stop_all_apps(self):
        """Stop all running apps"""
        try:
            app_ids = list(self.running_processes.keys())
            
            for app_id in app_ids:
                self.stop_app(app_id)
            
            logger.info("All apps stopped")
            
        except Exception as e:
            logger.error(f"Failed to stop all apps: {e}")
    
    def get_running_apps(self) -> Dict[str, Dict[str, Any]]:
        """Get information about currently running apps"""
        running_apps = {}
        
        for app_id, process_info in self.running_processes.items():
            if self.is_app_running(app_id):
                running_apps[app_id] = {
                    'app_data': process_info['app_data'],
                    'launch_time': process_info['launch_time'],
                    'pid': process_info['process'].pid
                }
        
        return running_apps
    
    def cleanup(self):
        """Clean up launcher resources"""
        try:
            logger.info("Cleaning up app launcher...")
            self.stop_all_apps()
            
        except Exception as e:
            logger.error(f"Launcher cleanup error: {e}")

# Signal handler for graceful shutdown
def signal_handler(signum, frame):
    """Handle shutdown signals"""
    logger.info(f"Received signal {signum}, shutting down apps...")
    
    # Get global launcher instance if available
    import sys
    if hasattr(sys.modules[__name__], 'global_launcher'):
        global_launcher = getattr(sys.modules[__name__], 'global_launcher')
        global_launcher.cleanup()

# Register signal handlers
signal.signal(signal.SIGINT, signal_handler)
signal.signal(signal.SIGTERM, signal_handler)
