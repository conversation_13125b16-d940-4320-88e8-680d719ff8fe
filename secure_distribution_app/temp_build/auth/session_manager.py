"""
Session Manager for Secure Distribution Application

Handles Supabase authentication, license management, and secure session handling.
"""

import os
import json
import hashlib
import platform
import logging
from typing import Dict, Any, Optional
from pathlib import Path
import tempfile
import uuid
from datetime import datetime, timedelta
from supabase import create_client, Client
import threading
import time

logger = logging.getLogger(__name__)

class SessionManager:
    """Manages user sessions and Supabase authentication with license management"""

    def __init__(self, supabase_url: str = None, supabase_key: str = None):
        # Supabase configuration
        self.supabase_url = supabase_url or os.environ.get('SUPABASE_URL')
        self.supabase_key = supabase_key or os.environ.get('SUPABASE_ANON_KEY')

        if not self.supabase_url or not self.supabase_key:
            raise ValueError("Supabase URL and key must be provided via environment variables or parameters")

        # Initialize Supabase client
        self.supabase: Client = create_client(self.supabase_url, self.supabase_key)

        # Session management
        self.session_file = Path(tempfile.gettempdir()) / '.secure_app_session'
        self.current_user = None
        self.current_session = None
        self.license_info = None

        # Background monitoring
        self.monitoring_active = False
        self.monitoring_thread = None

        # Load existing session if available
        self.load_session()
    
    def generate_client_fingerprint(self) -> str:
        """Generate a unique client fingerprint for security"""
        try:
            # Combine system information to create a unique fingerprint
            system_info = {
                'platform': platform.platform(),
                'processor': platform.processor(),
                'machine': platform.machine(),
                'node': platform.node()
            }
            
            # Create hash of system information
            fingerprint_data = json.dumps(system_info, sort_keys=True)
            fingerprint = hashlib.sha256(fingerprint_data.encode()).hexdigest()
            
            return fingerprint
            
        except Exception as e:
            logger.warning(f"Failed to generate client fingerprint: {e}")
            # Fallback to random UUID
            return str(uuid.uuid4())
    
    def login(self, email: str, password: str, license_number: str = None) -> Dict[str, Any]:
        """Authenticate user with Supabase and validate license"""
        try:
            logger.info(f"Attempting login for user: {email}")

            # Authenticate with Supabase
            auth_response = self.supabase.auth.sign_in_with_password({
                "email": email,
                "password": password
            })

            if auth_response.user:
                self.current_user = auth_response.user
                self.current_session = auth_response.session

                # Get user profile and license information
                license_validation = self.validate_license(license_number)

                if license_validation.get('valid'):
                    # Update last login timestamp
                    self.update_last_login()

                    # Save session to file
                    self.save_session()

                    # Start background monitoring
                    self.start_license_monitoring()

                    logger.info(f"Login successful for user: {email}")
                    return {
                        'success': True,
                        'user_data': {
                            'id': self.current_user.id,
                            'email': self.current_user.email,
                            'user_metadata': self.current_user.user_metadata
                        },
                        'license_info': self.license_info
                    }
                else:
                    # Sign out if license is invalid
                    self.supabase.auth.sign_out()
                    return {
                        'success': False,
                        'error': license_validation.get('error', 'License validation failed')
                    }
            else:
                return {
                    'success': False,
                    'error': 'Invalid email or password'
                }

        except Exception as e:
            logger.error(f"Login error: {e}")
            error_message = str(e)

            # Handle specific Supabase errors
            if 'Invalid login credentials' in error_message:
                return {
                    'success': False,
                    'error': 'Invalid email or password'
                }
            elif 'Email not confirmed' in error_message:
                return {
                    'success': False,
                    'error': 'Please confirm your email address before logging in'
                }
            elif 'Too many requests' in error_message:
                return {
                    'success': False,
                    'error': 'Too many login attempts. Please try again later.'
                }
            else:
                return {
                    'success': False,
                    'error': f'Login failed: {error_message}'
                }

    def validate_license(self, license_number: str = None) -> Dict[str, Any]:
        """Validate user license and check expiry"""
        try:
            if not self.current_user:
                return {'valid': False, 'error': 'User not authenticated'}

            # Get user profile from database
            user_profile = self.supabase.table('user_profiles').select('*').eq('user_id', self.current_user.id).execute()

            if not user_profile.data:
                return {'valid': False, 'error': 'User profile not found'}

            profile = user_profile.data[0]

            # Check if license number is provided and matches
            if license_number and profile.get('license_number') != license_number:
                return {'valid': False, 'error': 'Invalid license number'}

            # Check license expiry
            license_expiry = profile.get('license_expiry')
            if license_expiry:
                expiry_date = datetime.fromisoformat(license_expiry.replace('Z', '+00:00'))
                current_date = datetime.now(expiry_date.tzinfo)

                if current_date > expiry_date:
                    return {'valid': False, 'error': 'License has expired'}

                # Check for grace period (7 days before expiry)
                grace_period = expiry_date - timedelta(days=7)
                if current_date > grace_period:
                    days_remaining = (expiry_date - current_date).days
                    self.license_info = {
                        'status': 'warning',
                        'message': f'License expires in {days_remaining} days',
                        'expiry_date': license_expiry,
                        'days_remaining': days_remaining
                    }
                else:
                    self.license_info = {
                        'status': 'active',
                        'expiry_date': license_expiry,
                        'days_remaining': (expiry_date - current_date).days
                    }
            else:
                # No expiry date set
                self.license_info = {
                    'status': 'active',
                    'expiry_date': None,
                    'days_remaining': None
                }

            return {'valid': True, 'license_info': self.license_info}

        except Exception as e:
            logger.error(f"License validation error: {e}")
            return {'valid': False, 'error': f'License validation failed: {str(e)}'}

    def update_last_login(self):
        """Update last login timestamp in database"""
        try:
            if self.current_user:
                self.supabase.table('user_profiles').update({
                    'last_login': datetime.utcnow().isoformat()
                }).eq('user_id', self.current_user.id).execute()

        except Exception as e:
            logger.warning(f"Failed to update last login: {e}")

    def start_license_monitoring(self):
        """Start background license monitoring"""
        try:
            if not self.monitoring_active:
                self.monitoring_active = True
                self.monitoring_thread = threading.Thread(target=self._license_monitoring_loop, daemon=True)
                self.monitoring_thread.start()
                logger.info("License monitoring started")

        except Exception as e:
            logger.error(f"Failed to start license monitoring: {e}")

    def _license_monitoring_loop(self):
        """Background loop for license monitoring"""
        while self.monitoring_active:
            try:
                if self.current_user:
                    # Check license validity every 5 minutes
                    validation_result = self.validate_license()

                    if not validation_result.get('valid'):
                        logger.warning("License validation failed during monitoring")
                        self.handle_license_expiry()
                        break

                # Sleep for 5 minutes
                time.sleep(300)

            except Exception as e:
                logger.error(f"License monitoring error: {e}")
                time.sleep(60)  # Retry after 1 minute on error

    def handle_license_expiry(self):
        """Handle license expiry by terminating session"""
        try:
            logger.warning("License expired - terminating session")
            self.monitoring_active = False

            # Clear session
            self.clear_session()

            # Notify the application about license expiry
            # This would trigger UI updates and app termination

        except Exception as e:
            logger.error(f"Error handling license expiry: {e}")

    def stop_license_monitoring(self):
        """Stop background license monitoring"""
        try:
            self.monitoring_active = False
            if self.monitoring_thread and self.monitoring_thread.is_alive():
                self.monitoring_thread.join(timeout=2.0)
            logger.info("License monitoring stopped")

        except Exception as e:
            logger.error(f"Error stopping license monitoring: {e}")
    
    def register(self, email: str, password: str, first_name: str, last_name: str,
                 license_number: str = None) -> Dict[str, Any]:
        """Register a new user with Supabase"""
        try:
            logger.info(f"Attempting registration for user: {email}")

            # Register with Supabase Auth
            auth_response = self.supabase.auth.sign_up({
                "email": email,
                "password": password,
                "options": {
                    "data": {
                        "first_name": first_name,
                        "last_name": last_name
                    }
                }
            })

            if auth_response.user:
                # Create user profile in database
                profile_data = {
                    'user_id': auth_response.user.id,
                    'email': email,
                    'first_name': first_name,
                    'last_name': last_name,
                    'license_number': license_number,
                    'created_at': datetime.utcnow().isoformat(),
                    'is_active': True
                }

                # Insert user profile
                self.supabase.table('user_profiles').insert(profile_data).execute()

                logger.info(f"Registration successful for user: {email}")
                return {
                    'success': True,
                    'user_data': {
                        'id': auth_response.user.id,
                        'email': email,
                        'first_name': first_name,
                        'last_name': last_name
                    },
                    'message': 'Registration successful. Please check your email to confirm your account.'
                }
            else:
                return {
                    'success': False,
                    'error': 'Registration failed. Please try again.'
                }

        except Exception as e:
            logger.error(f"Registration error: {e}")
            error_message = str(e)

            # Handle specific Supabase errors
            if 'User already registered' in error_message:
                return {
                    'success': False,
                    'error': 'An account with this email already exists'
                }
            elif 'Password should be at least' in error_message:
                return {
                    'success': False,
                    'error': 'Password must be at least 6 characters long'
                }
            elif 'Invalid email' in error_message:
                return {
                    'success': False,
                    'error': 'Please enter a valid email address'
                }
            else:
                return {
                    'success': False,
                    'error': f'Registration failed: {error_message}'
                }
    
    def validate_session(self) -> Dict[str, Any]:
        """Validate current session with the server"""
        try:
            if not self.access_token:
                return {'success': False, 'error': 'No access token'}
            
            # Prepare validation data
            validation_data = {
                'client_fingerprint': self.generate_client_fingerprint()
            }
            
            # Make validation request
            response = requests.post(
                f"{self.server_url}/api/auth/validate",
                json=validation_data,
                timeout=30,
                headers={
                    'Content-Type': 'application/json',
                    'Authorization': f'Bearer {self.access_token}'
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                
                if data.get('valid'):
                    self.session_token = data.get('session_token')
                    self.current_user = data.get('user')
                    
                    logger.info("Session validation successful")
                    return {
                        'success': True,
                        'user_data': self.current_user
                    }
            
            return {
                'success': False,
                'error': 'Session validation failed'
            }
            
        except Exception as e:
            logger.error(f"Session validation error: {e}")
            return {
                'success': False,
                'error': f'Session validation failed: {str(e)}'
            }
    
    def refresh_access_token(self) -> bool:
        """Refresh the access token using the refresh token"""
        try:
            if not self.refresh_token:
                return False
            
            response = requests.post(
                f"{self.server_url}/api/auth/refresh",
                timeout=30,
                headers={
                    'Authorization': f'Bearer {self.refresh_token}'
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                self.access_token = data.get('access_token')
                self.save_session()
                
                logger.info("Access token refreshed successfully")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Token refresh error: {e}")
            return False
    
    def save_session(self):
        """Save session data to file"""
        try:
            if self.current_session and self.current_user:
                session_data = {
                    'access_token': self.current_session.access_token,
                    'refresh_token': self.current_session.refresh_token,
                    'user_id': self.current_user.id,
                    'user_email': self.current_user.email,
                    'license_info': self.license_info,
                    'saved_at': datetime.utcnow().isoformat()
                }

                with open(self.session_file, 'w') as f:
                    json.dump(session_data, f)

                # Set restrictive permissions on session file
                os.chmod(self.session_file, 0o600)
                logger.info("Session saved to file")

        except Exception as e:
            logger.warning(f"Failed to save session: {e}")

    def load_session(self):
        """Load session data from file and validate"""
        try:
            if self.session_file.exists():
                with open(self.session_file, 'r') as f:
                    session_data = json.load(f)

                # Check if session is recent (within 24 hours)
                saved_at = session_data.get('saved_at')
                if saved_at:
                    saved_time = datetime.fromisoformat(saved_at)
                    if datetime.utcnow() - saved_time > timedelta(hours=24):
                        logger.info("Session expired, clearing")
                        self.clear_session()
                        return

                # Try to restore session with Supabase
                access_token = session_data.get('access_token')
                if access_token:
                    try:
                        # Set the session in Supabase client
                        self.supabase.auth.set_session(access_token, session_data.get('refresh_token'))

                        # Get current user
                        user_response = self.supabase.auth.get_user()
                        if user_response.user:
                            self.current_user = user_response.user
                            self.current_session = self.supabase.auth.get_session()
                            self.license_info = session_data.get('license_info')

                            # Validate license
                            license_validation = self.validate_license()
                            if license_validation.get('valid'):
                                logger.info("Session restored successfully")
                                self.start_license_monitoring()
                            else:
                                logger.warning("License validation failed, clearing session")
                                self.clear_session()
                        else:
                            logger.warning("Failed to get user from restored session")
                            self.clear_session()

                    except Exception as e:
                        logger.warning(f"Failed to restore session: {e}")
                        self.clear_session()

        except Exception as e:
            logger.warning(f"Failed to load session: {e}")
            self.clear_session()

    def clear_session(self):
        """Clear current session data and sign out"""
        try:
            # Stop license monitoring
            self.stop_license_monitoring()

            # Sign out from Supabase
            if self.current_session:
                try:
                    self.supabase.auth.sign_out()
                except:
                    pass  # Ignore errors during sign out

            # Clear local data
            self.current_user = None
            self.current_session = None
            self.license_info = None

            # Remove session file
            if self.session_file.exists():
                self.session_file.unlink()

            logger.info("Session cleared")

        except Exception as e:
            logger.warning(f"Failed to clear session: {e}")

    def is_authenticated(self) -> bool:
        """Check if user is currently authenticated"""
        return (self.current_user is not None and
                self.current_session is not None and
                self.license_info is not None)
    
    def get_auth_headers(self) -> Dict[str, str]:
        """Get authentication headers for API requests"""
        if self.current_session and self.current_session.access_token:
            return {
                'Authorization': f'Bearer {self.current_session.access_token}',
                'Content-Type': 'application/json'
            }
        return {'Content-Type': 'application/json'}

    def get_user_info(self) -> Dict[str, Any]:
        """Get current user information"""
        if self.current_user:
            return {
                'id': self.current_user.id,
                'email': self.current_user.email,
                'user_metadata': self.current_user.user_metadata or {},
                'license_info': self.license_info
            }
        return {}

    def refresh_session(self) -> bool:
        """Refresh the current session"""
        try:
            if self.current_session:
                refresh_response = self.supabase.auth.refresh_session()
                if refresh_response.session:
                    self.current_session = refresh_response.session
                    self.save_session()
                    logger.info("Session refreshed successfully")
                    return True

            return False

        except Exception as e:
            logger.error(f"Session refresh error: {e}")
            return False

    def get_license_status(self) -> Dict[str, Any]:
        """Get current license status"""
        if self.license_info:
            return self.license_info
        return {'status': 'unknown'}

    def is_license_valid(self) -> bool:
        """Check if current license is valid"""
        if not self.license_info:
            return False

        status = self.license_info.get('status')
        return status in ['active', 'warning']

    def get_license_expiry_warning(self) -> Optional[str]:
        """Get license expiry warning message if applicable"""
        if self.license_info and self.license_info.get('status') == 'warning':
            return self.license_info.get('message')
        return None
