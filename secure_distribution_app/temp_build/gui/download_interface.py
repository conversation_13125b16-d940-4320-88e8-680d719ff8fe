"""
Download Interface for Secure Distribution Application

Handles the initial download of the unified mobile automation package.
"""

import os
import sys
import logging
import threading
import tkinter as tk
from tkinter import ttk, messagebox
from typing import Callable, Optional
from pathlib import Path

logger = logging.getLogger(__name__)

class DownloadInterface:
    """Download interface for unified package download"""
    
    def __init__(self, parent: tk.Tk, session_manager, on_download_complete: Callable[[str], None]):
        self.parent = parent
        self.session_manager = session_manager
        self.on_download_complete = on_download_complete
        
        # Download state
        self.is_downloading = False
        self.download_thread = None
        self.downloader = None
        
        # Initialize downloader
        self._initialize_downloader()
        
        # Create interface
        self.create_interface()
    
    def _initialize_downloader(self):
        """Initialize the hidden link downloader"""
        try:
            from downloader.hidden_link_downloader import HiddenLinkDownloader
            self.downloader = HiddenLinkDownloader(self.session_manager)
            
        except Exception as e:
            logger.error(f"Failed to initialize downloader: {e}")
            messagebox.showerror("Error", f"Failed to initialize downloader: {e}")
    
    def create_interface(self):
        """Create the download interface"""
        try:
            # Configure the main window
            self.parent.title("Mobile App Automation - Download")
            self.parent.geometry("600x400")
            self.parent.resizable(False, False)
            
            # Main frame
            main_frame = ttk.Frame(self.parent, padding="30")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # Title
            title_label = ttk.Label(
                main_frame,
                text="Mobile App Automation Suite",
                font=("Arial", 18, "bold")
            )
            title_label.pack(pady=(0, 10))
            
            # Subtitle
            subtitle_label = ttk.Label(
                main_frame,
                text="Download Required",
                font=("Arial", 12)
            )
            subtitle_label.pack(pady=(0, 20))
            
            # Description
            description_text = """Welcome to Mobile App Automation Suite!

This package contains complete iOS and Android mobile app testing and automation tools.

To get started, you need to download the automation package. This is a one-time download that includes:

• iOS Mobile App Automation Platform
• Android Mobile App Automation Platform  
• Complete testing and automation tools
• Documentation and quick start guides

The download is approximately 100MB and will be stored securely on your system."""
            
            description_label = ttk.Label(
                main_frame,
                text=description_text,
                font=("Arial", 10),
                justify=tk.LEFT,
                wraplength=500
            )
            description_label.pack(pady=(0, 30))
            
            # Download button frame
            button_frame = ttk.Frame(main_frame)
            button_frame.pack(pady=(0, 20))
            
            # Download button
            self.download_button = ttk.Button(
                button_frame,
                text="Download Mobile Automation Package",
                command=self.start_download,
                style="Accent.TButton"
            )
            self.download_button.pack(pady=10)
            
            # Progress frame (initially hidden)
            self.progress_frame = ttk.Frame(main_frame)
            
            # Progress label
            self.progress_label = ttk.Label(
                self.progress_frame,
                text="Preparing download...",
                font=("Arial", 10)
            )
            self.progress_label.pack(pady=(0, 10))
            
            # Progress bar
            self.progress_bar = ttk.Progressbar(
                self.progress_frame,
                mode='indeterminate',
                length=400
            )
            self.progress_bar.pack(pady=(0, 10))
            
            # Status label
            self.status_label = ttk.Label(
                self.progress_frame,
                text="",
                font=("Arial", 9),
                foreground="gray"
            )
            self.status_label.pack()
            
            # Cancel button (for download)
            self.cancel_button = ttk.Button(
                self.progress_frame,
                text="Cancel",
                command=self.cancel_download
            )
            self.cancel_button.pack(pady=(20, 0))
            
            # Footer
            footer_label = ttk.Label(
                main_frame,
                text="Secure distribution system with license validation",
                font=("Arial", 8),
                foreground="gray"
            )
            footer_label.pack(side=tk.BOTTOM, pady=(20, 0))
            
        except Exception as e:
            logger.error(f"Failed to create download interface: {e}")
            messagebox.showerror("Error", f"Failed to create interface: {e}")
    
    def start_download(self):
        """Start the package download"""
        try:
            if self.is_downloading:
                return
            
            if not self.downloader:
                messagebox.showerror("Error", "Downloader not initialized")
                return
            
            # Validate license before download
            if not self.session_manager.is_authenticated():
                messagebox.showerror("Error", "Authentication required")
                return
            
            if not self.session_manager.is_license_valid():
                messagebox.showerror("Error", "License expired or invalid")
                return
            
            # Show progress interface
            self.show_download_progress(True)
            self.is_downloading = True
            
            # Start download in background thread
            self.download_thread = threading.Thread(target=self._download_worker, daemon=True)
            self.download_thread.start()
            
        except Exception as e:
            logger.error(f"Failed to start download: {e}")
            messagebox.showerror("Error", f"Failed to start download: {e}")
            self.show_download_progress(False)
    
    def _download_worker(self):
        """Background download worker"""
        try:
            # Update status
            self.parent.after(0, lambda: self.update_status("Connecting to download server..."))
            
            # Download package
            package_path = self.downloader.download_package(
                progress_callback=self.update_download_progress
            )
            
            if package_path:
                # Update status
                self.parent.after(0, lambda: self.update_status("Extracting package..."))
                
                # Extract package
                extract_path = self.downloader.extract_package(package_path)
                
                if extract_path:
                    # Clean up download file
                    self.downloader.cleanup_downloads()
                    
                    # Notify completion
                    self.parent.after(0, lambda: self.handle_download_complete(extract_path))
                else:
                    self.parent.after(0, lambda: self.handle_download_error("Package extraction failed"))
            else:
                self.parent.after(0, lambda: self.handle_download_error("Download failed"))
                
        except Exception as e:
            logger.error(f"Download worker error: {e}")
            self.parent.after(0, lambda: self.handle_download_error(str(e)))
    
    def update_download_progress(self, downloaded: int, total: int):
        """Update download progress"""
        try:
            if total > 0:
                progress_percent = (downloaded / total) * 100
                
                # Update progress bar
                self.parent.after(0, lambda: self.progress_bar.config(mode='determinate'))
                self.parent.after(0, lambda: setattr(self.progress_bar, 'value', progress_percent))
                
                # Update status
                downloaded_mb = downloaded / (1024 * 1024)
                total_mb = total / (1024 * 1024)
                status_text = f"Downloaded {downloaded_mb:.1f} MB of {total_mb:.1f} MB ({progress_percent:.1f}%)"
                self.parent.after(0, lambda: self.update_status(status_text))
                
        except Exception as e:
            logger.error(f"Progress update error: {e}")
    
    def update_status(self, message: str):
        """Update status message"""
        try:
            self.status_label.config(text=message)
            
        except Exception as e:
            logger.error(f"Status update error: {e}")
    
    def show_download_progress(self, show: bool):
        """Show or hide download progress"""
        try:
            if show:
                self.download_button.pack_forget()
                self.progress_frame.pack(pady=(0, 20))
                self.progress_bar.start()
            else:
                self.progress_frame.pack_forget()
                self.download_button.pack(pady=10)
                self.progress_bar.stop()
                self.is_downloading = False
                
        except Exception as e:
            logger.error(f"Progress display error: {e}")
    
    def handle_download_complete(self, extract_path: str):
        """Handle successful download completion"""
        try:
            self.show_download_progress(False)
            
            logger.info(f"Package download and extraction completed: {extract_path}")
            
            # Show success message
            messagebox.showinfo(
                "Download Complete",
                "Mobile App Automation Suite has been downloaded and installed successfully!\n\n"
                "You can now launch iOS or Android automation tools."
            )
            
            # Notify parent of completion
            self.on_download_complete(extract_path)
            
        except Exception as e:
            logger.error(f"Download completion handler error: {e}")
            messagebox.showerror("Error", f"Download completion error: {e}")
    
    def handle_download_error(self, error_message: str):
        """Handle download error"""
        try:
            self.show_download_progress(False)
            
            logger.error(f"Download error: {error_message}")
            
            # Show error message
            messagebox.showerror(
                "Download Failed",
                f"Failed to download the automation package:\n\n{error_message}\n\n"
                "Please check your internet connection and try again."
            )
            
        except Exception as e:
            logger.error(f"Download error handler error: {e}")
    
    def cancel_download(self):
        """Cancel ongoing download"""
        try:
            if self.is_downloading:
                # Note: This is a simple cancellation - in production you might want
                # to implement proper download cancellation in the downloader
                self.show_download_progress(False)
                
                messagebox.showinfo("Download Cancelled", "Download has been cancelled.")
                
        except Exception as e:
            logger.error(f"Download cancellation error: {e}")
    
    def destroy(self):
        """Clean up the interface"""
        try:
            if self.is_downloading and self.download_thread:
                # Wait briefly for download thread to finish
                self.download_thread.join(timeout=1.0)
            
            # Clean up any remaining downloads
            if self.downloader:
                self.downloader.cleanup_downloads()
                
        except Exception as e:
            logger.error(f"Interface cleanup error: {e}")
