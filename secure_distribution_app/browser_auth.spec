# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main_browser_auth.py'],
    pathex=[],
    binaries=[],
    datas=[
        ('.env', '.'),
        ('templates', 'templates'),
        ('auth', 'auth'),
        ('gui', 'gui'),
        ('security', 'security'),
        ('downloader', 'downloader'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'webbrowser',
        'http.server',
        'socketserver',
        'urllib.parse',
        'threading',
        'json',
        'pathlib',
        'supabase',
        'cryptography',
        'pystray',
        'PIL',
        'psutil',
    ],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='MobileAppAutomationBrowser',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=True,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=True,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
)

# Create macOS app bundle
app = BUNDLE(
    exe,
    name='MobileAppAutomationBrowser.app',
    icon=None,
    bundle_identifier='com.mobileautomation.browser',
    info_plist={
        'CFBundleName': 'Mobile App Automation Browser',
        'CFBundleDisplayName': 'Mobile App Automation Browser',
        'CFBundleVersion': '1.0.0',
        'CFBundleShortVersionString': '1.0.0',
        'NSHighResolutionCapable': True,
        'LSUIElement': False,  # Show in dock
    },
)
