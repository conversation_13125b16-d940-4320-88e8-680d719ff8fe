#!/usr/bin/env python3
"""
Debug LoginWindow Class

This script tests the LoginWindow class directly to identify GUI issues.
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

# Load environment
def load_environment():
    """Load environment variables from .env file"""
    try:
        env_file = Path('.env')
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            print("✅ Environment loaded")
    except Exception as e:
        print(f"❌ Failed to load .env: {e}")

load_environment()

def test_basic_toplevel():
    """Test basic Toplevel window creation"""
    print("🔍 Testing Basic Toplevel Window...")
    
    try:
        root = tk.Tk()
        root.title("Root Window")
        root.geometry("200x100")
        root.withdraw()  # Hide root
        
        # Create toplevel window
        toplevel = tk.Toplevel(root)
        toplevel.title("Test Toplevel")
        toplevel.geometry("400x300")
        
        # Add some content
        label = tk.Label(toplevel, text="Toplevel Window Working!", font=("Arial", 14))
        label.pack(pady=50)
        
        button = tk.Button(toplevel, text="Close", command=root.destroy)
        button.pack(pady=10)
        
        print("✅ Basic toplevel window created")
        
        # Auto-close after 3 seconds
        root.after(3000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Basic toplevel test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_login_window_manual():
    """Test LoginWindow creation manually"""
    print("🔍 Testing Manual LoginWindow Creation...")
    
    try:
        root = tk.Tk()
        root.title("Root Window")
        root.geometry("200x100")
        root.withdraw()  # Hide root
        
        # Create login window manually
        login_window = tk.Toplevel(root)
        login_window.title("Secure Access - Login")
        login_window.geometry("400x500")
        login_window.resizable(False, False)
        
        # Make window modal
        login_window.transient(root)
        login_window.grab_set()
        
        print("✅ Login window created")
        
        # Create main frame
        main_frame = ttk.Frame(login_window, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)
        print("✅ Main frame created and packed")
        
        # Title
        title_label = ttk.Label(main_frame, text="Mobile App Automation", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 5))
        print("✅ Title label created")
        
        subtitle_label = ttk.Label(main_frame, text="Secure Access Portal", 
                                  font=("Arial", 10))
        subtitle_label.pack(pady=(0, 30))
        print("✅ Subtitle label created")
        
        # Form container
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill=tk.X, pady=(0, 20))
        print("✅ Form frame created")
        
        # Email field
        ttk.Label(form_frame, text="Email:").pack(anchor=tk.W, pady=(0, 5))
        email_entry = ttk.Entry(form_frame, font=("Arial", 10), width=30)
        email_entry.pack(fill=tk.X, pady=(0, 15))
        email_entry.insert(0, "<EMAIL>")
        print("✅ Email field created")
        
        # Password field
        ttk.Label(form_frame, text="Password:").pack(anchor=tk.W, pady=(0, 5))
        password_entry = ttk.Entry(form_frame, show="*", font=("Arial", 10), width=30)
        password_entry.pack(fill=tk.X, pady=(0, 15))
        password_entry.insert(0, "password123")
        print("✅ Password field created")
        
        # License field
        ttk.Label(form_frame, text="License Number:").pack(anchor=tk.W, pady=(0, 5))
        license_entry = ttk.Entry(form_frame, font=("Arial", 10), width=30)
        license_entry.pack(fill=tk.X, pady=(0, 15))
        license_entry.insert(0, "TEST-LICENSE-001")
        print("✅ License field created")
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        print("✅ Button frame created")
        
        def test_action():
            messagebox.showinfo("Test", "Button clicked successfully!")
        
        login_button = ttk.Button(button_frame, text="Login", command=test_action)
        login_button.pack(side=tk.LEFT, padx=(0, 10))
        
        register_button = ttk.Button(button_frame, text="Register", command=test_action)
        register_button.pack(side=tk.LEFT)
        
        close_button = ttk.Button(button_frame, text="Close", command=root.destroy)
        close_button.pack(side=tk.RIGHT)
        print("✅ Buttons created")
        
        # Force update
        login_window.update_idletasks()
        login_window.update()
        print("✅ Window updated")
        
        # Center the window
        x = (login_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (login_window.winfo_screenheight() // 2) - (500 // 2)
        login_window.geometry(f"400x500+{x}+{y}")
        print("✅ Window centered")
        
        print("🎉 Manual login window should be visible now!")
        
        # Auto-close after 5 seconds
        root.after(5000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Manual login window test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_login_window_class():
    """Test the actual LoginWindow class"""
    print("🔍 Testing LoginWindow Class...")
    
    try:
        from gui.login_window import LoginWindow
        
        root = tk.Tk()
        root.title("Root Window")
        root.geometry("200x100")
        root.withdraw()  # Hide root
        
        print("✅ Root window created")
        
        # Mock session manager
        class MockSessionManager:
            def __init__(self):
                self.supabase = None
            
            def login(self, email, password, license):
                return {"success": False, "error": "Mock session manager - login not implemented"}
            
            def register(self, email, password, first_name, last_name, license_number):
                return {"success": False, "error": "Mock session manager - register not implemented"}
        
        session_manager = MockSessionManager()
        print("✅ Mock session manager created")
        
        # Callback functions
        def on_login_success(user_data):
            print(f"Login success callback: {user_data}")
            messagebox.showinfo("Success", "Login successful!")
        
        def on_register_success(user_data):
            print(f"Register success callback: {user_data}")
            messagebox.showinfo("Success", "Registration successful!")
        
        print("✅ Callback functions defined")
        
        # Create LoginWindow
        print("Creating LoginWindow...")
        login_window = LoginWindow(
            parent=root,
            session_manager=session_manager,
            on_login_success=on_login_success,
            on_register_success=on_register_success
        )
        
        print("✅ LoginWindow created successfully")
        
        # Check if window exists
        if hasattr(login_window, 'window') and login_window.window:
            print("✅ LoginWindow.window exists")
            print(f"Window title: {login_window.window.title()}")
            print(f"Window geometry: {login_window.window.geometry()}")
            
            # Force update
            login_window.window.update_idletasks()
            login_window.window.update()
            print("✅ Window updated")
            
        else:
            print("❌ LoginWindow.window does not exist")
            return False
        
        print("🎉 LoginWindow class test should show the window!")
        
        # Auto-close after 5 seconds
        root.after(5000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ LoginWindow class test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 LoginWindow Debug Tests")
    print("=" * 40)
    
    tests = [
        ("Basic Toplevel Window", test_basic_toplevel),
        ("Manual LoginWindow Creation", test_login_window_manual),
        ("LoginWindow Class", test_login_window_class),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
            if result:
                print(f"✅ {test_name} PASSED")
            else:
                print(f"❌ {test_name} FAILED")
        except Exception as e:
            print(f"❌ {test_name} CRASHED: {e}")
            results.append((test_name, False))
        
        print(f"{'='*60}")
    
    # Summary
    print(f"\n📊 Test Results Summary")
    print("=" * 40)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! GUI should be working.")
        return True
    else:
        print("⚠️ Some tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
