"""
Platform Selector Interface for Secure Distribution Application

Provides interface for selecting and launching iOS or Android automation platforms.
"""

import os
import sys
import logging
import threading
import tkinter as tk
from tkinter import ttk, messagebox
from typing import Callable, Optional
from pathlib import Path
import webbrowser
import time
import subprocess

logger = logging.getLogger(__name__)

class PlatformSelector:
    """Platform selection interface for launching iOS or Android automation"""
    
    def __init__(self, parent: tk.Tk, session_manager, package_path: str, 
                 on_minimize_to_tray: Callable[[], bool] = None):
        self.parent = parent
        self.session_manager = session_manager
        self.package_path = Path(package_path)
        self.on_minimize_to_tray = on_minimize_to_tray
        
        # Running processes
        self.running_processes = {}
        
        # Create interface
        self.create_interface()
    
    def create_interface(self):
        """Create the platform selection interface"""
        try:
            # Configure the main window
            self.parent.title("Mobile App Automation - Platform Selection")
            self.parent.geometry("700x500")
            self.parent.resizable(False, False)
            
            # Handle window close
            self.parent.protocol("WM_DELETE_WINDOW", self.on_window_close)
            
            # Main frame
            main_frame = ttk.Frame(self.parent, padding="30")
            main_frame.pack(fill=tk.BOTH, expand=True)
            
            # Title
            title_label = ttk.Label(
                main_frame,
                text="Mobile App Automation Suite",
                font=("Arial", 20, "bold")
            )
            title_label.pack(pady=(0, 10))
            
            # Subtitle
            subtitle_label = ttk.Label(
                main_frame,
                text="Choose Your Platform",
                font=("Arial", 14)
            )
            subtitle_label.pack(pady=(0, 30))
            
            # Platform selection frame
            platform_frame = ttk.Frame(main_frame)
            platform_frame.pack(pady=(0, 30))
            
            # iOS Platform Card
            ios_frame = ttk.LabelFrame(platform_frame, text="iOS Platform", padding="20")
            ios_frame.grid(row=0, column=0, padx=(0, 15), pady=10, sticky="nsew")
            
            ios_icon_label = ttk.Label(ios_frame, text="📱", font=("Arial", 32))
            ios_icon_label.pack(pady=(0, 10))
            
            ios_title_label = ttk.Label(
                ios_frame,
                text="iOS Mobile App Automation",
                font=("Arial", 12, "bold")
            )
            ios_title_label.pack(pady=(0, 10))
            
            ios_desc_label = ttk.Label(
                ios_frame,
                text="Complete iOS app testing platform\\n• Xcode integration\\n• iOS Simulator support\\n• Real device testing\\n• Advanced element detection",
                font=("Arial", 9),
                justify=tk.CENTER
            )
            ios_desc_label.pack(pady=(0, 15))
            
            self.ios_button = ttk.Button(
                ios_frame,
                text="Launch iOS App",
                command=lambda: self.launch_platform('ios'),
                style="Accent.TButton"
            )
            self.ios_button.pack(pady=(0, 10))
            
            self.ios_status_label = ttk.Label(
                ios_frame,
                text="Ready to launch",
                font=("Arial", 8),
                foreground="green"
            )
            self.ios_status_label.pack()
            
            # Android Platform Card
            android_frame = ttk.LabelFrame(platform_frame, text="Android Platform", padding="20")
            android_frame.grid(row=0, column=1, padx=(15, 0), pady=10, sticky="nsew")
            
            android_icon_label = ttk.Label(android_frame, text="🤖", font=("Arial", 32))
            android_icon_label.pack(pady=(0, 10))
            
            android_title_label = ttk.Label(
                android_frame,
                text="Android Mobile App Automation",
                font=("Arial", 12, "bold")
            )
            android_title_label.pack(pady=(0, 10))
            
            android_desc_label = ttk.Label(
                android_frame,
                text="Complete Android app testing platform\\n• Android Studio integration\\n• Emulator support\\n• Real device testing via ADB\\n• Advanced element detection",
                font=("Arial", 9),
                justify=tk.CENTER
            )
            android_desc_label.pack(pady=(0, 15))
            
            self.android_button = ttk.Button(
                android_frame,
                text="Launch Android App",
                command=lambda: self.launch_platform('android'),
                style="Accent.TButton"
            )
            self.android_button.pack(pady=(0, 10))
            
            self.android_status_label = ttk.Label(
                android_frame,
                text="Ready to launch",
                font=("Arial", 8),
                foreground="green"
            )
            self.android_status_label.pack()
            
            # Configure grid weights
            platform_frame.grid_columnconfigure(0, weight=1)
            platform_frame.grid_columnconfigure(1, weight=1)
            
            # Status frame
            status_frame = ttk.Frame(main_frame)
            status_frame.pack(pady=(20, 0), fill=tk.X)
            
            # Running apps status
            self.running_status_label = ttk.Label(
                status_frame,
                text="No applications currently running",
                font=("Arial", 10),
                foreground="gray"
            )
            self.running_status_label.pack()
            
            # Control buttons frame
            control_frame = ttk.Frame(main_frame)
            control_frame.pack(pady=(20, 0))
            
            # Stop all button
            self.stop_all_button = ttk.Button(
                control_frame,
                text="Stop All Applications",
                command=self.stop_all_applications,
                state=tk.DISABLED
            )
            self.stop_all_button.pack(side=tk.LEFT, padx=(0, 10))
            
            # Minimize to tray button
            if self.on_minimize_to_tray:
                self.minimize_button = ttk.Button(
                    control_frame,
                    text="Minimize to Tray",
                    command=self.minimize_to_tray
                )
                self.minimize_button.pack(side=tk.LEFT, padx=(10, 0))
            
            # Footer
            footer_frame = ttk.Frame(main_frame)
            footer_frame.pack(side=tk.BOTTOM, fill=tk.X, pady=(20, 0))
            
            user_info = self.session_manager.get_user_info()
            user_email = user_info.get('email', 'Unknown') if user_info else 'Unknown'
            
            footer_label = ttk.Label(
                footer_frame,
                text=f"Logged in as: {user_email} | License: Active",
                font=("Arial", 8),
                foreground="gray"
            )
            footer_label.pack(side=tk.LEFT)
            
            logout_button = ttk.Button(
                footer_frame,
                text="Logout",
                command=self.logout
            )
            logout_button.pack(side=tk.RIGHT)
            
        except Exception as e:
            logger.error(f"Failed to create platform selector interface: {e}")
            messagebox.showerror("Error", f"Failed to create interface: {e}")
    
    def launch_platform(self, platform: str):
        """Launch the specified platform"""
        try:
            if platform in self.running_processes:
                messagebox.showinfo("Already Running", f"{platform.upper()} automation is already running.")
                return
            
            # Validate license before launching
            if not self.session_manager.is_license_valid():
                messagebox.showerror("License Error", "License expired or invalid. Please contact support.")
                return
            
            logger.info(f"Launching {platform} platform...")
            
            # Update button state
            if platform == 'ios':
                self.ios_button.config(state=tk.DISABLED, text="Starting...")
                self.ios_status_label.config(text="Starting...", foreground="orange")
            else:
                self.android_button.config(state=tk.DISABLED, text="Starting...")
                self.android_status_label.config(text="Starting...", foreground="orange")
            
            # Launch in background thread
            launch_thread = threading.Thread(
                target=self._launch_platform_worker,
                args=(platform,),
                daemon=True
            )
            launch_thread.start()
            
        except Exception as e:
            logger.error(f"Failed to launch {platform} platform: {e}")
            messagebox.showerror("Launch Error", f"Failed to launch {platform} platform: {e}")
            self._reset_platform_button(platform)
    
    def _launch_platform_worker(self, platform: str):
        """Background worker for platform launching"""
        try:
            # Determine script path and port
            if platform == 'ios':
                script_path = self.package_path / 'launch_ios.py'
                port = 8090
                url = f"http://localhost:{port}"
            else:
                script_path = self.package_path / 'launch_android.py'
                port = 8091
                url = f"http://localhost:{port}"
            
            if not script_path.exists():
                raise Exception(f"Launch script not found: {script_path}")
            
            # Start the platform process
            process = subprocess.Popen(
                [sys.executable, str(script_path)],
                cwd=str(self.package_path),
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                creationflags=subprocess.CREATE_NO_WINDOW if os.name == 'nt' else 0
            )
            
            # Store process
            self.running_processes[platform] = {
                'process': process,
                'port': port,
                'url': url,
                'start_time': time.time()
            }
            
            # Wait for server to start
            time.sleep(3)
            
            # Open browser
            webbrowser.open(url)
            
            # Update UI
            self.parent.after(0, lambda: self._update_platform_launched(platform))
            
            # Auto-minimize to tray if available
            if self.on_minimize_to_tray:
                self.parent.after(2000, self._auto_minimize)  # Minimize after 2 seconds
            
        except Exception as e:
            logger.error(f"Platform launch worker error: {e}")
            self.parent.after(0, lambda: self._handle_launch_error(platform, str(e)))
    
    def _update_platform_launched(self, platform: str):
        """Update UI after successful platform launch"""
        try:
            if platform == 'ios':
                self.ios_button.config(state=tk.NORMAL, text="Open in Browser")
                self.ios_status_label.config(text="Running on port 8090", foreground="green")
            else:
                self.android_button.config(state=tk.NORMAL, text="Open in Browser")
                self.android_status_label.config(text="Running on port 8091", foreground="green")
            
            # Update running status
            running_count = len(self.running_processes)
            self.running_status_label.config(
                text=f"{running_count} application(s) currently running",
                foreground="green"
            )
            
            # Enable stop all button
            self.stop_all_button.config(state=tk.NORMAL)
            
            logger.info(f"{platform.upper()} platform launched successfully")
            
        except Exception as e:
            logger.error(f"UI update error: {e}")
    
    def _handle_launch_error(self, platform: str, error_message: str):
        """Handle platform launch error"""
        try:
            self._reset_platform_button(platform)
            
            # Remove from running processes if added
            if platform in self.running_processes:
                del self.running_processes[platform]
            
            messagebox.showerror(
                "Launch Error",
                f"Failed to launch {platform.upper()} platform:\\n\\n{error_message}"
            )
            
        except Exception as e:
            logger.error(f"Launch error handler error: {e}")
    
    def _reset_platform_button(self, platform: str):
        """Reset platform button to original state"""
        try:
            if platform == 'ios':
                self.ios_button.config(state=tk.NORMAL, text="Launch iOS App")
                self.ios_status_label.config(text="Ready to launch", foreground="green")
            else:
                self.android_button.config(state=tk.NORMAL, text="Launch Android App")
                self.android_status_label.config(text="Ready to launch", foreground="green")
                
        except Exception as e:
            logger.error(f"Button reset error: {e}")
    
    def _auto_minimize(self):
        """Auto-minimize to tray after launching"""
        try:
            if self.running_processes and self.on_minimize_to_tray:
                success = self.on_minimize_to_tray()
                if success:
                    logger.info("Application auto-minimized to system tray")
                    
        except Exception as e:
            logger.error(f"Auto-minimize error: {e}")
    
    def stop_all_applications(self):
        """Stop all running applications"""
        try:
            if not self.running_processes:
                return
            
            stopped_count = 0
            for platform, info in list(self.running_processes.items()):
                try:
                    process = info['process']
                    process.terminate()
                    process.wait(timeout=5)
                    stopped_count += 1
                    
                    # Reset button
                    self._reset_platform_button(platform)
                    
                except Exception as e:
                    logger.warning(f"Failed to stop {platform} process: {e}")
            
            # Clear all processes
            self.running_processes.clear()
            
            # Update UI
            self.running_status_label.config(
                text="No applications currently running",
                foreground="gray"
            )
            self.stop_all_button.config(state=tk.DISABLED)
            
            if stopped_count > 0:
                messagebox.showinfo("Applications Stopped", f"Stopped {stopped_count} application(s).")
            
        except Exception as e:
            logger.error(f"Stop all applications error: {e}")
            messagebox.showerror("Error", f"Failed to stop applications: {e}")
    
    def minimize_to_tray(self):
        """Minimize application to system tray"""
        try:
            if self.on_minimize_to_tray:
                success = self.on_minimize_to_tray()
                if not success:
                    messagebox.showwarning("System Tray", "System tray is not available on this system.")
            else:
                messagebox.showwarning("System Tray", "System tray functionality is not available.")
                
        except Exception as e:
            logger.error(f"Minimize to tray error: {e}")
            messagebox.showerror("Error", f"Failed to minimize to tray: {e}")
    
    def logout(self):
        """Handle user logout"""
        try:
            if self.running_processes:
                result = messagebox.askyesno(
                    "Logout Confirmation",
                    f"You have {len(self.running_processes)} application(s) running.\\n\\n"
                    "Logging out will stop all running applications. Continue?"
                )
                
                if not result:
                    return
                
                # Stop all applications
                self.stop_all_applications()
            
            # Clear session and logout
            self.session_manager.clear_session()
            
            # Notify parent to show login screen
            from main import MobileAppAutomationApp
            app = MobileAppAutomationApp()
            app.on_logout()
            
        except Exception as e:
            logger.error(f"Logout error: {e}")
            messagebox.showerror("Error", f"Logout failed: {e}")
    
    def on_window_close(self):
        """Handle window close event"""
        try:
            if self.running_processes:
                result = messagebox.askyesnocancel(
                    "Close Application",
                    f"You have {len(self.running_processes)} application(s) running.\\n\\n"
                    "Choose an option:\\n"
                    "• Yes: Minimize to system tray (keep apps running)\\n"
                    "• No: Close and stop all apps\\n"
                    "• Cancel: Return to application"
                )
                
                if result is True:  # Yes - minimize to tray
                    if self.on_minimize_to_tray and self.on_minimize_to_tray():
                        return  # Successfully minimized
                elif result is False:  # No - close and stop apps
                    self.stop_all_applications()
                    self.logout()
                # Cancel - do nothing, return to app
                return
            
            # No running apps - normal logout
            self.logout()
            
        except Exception as e:
            logger.error(f"Window close handler error: {e}")
            self.logout()
    
    def destroy(self):
        """Clean up the interface"""
        try:
            # Stop all running processes
            self.stop_all_applications()
            
        except Exception as e:
            logger.error(f"Interface cleanup error: {e}")
