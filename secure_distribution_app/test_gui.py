#!/usr/bin/env python3
"""
Test GUI Components

This script tests the GUI components individually to identify issues.
"""

import sys
import os
import tkinter as tk
from tkinter import ttk, messagebox
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

# Load environment
def load_environment():
    """Load environment variables from .env file"""
    try:
        env_file = Path('.env')
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            print("✅ Environment loaded")
        else:
            print("⚠️ No .env file found")
    except Exception as e:
        print(f"❌ Failed to load .env: {e}")

load_environment()

def test_basic_tkinter():
    """Test basic tkinter functionality"""
    print("🔍 Testing Basic Tkinter...")
    
    try:
        root = tk.Tk()
        root.title("Basic Tkinter Test")
        root.geometry("300x200")
        
        label = tk.Label(root, text="Basic Tkinter Working!", font=("Arial", 12))
        label.pack(pady=20)
        
        button = tk.Button(root, text="Close", command=root.destroy)
        button.pack(pady=10)
        
        print("✅ Basic tkinter window created")
        
        # Show for 3 seconds then close
        root.after(3000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Basic tkinter failed: {e}")
        return False

def test_login_window_standalone():
    """Test login window without session manager"""
    print("🔍 Testing Login Window (Standalone)...")
    
    try:
        root = tk.Tk()
        root.title("Login Window Test")
        root.geometry("800x600")
        root.withdraw()  # Hide main window
        
        # Create a simple login window
        login_window = tk.Toplevel(root)
        login_window.title("Secure Access - Login")
        login_window.geometry("400x500")
        login_window.resizable(False, False)
        
        # Make window modal
        login_window.transient(root)
        login_window.grab_set()
        
        # Create UI
        main_frame = ttk.Frame(login_window, padding="30")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Title
        title_label = ttk.Label(main_frame, text="Mobile App Automation", 
                               font=("Arial", 16, "bold"))
        title_label.pack(pady=(0, 5))
        
        subtitle_label = ttk.Label(main_frame, text="Secure Access Portal", 
                                  font=("Arial", 10))
        subtitle_label.pack(pady=(0, 30))
        
        # Form container
        form_frame = ttk.Frame(main_frame)
        form_frame.pack(fill=tk.X, pady=(0, 20))
        
        # Email field
        ttk.Label(form_frame, text="Email:").pack(anchor=tk.W, pady=(0, 5))
        email_entry = ttk.Entry(form_frame, font=("Arial", 10), width=30)
        email_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Password field
        ttk.Label(form_frame, text="Password:").pack(anchor=tk.W, pady=(0, 5))
        password_entry = ttk.Entry(form_frame, show="*", font=("Arial", 10), width=30)
        password_entry.pack(fill=tk.X, pady=(0, 15))
        
        # License field
        ttk.Label(form_frame, text="License Number:").pack(anchor=tk.W, pady=(0, 5))
        license_entry = ttk.Entry(form_frame, font=("Arial", 10), width=30)
        license_entry.pack(fill=tk.X, pady=(0, 15))
        
        # Buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, pady=(20, 0))
        
        login_button = ttk.Button(button_frame, text="Login", 
                                 command=lambda: print("Login clicked"))
        login_button.pack(side=tk.LEFT, padx=(0, 10))
        
        register_button = ttk.Button(button_frame, text="Register", 
                                    command=lambda: print("Register clicked"))
        register_button.pack(side=tk.LEFT)
        
        close_button = ttk.Button(button_frame, text="Close", 
                                 command=root.destroy)
        close_button.pack(side=tk.RIGHT)
        
        print("✅ Standalone login window created")
        
        # Center the window
        login_window.update_idletasks()
        x = (login_window.winfo_screenwidth() // 2) - (400 // 2)
        y = (login_window.winfo_screenheight() // 2) - (500 // 2)
        login_window.geometry(f"400x500+{x}+{y}")
        
        # Show for 5 seconds then close
        root.after(5000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Standalone login window failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_session_manager():
    """Test session manager initialization"""
    print("🔍 Testing Session Manager...")
    
    try:
        from auth.session_manager import SessionManager
        
        session_manager = SessionManager()
        print("✅ SessionManager created successfully")
        
        if session_manager.supabase:
            print("✅ Supabase client initialized")
        else:
            print("❌ Supabase client is None")
        
        return True
        
    except Exception as e:
        print(f"❌ SessionManager failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_login_window_with_session():
    """Test login window with session manager"""
    print("🔍 Testing Login Window (With Session Manager)...")
    
    try:
        from gui.login_window import LoginWindow
        from auth.session_manager import SessionManager
        
        # Create session manager
        session_manager = SessionManager()
        print("✅ SessionManager created")
        
        # Create root window
        root = tk.Tk()
        root.title("Login Window with Session Test")
        root.geometry("800x600")
        root.withdraw()  # Hide main window
        
        # Create login window
        def on_login_success(user_data):
            print(f"Login success: {user_data}")
            
        def on_register_success(user_data):
            print(f"Register success: {user_data}")
        
        login_window = LoginWindow(
            parent=root,
            session_manager=session_manager,
            on_login_success=on_login_success,
            on_register_success=on_register_success
        )
        
        print("✅ LoginWindow created successfully")
        
        # Show for 5 seconds then close
        root.after(5000, root.destroy)
        root.mainloop()
        
        return True
        
    except Exception as e:
        print(f"❌ Login window with session failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function"""
    print("🧪 GUI Component Testing")
    print("=" * 40)
    
    tests = [
        ("Basic Tkinter", test_basic_tkinter),
        ("Session Manager", test_session_manager),
        ("Login Window (Standalone)", test_login_window_standalone),
        ("Login Window (With Session)", test_login_window_with_session),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*20} {test_name} {'='*20}")
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    # Summary
    print(f"\n{'='*60}")
    print("📊 Test Results Summary")
    print("=" * 60)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 Overall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All GUI tests passed!")
        return True
    else:
        print("⚠️ Some GUI tests failed. Check the output above for details.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
