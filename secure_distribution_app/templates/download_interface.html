<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mobile App Automation - Download Center</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        .navbar-brand {
            font-weight: bold;
        }
        .download-card {
            transition: transform 0.2s, box-shadow 0.2s;
            cursor: pointer;
            border: 2px solid transparent;
        }
        .download-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            border-color: #007bff;
        }
        .platform-icon {
            font-size: 4rem;
            margin-bottom: 1rem;
        }
        .download-btn {
            font-size: 1.1rem;
            padding: 12px 30px;
            border-radius: 8px;
            font-weight: 600;
        }
        .feature-list {
            text-align: left;
            margin: 1.5rem 0;
        }
        .feature-list li {
            margin-bottom: 0.5rem;
            color: #666;
        }
        .hero-section {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 3rem 0;
            margin-bottom: 3rem;
        }
        .status-indicator {
            position: absolute;
            top: 15px;
            right: 15px;
            width: 12px;
            height: 12px;
            border-radius: 50%;
            background-color: #28a745;
        }
        .card-body {
            position: relative;
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-mobile-alt me-2"></i>Mobile App Automation
            </a>
            
            <div class="navbar-nav ms-auto">
                <span class="navbar-text">
                    <i class="fas fa-user me-1"></i>
                    <span id="user-email">Loading...</span>
                </span>
            </div>
        </div>
    </nav>

    <!-- Hero Section -->
    <div class="hero-section">
        <div class="container text-center">
            <h1 class="display-4 mb-3">
                <i class="fas fa-download me-3"></i>Download Mobile Automation Tools
            </h1>
            <p class="lead mb-0">
                Choose your platform and start automating mobile app testing
            </p>
        </div>
    </div>

    <!-- Download Options -->
    <div class="container">
        <div class="row justify-content-center">
            <!-- iOS Download -->
            <div class="col-lg-5 col-md-6 mb-4">
                <div class="card download-card h-100 text-center">
                    <div class="card-body">
                        <div class="status-indicator" title="Available"></div>
                        <div class="platform-icon text-primary">
                            <i class="fab fa-apple"></i>
                        </div>
                        <h3 class="card-title mb-3">iOS Automation</h3>
                        <p class="card-text text-muted mb-4">
                            Advanced iOS app testing and automation tools
                        </p>
                        
                        <ul class="feature-list list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>XCUITest Integration</li>
                            <li><i class="fas fa-check text-success me-2"></i>Real Device Testing</li>
                            <li><i class="fas fa-check text-success me-2"></i>Simulator Support</li>
                            <li><i class="fas fa-check text-success me-2"></i>Automated Screenshots</li>
                            <li><i class="fas fa-check text-success me-2"></i>Performance Monitoring</li>
                        </ul>
                        
                        <button class="btn btn-primary download-btn" onclick="downloadiOS()">
                            <i class="fas fa-download me-2"></i>Download iOS Tools
                        </button>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Requires macOS and Xcode
                            </small>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Android Download -->
            <div class="col-lg-5 col-md-6 mb-4">
                <div class="card download-card h-100 text-center">
                    <div class="card-body">
                        <div class="status-indicator" title="Available"></div>
                        <div class="platform-icon text-success">
                            <i class="fab fa-android"></i>
                        </div>
                        <h3 class="card-title mb-3">Android Automation</h3>
                        <p class="card-text text-muted mb-4">
                            Comprehensive Android app testing and automation suite
                        </p>
                        
                        <ul class="feature-list list-unstyled">
                            <li><i class="fas fa-check text-success me-2"></i>Appium Integration</li>
                            <li><i class="fas fa-check text-success me-2"></i>Real Device Testing</li>
                            <li><i class="fas fa-check text-success me-2"></i>Emulator Support</li>
                            <li><i class="fas fa-check text-success me-2"></i>UI Automation</li>
                            <li><i class="fas fa-check text-success me-2"></i>Performance Testing</li>
                        </ul>
                        
                        <button class="btn btn-success download-btn" onclick="downloadAndroid()">
                            <i class="fas fa-download me-2"></i>Download Android Tools
                        </button>
                        
                        <div class="mt-3">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                Cross-platform compatible
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Additional Information -->
        <div class="row mt-5">
            <div class="col-12">
                <div class="card bg-light">
                    <div class="card-body">
                        <h5 class="card-title">
                            <i class="fas fa-question-circle me-2"></i>Getting Started
                        </h5>
                        <div class="row">
                            <div class="col-md-4">
                                <h6><i class="fas fa-download me-2 text-primary"></i>1. Download</h6>
                                <p class="small text-muted">Choose your platform and download the automation tools</p>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="fas fa-cog me-2 text-warning"></i>2. Install</h6>
                                <p class="small text-muted">Follow the installation guide for your operating system</p>
                            </div>
                            <div class="col-md-4">
                                <h6><i class="fas fa-play me-2 text-success"></i>3. Start Testing</h6>
                                <p class="small text-muted">Begin automating your mobile app testing workflows</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Footer -->
    <footer class="mt-5 py-4 bg-dark text-light">
        <div class="container text-center">
            <p class="mb-0">
                <i class="fas fa-mobile-alt me-2"></i>
                Mobile App Automation Platform &copy; 2024
            </p>
        </div>
    </footer>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        // Download functions
        function downloadiOS() {
            const button = event.target;
            const originalText = button.innerHTML;
            
            // Show loading state
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Preparing Download...';
            button.disabled = true;
            
            // Simulate download preparation
            setTimeout(() => {
                // In a real implementation, this would trigger the actual download
                showToast('iOS automation tools download started!', 'success');
                
                // Reset button
                button.innerHTML = originalText;
                button.disabled = false;
                
                // Trigger download (placeholder)
                triggerDownload('ios');
            }, 2000);
        }
        
        function downloadAndroid() {
            const button = event.target;
            const originalText = button.innerHTML;
            
            // Show loading state
            button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Preparing Download...';
            button.disabled = true;
            
            // Simulate download preparation
            setTimeout(() => {
                // In a real implementation, this would trigger the actual download
                showToast('Android automation tools download started!', 'success');
                
                // Reset button
                button.innerHTML = originalText;
                button.disabled = false;
                
                // Trigger download (placeholder)
                triggerDownload('android');
            }, 2000);
        }
        
        function triggerDownload(platform) {
            // This would be replaced with actual download logic
            console.log(`Downloading ${platform} automation tools...`);
            
            // For now, just show a message
            showToast(`${platform.toUpperCase()} download will begin shortly...`, 'info');
        }
        
        function showToast(message, type = 'info') {
            const toastContainer = document.getElementById('toast-container') || createToastContainer();
            const toast = document.createElement('div');
            toast.className = `toast align-items-center text-white bg-${type} border-0`;
            toast.setAttribute('role', 'alert');
            toast.innerHTML = `
                <div class="d-flex">
                    <div class="toast-body">${message}</div>
                    <button type="button" class="btn-close btn-close-white me-2 m-auto" data-bs-dismiss="toast"></button>
                </div>
            `;
            toastContainer.appendChild(toast);
            const bsToast = new bootstrap.Toast(toast);
            bsToast.show();
            
            toast.addEventListener('hidden.bs.toast', () => {
                toast.remove();
            });
        }
        
        function createToastContainer() {
            const container = document.createElement('div');
            container.id = 'toast-container';
            container.className = 'toast-container position-fixed bottom-0 end-0 p-3';
            document.body.appendChild(container);
            return container;
        }
        
        // Load user information
        document.addEventListener('DOMContentLoaded', function() {
            // This would be populated by the application
            document.getElementById('user-email').textContent = '<EMAIL>';
        });
    </script>
</body>
</html>
