#!/usr/bin/env python3
"""
Simple GUI Test

This script creates a simple login window to test if the GUI is working.
"""

import sys
import os
import tkinter as tk
from tkinter import ttk
from pathlib import Path

# Add current directory to path
sys.path.insert(0, str(Path(__file__).parent))

# Load environment
def load_environment():
    """Load environment variables from .env file"""
    try:
        env_file = Path('.env')
        if env_file.exists():
            with open(env_file, 'r') as f:
                for line in f:
                    line = line.strip()
                    if line and not line.startswith('#') and '=' in line:
                        key, value = line.split('=', 1)
                        os.environ[key.strip()] = value.strip()
            print("✅ Environment loaded")
    except Exception as e:
        print(f"❌ Failed to load .env: {e}")

load_environment()

def create_simple_login_window():
    """Create a simple login window for testing"""
    
    # Create main window (hidden)
    root = tk.Tk()
    root.title("Test App")
    root.withdraw()  # Hide main window
    
    # Create login window
    login_window = tk.Toplevel(root)
    login_window.title("Secure Access - Login")
    login_window.geometry("400x500")
    login_window.resizable(False, False)
    
    # Make window modal
    login_window.transient(root)
    login_window.grab_set()
    
    # Center the window
    login_window.update_idletasks()
    x = (login_window.winfo_screenwidth() // 2) - (400 // 2)
    y = (login_window.winfo_screenheight() // 2) - (500 // 2)
    login_window.geometry(f"400x500+{x}+{y}")
    
    # Create UI
    main_frame = ttk.Frame(login_window, padding="30")
    main_frame.pack(fill=tk.BOTH, expand=True)
    
    # Title
    title_label = ttk.Label(main_frame, text="Mobile App Automation", 
                           font=("Arial", 16, "bold"))
    title_label.pack(pady=(0, 5))
    
    subtitle_label = ttk.Label(main_frame, text="Secure Access Portal", 
                              font=("Arial", 10))
    subtitle_label.pack(pady=(0, 30))
    
    # Status label
    status_label = ttk.Label(main_frame, text="✅ GUI is working correctly!", 
                            font=("Arial", 12), foreground="green")
    status_label.pack(pady=(0, 20))
    
    # Form container
    form_frame = ttk.Frame(main_frame)
    form_frame.pack(fill=tk.X, pady=(0, 20))
    
    # Email field
    ttk.Label(form_frame, text="Email:").pack(anchor=tk.W, pady=(0, 5))
    email_entry = ttk.Entry(form_frame, font=("Arial", 10), width=30)
    email_entry.pack(fill=tk.X, pady=(0, 15))
    email_entry.insert(0, "<EMAIL>")
    
    # Password field
    ttk.Label(form_frame, text="Password:").pack(anchor=tk.W, pady=(0, 5))
    password_entry = ttk.Entry(form_frame, show="*", font=("Arial", 10), width=30)
    password_entry.pack(fill=tk.X, pady=(0, 15))
    password_entry.insert(0, "password123")
    
    # License field
    ttk.Label(form_frame, text="License Number:").pack(anchor=tk.W, pady=(0, 5))
    license_entry = ttk.Entry(form_frame, font=("Arial", 10), width=30)
    license_entry.pack(fill=tk.X, pady=(0, 15))
    license_entry.insert(0, "TEST-LICENSE-001")
    
    # Buttons
    button_frame = ttk.Frame(main_frame)
    button_frame.pack(fill=tk.X, pady=(20, 0))
    
    def test_login():
        email = email_entry.get()
        password = password_entry.get()
        license_num = license_entry.get()
        
        if email and password and license_num:
            status_label.config(text="✅ Login form data captured successfully!", foreground="green")
            print(f"Login attempt: {email}, License: {license_num}")
        else:
            status_label.config(text="❌ Please fill in all fields", foreground="red")
    
    def test_register():
        status_label.config(text="✅ Register button clicked!", foreground="blue")
        print("Register button clicked")
    
    login_button = ttk.Button(button_frame, text="Test Login", command=test_login)
    login_button.pack(side=tk.LEFT, padx=(0, 10))
    
    register_button = ttk.Button(button_frame, text="Test Register", command=test_register)
    register_button.pack(side=tk.LEFT)
    
    close_button = ttk.Button(button_frame, text="Close", command=root.destroy)
    close_button.pack(side=tk.RIGHT)
    
    # Instructions
    instructions = ttk.Label(main_frame, 
                            text="This is a test of the GUI components.\nFill in the fields and click 'Test Login' to verify functionality.",
                            font=("Arial", 9), foreground="gray")
    instructions.pack(pady=(20, 0))
    
    print("✅ Simple login window created and displayed")
    print("📋 Test the following:")
    print("   1. Window appears and is properly centered")
    print("   2. All form fields are visible and editable")
    print("   3. Buttons are clickable and responsive")
    print("   4. Status messages update when buttons are clicked")
    
    # Start the GUI
    root.mainloop()

def main():
    """Main function"""
    print("🧪 Simple GUI Test")
    print("=" * 30)
    
    try:
        create_simple_login_window()
        print("✅ GUI test completed successfully")
        return True
    except Exception as e:
        print(f"❌ GUI test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
