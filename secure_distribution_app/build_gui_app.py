#!/usr/bin/env python3
"""
GUI Application Builder for Mobile App Automation

Creates a working GUI executable with proper tkinter support on macOS.
"""

import os
import sys
import subprocess
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def check_tkinter():
    """Check if tkinter is available"""
    try:
        import tkinter
        logger.info("✅ tkinter is available")
        return True
    except ImportError:
        logger.error("❌ tkinter is not available")
        return False

def create_gui_spec():
    """Create PyInstaller spec file optimized for GUI with tkinter"""
    
    # Get the Python executable path
    python_path = sys.executable
    logger.info(f"Using Python: {python_path}")
    
    spec_content = f'''# -*- mode: python ; coding: utf-8 -*-
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(SPEC)))

a = Analysis(
    ['main.py'],
    pathex=[os.path.dirname(os.path.abspath(SPEC))],
    binaries=[],
    datas=[
        ('gui', 'gui'),
        ('auth', 'auth'),
        ('security', 'security'),
        ('downloader', 'downloader'),
        ('launcher', 'launcher'),
        ('.env', '.'),
    ],
    hiddenimports=[
        'tkinter',
        'tkinter.ttk',
        'tkinter.messagebox',
        'tkinter.filedialog',
        'tkinter.font',
        'tkinter.scrolledtext',
        '_tkinter',
        'supabase',
        'cryptography',
        'cryptography.fernet',
        'cryptography.hazmat.primitives',
        'cryptography.hazmat.primitives.kdf.pbkdf2',
        'cryptography.hazmat.primitives.hashes',
        'pystray',
        'PIL',
        'PIL.Image',
        'PIL.ImageDraw',
        'psutil',
        'threading',
        'urllib.request',
        'urllib.parse',
        'base64',
        'hashlib',
        'tempfile',
        'zipfile',
        'shutil',
        'platform',
        'webbrowser',
        'subprocess',
        'json',
        'pathlib',
        'os',
        'sys',
        'time',
        'logging',
        'uuid',
        'ctypes',
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=[],
    noarchive=False,
    optimize=0,
)

# Filter out any problematic binaries
a.binaries = [x for x in a.binaries if not x[0].startswith('tk') or 'tcl' not in x[0].lower()]

pyz = PYZ(a.pure, a.zipped_data, cipher=None)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.datas,
    [],
    name='MobileAppAutomation',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=True,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon=None,
)

# Create macOS app bundle
app = BUNDLE(
    exe,
    name='MobileAppAutomation.app',
    icon=None,
    bundle_identifier='com.mobileautomation.app',
    info_plist={{
        'CFBundleName': 'Mobile App Automation',
        'CFBundleDisplayName': 'Mobile App Automation',
        'CFBundleVersion': '2.0.0',
        'CFBundleShortVersionString': '2.0.0',
        'NSHighResolutionCapable': True,
        'LSUIElement': False,
    }},
)
'''
    
    with open('gui_app.spec', 'w') as f:
        f.write(spec_content)
    
    logger.info("GUI spec file created")

def build_executable():
    """Build the GUI executable"""
    try:
        logger.info("Building GUI executable with PyInstaller...")
        
        # Check tkinter first
        if not check_tkinter():
            logger.error("tkinter is required but not available")
            return False
        
        # Create spec file
        create_gui_spec()
        
        # Clean previous builds
        if Path('dist').exists():
            import shutil
            shutil.rmtree('dist')
        if Path('build').exists():
            import shutil
            shutil.rmtree('build')
        
        # Run PyInstaller with verbose output
        cmd = [sys.executable, '-m', 'PyInstaller', '--clean', '--noconfirm', 'gui_app.spec']
        
        logger.info(f"Running: {{' '.join(cmd)}}")
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            logger.info("✅ Executable built successfully!")
            
            # Check what was created
            dist_dir = Path('dist')
            if dist_dir.exists():
                for item in dist_dir.iterdir():
                    logger.info(f"Created: {{item}}")
            
            return True
        else:
            logger.error(f"❌ PyInstaller failed:")
            logger.error(f"STDOUT: {{result.stdout}}")
            logger.error(f"STDERR: {{result.stderr}}")
            return False
            
    except Exception as e:
        logger.error(f"Build failed: {{e}}")
        return False

def test_executable():
    """Test the built executable"""
    try:
        logger.info("Testing executable...")
        
        # Test the standalone executable first
        exe_path = Path('dist/MobileAppAutomation')
        if exe_path.exists():
            logger.info("Testing standalone executable...")
            # Just check if it starts without errors (don't wait for GUI)
            result = subprocess.run([str(exe_path), '--help'], 
                                  capture_output=True, text=True, timeout=5)
            if result.returncode == 0 or 'usage:' in result.stdout.lower():
                logger.info("✅ Standalone executable appears to work")
            else:
                logger.warning(f"⚠️ Standalone executable test: {{result.stderr}}")
        
        # Test the app bundle
        app_path = Path('dist/MobileAppAutomation.app')
        if app_path.exists():
            logger.info("✅ App bundle created successfully")
            logger.info("You can test it by running: open dist/MobileAppAutomation.app")
            return True
        else:
            logger.error("❌ App bundle not found")
            return False
            
    except subprocess.TimeoutExpired:
        logger.info("✅ Executable started (timeout reached, which is expected for GUI)")
        return True
    except Exception as e:
        logger.error(f"Test failed: {{e}}")
        return False

def main():
    """Main entry point"""
    try:
        print("🚀 GUI Application Builder - Mobile App Automation")
        print("=" * 50)
        
        # Check if we're in the right directory
        if not Path('main.py').exists():
            print("❌ main.py not found. Please run from secure_distribution_app directory.")
            sys.exit(1)
        
        # Check Python and tkinter
        print(f"🐍 Python: {{sys.version}}")
        print(f"📍 Python path: {{sys.executable}}")
        
        if not check_tkinter():
            print("❌ tkinter is not available in this Python installation.")
            print("💡 Try using system Python: /usr/bin/python3")
            sys.exit(1)
        
        # Build executable
        if build_executable():
            print("\\n✅ Build completed successfully!")
            
            # Test the executable
            if test_executable():
                print("\\n📁 Output location:")
                print("   macOS App: dist/MobileAppAutomation.app")
                print("   Executable: dist/MobileAppAutomation")
                
                print("\\n🧪 Testing:")
                print("   Run: open dist/MobileAppAutomation.app")
                print("   Or:  ./dist/MobileAppAutomation")
                
                print("\\n📋 Next steps:")
                print("1. Test the GUI application")
                print("2. Configure environment variables (.env file)")
                print("3. Set up Supabase credentials")
                print("4. Test user registration flow")
            else:
                print("\\n⚠️ Build completed but testing failed")
        else:
            print("\\n❌ Build failed!")
            sys.exit(1)
        
    except KeyboardInterrupt:
        print("\\n⚠️ Build cancelled by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Build process failed: {{e}}")
        print(f"\\n❌ Build process failed: {{e}}")
        sys.exit(1)

if __name__ == "__main__":
    main()
