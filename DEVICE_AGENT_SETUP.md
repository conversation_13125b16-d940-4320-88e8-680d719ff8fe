# Local Device Agent Setup Guide

The Local Device Agent runs directly on the host machine to provide reliable access to physical USB devices for the containerized mobile automation applications.

## Overview

The device agent acts as a bridge between:
- Physical devices connected via USB
- Containerized Android and iOS automation applications
- The testing dashboard and management interfaces

## Prerequisites

### System Requirements
- **Operating System**: macOS, Linux, or Windows
- **Python**: 3.8 or higher
- **USB Ports**: Available USB ports for device connections
- **Network**: Local network access for container communication

### Platform-Specific Requirements

#### macOS
```bash
# Install Xcode Command Line Tools
xcode-select --install

# Install Homebrew (if not already installed)
/bin/bash -c "$(curl -fsSL https://raw.githubusercontent.com/Homebrew/install/HEAD/install.sh)"

# Install required tools
brew install libimobiledevice
brew install ideviceinstaller
brew install ios-deploy
```

#### Linux (Ubuntu/Debian)
```bash
# Update package list
sudo apt update

# Install system dependencies
sudo apt install -y \
    python3-dev \
    python3-pip \
    build-essential \
    libusb-1.0-0 \
    libusb-1.0-0-dev \
    libudev-dev \
    libimobiledevice6 \
    libimobiledevice-utils \
    ideviceinstaller \
    pkg-config \
    libssl-dev \
    libffi-dev

# Add user to plugdev group for USB access
sudo usermod -a -G plugdev $USER

# Create udev rules for Android devices
echo 'SUBSYSTEM=="usb", ATTR{idVendor}=="18d1", MODE="0666", GROUP="plugdev"' | sudo tee /etc/udev/rules.d/51-android.rules
echo 'SUBSYSTEM=="usb", ATTR{idVendor}=="04e8", MODE="0666", GROUP="plugdev"' | sudo tee -a /etc/udev/rules.d/51-android.rules

# Reload udev rules
sudo udevadm control --reload-rules
sudo udevadm trigger
```

#### Windows
```powershell
# Install Python 3.8+ from python.org
# Install Git for Windows
# Install Android SDK Platform Tools
# Install iTunes (for iOS device support)
```

## Android Development Setup

### Install Android SDK
```bash
# Download Android SDK Command Line Tools
wget https://dl.google.com/android/repository/commandlinetools-linux-latest.zip
unzip commandlinetools-linux-latest.zip

# Set environment variables
export ANDROID_HOME=$HOME/android-sdk
export PATH=$PATH:$ANDROID_HOME/platform-tools
export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin

# Create SDK directory and install platform tools
mkdir -p $ANDROID_HOME
cd cmdline-tools
./bin/sdkmanager --sdk_root=$ANDROID_HOME "platform-tools" "build-tools;30.0.3"
```

### Enable USB Debugging
1. On your Android device, go to **Settings > About Phone**
2. Tap **Build Number** 7 times to enable Developer Options
3. Go to **Settings > Developer Options**
4. Enable **USB Debugging**
5. Connect device via USB and authorize the computer

### Verify ADB Connection
```bash
# Check if device is detected
adb devices

# Should show something like:
# List of devices attached
# ABC123DEF456    device
```

## iOS Development Setup

### Install iOS Tools
```bash
# macOS only - install via Homebrew
brew install libimobiledevice
brew install ideviceinstaller
brew install ios-deploy

# Verify installation
idevice_id -l  # List connected iOS devices
```

### Enable iOS Development
1. Connect iOS device via USB
2. Trust the computer when prompted
3. Ensure device is not locked during testing
4. For app installation, you may need a valid iOS Developer Certificate

## Python Dependencies Installation

### Install Required Packages
```bash
# Navigate to project directory
cd /path/to/MobileAppAutomation

# Install base requirements
pip3 install -r requirements.txt

# Install device agent specific packages
pip3 install \
    pyudev \
    libimobiledevice \
    pymobiledevice3 \
    adb-shell \
    pure-python-adb \
    psutil \
    netifaces
```

## Device Agent Configuration

### Environment Variables
Create a `.env` file or set environment variables:

```bash
# Device Agent Configuration
AGENT_PORT=8084
ADB_SERVER_PORT=5037
IOS_PROXY_PORT=8100
LOG_LEVEL=INFO

# Device Detection Settings
DEVICE_POLLING_INTERVAL=5
MAX_DEVICE_RETRIES=3
DEVICE_TIMEOUT=30

# Security Settings
ALLOWED_HOSTS=localhost,127.0.0.1,host.docker.internal
API_KEY_REQUIRED=false
```

### Configuration File
The agent uses `config.py` for additional settings:

```python
# Device Agent specific configuration
DEVICE_AGENT_CONFIG = {
    'port': 8084,
    'host': '0.0.0.0',
    'debug': True,
    'auto_start_adb': True,
    'device_scan_interval': 5,
    'max_concurrent_devices': 10
}
```

## Starting the Device Agent

### Manual Start
```bash
# Navigate to project directory
cd /path/to/MobileAppAutomation

# Start the device agent
python3 local_device_agent.py --port 8084

# Or with custom configuration
python3 local_device_agent.py --port 8084 --host 0.0.0.0 --debug
```

### Using Setup Script
```bash
# Start device agent
./docker-setup.sh agent start

# Check status
./docker-setup.sh agent status

# Stop device agent
./docker-setup.sh agent stop
```

### As System Service (Linux)

Create a systemd service file:

```bash
sudo tee /etc/systemd/system/device-agent.service > /dev/null <<EOF
[Unit]
Description=Mobile Device Agent
After=network.target

[Service]
Type=simple
User=$USER
WorkingDirectory=/path/to/MobileAppAutomation
ExecStart=/usr/bin/python3 local_device_agent.py --port 8084
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Enable and start service
sudo systemctl enable device-agent
sudo systemctl start device-agent
sudo systemctl status device-agent
```

## Verification and Testing

### Health Check
```bash
# Check if agent is running
curl http://localhost:8084/health

# Expected response:
# {"status": "healthy", "timestamp": "2024-01-15T10:30:00Z"}
```

### Device Discovery
```bash
# List all detected devices
curl http://localhost:8084/devices

# List Android devices
curl http://localhost:8084/android/devices

# List iOS devices
curl http://localhost:8084/ios/devices
```

### Test Device Communication
```bash
# Test Android device communication
curl -X POST http://localhost:8084/android/test-connection \
  -H "Content-Type: application/json" \
  -d '{"device_id": "your_device_id"}'

# Test iOS device communication
curl -X POST http://localhost:8084/ios/test-connection \
  -H "Content-Type: application/json" \
  -d '{"device_id": "your_device_id"}'
```

## Troubleshooting

### Common Issues

#### Device Not Detected
1. **Check USB Connection**: Ensure device is properly connected
2. **Enable USB Debugging**: Verify debugging is enabled on Android
3. **Trust Computer**: Ensure iOS device trusts the computer
4. **Check Permissions**: Verify user has USB device access
5. **Restart ADB**: `adb kill-server && adb start-server`

#### Permission Denied Errors
```bash
# Linux: Add user to plugdev group
sudo usermod -a -G plugdev $USER
# Log out and log back in

# macOS: Check System Preferences > Security & Privacy
# Allow terminal/IDE to access USB devices
```

#### Port Already in Use
```bash
# Find process using port 8084
lsof -i :8084

# Kill process if needed
kill -9 <PID>

# Or use different port
python3 local_device_agent.py --port 8085
```

### Logs and Debugging

```bash
# View agent logs
tail -f logs/device_agent.log

# Enable debug logging
export LOG_LEVEL=DEBUG
python3 local_device_agent.py --port 8084 --debug

# Check system logs (Linux)
journalctl -u device-agent -f
```

### Performance Monitoring

```bash
# Monitor agent performance
curl http://localhost:8084/metrics

# Check resource usage
top -p $(pgrep -f local_device_agent.py)
```

## Security Considerations

### Network Security
- The agent binds to `0.0.0.0` to allow container access
- Use firewall rules to restrict access if needed
- Consider enabling API key authentication for production

### Device Security
- Keep devices locked when not testing
- Use test devices, not personal devices
- Regularly update device firmware and security patches

### Host Security
- Run agent with minimal required privileges
- Keep Python and dependencies updated
- Monitor agent logs for suspicious activity

## Integration with Docker Services

Once the device agent is running, containerized services can connect using:

```yaml
# In docker-compose files
environment:
  DEVICE_AGENT_URL: http://host.docker.internal:8084
  DEVICE_AGENT_ENABLED: "true"
```

The agent provides a REST API that the containerized Android and iOS applications use to:
- Discover connected devices
- Execute device commands
- Transfer files
- Monitor device status
- Handle device-specific operations

This architecture ensures reliable device communication while maintaining the benefits of containerized application environments.