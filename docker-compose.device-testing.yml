version: '3.8'

# Device Testing Override for docker-compose.yml
# Usage: docker-compose -f docker-compose.yml -f docker-compose.device-testing.yml up
# This configuration enables physical device testing with proper device access

services:
  # NOTE: Local Device Agent runs directly on host machine
  # Start it manually with: python3 local_device_agent.py --port 8084
  # The agent must run on the host to access physical USB devices

  # Android App with Device Agent Integration
  android_app:
    environment:
      # Device Agent Configuration (runs on host)
      DEVICE_AGENT_URL: http://host.docker.internal:8084
      DEVICE_AGENT_ENABLED: "true"
      
      # Enhanced Device Detection
      ADB_SERVER_HOST: host.docker.internal
      ADB_SERVER_PORT: 5037
      
      # Testing Configuration
      ENABLE_DEVICE_TESTING: "true"
      DEVICE_POLLING_INTERVAL: 5
      
      # Debug Configuration
      DEBUG_DEVICE_DISCOVERY: "true"
      LOG_LEVEL: DEBUG
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    # Remove direct USB access since device_agent handles it
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
      - ./temp_android:/app/temp
    # Remove privileged and devices since not needed with device_agent
    privileged: false

  # iOS App with Device Agent Integration  
  ios_app:
    environment:
      # Device Agent Configuration (runs on host)
      DEVICE_AGENT_URL: http://host.docker.internal:8084
      DEVICE_AGENT_ENABLED: "true"
      
      # iOS Device Configuration
      IOS_PROXY_HOST: host.docker.internal
      IOS_PROXY_PORT: 8100
      
      # Testing Configuration
      ENABLE_DEVICE_TESTING: "true"
      DEVICE_POLLING_INTERVAL: 5
      
      # Debug Configuration
      DEBUG_DEVICE_DISCOVERY: "true"
      LOG_LEVEL: DEBUG
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
      - ./temp_ios:/app/temp

  # Device Testing Dashboard
  device_dashboard:
    build:
      context: .
      dockerfile: Dockerfile.dashboard
    container_name: device_testing_dashboard
    environment:
      DASHBOARD_PORT: 8090
      DEVICE_AGENT_URL: http://host.docker.internal:8084
      ANDROID_APP_URL: http://android_app:8083
      IOS_APP_URL: http://ios_app:8088
    ports:
      - "8090:8090"
    networks:
      - saas_network
    depends_on:
      - android_app
      - ios_app
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8090/health"]
      interval: 30s
      timeout: 10s
      retries: 3

# Additional volumes for device testing
volumes:
  device_agent_logs:
    driver: local
  device_temp_data:
    driver: local

# Network configuration remains the same
networks:
  saas_network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16
          gateway: **********