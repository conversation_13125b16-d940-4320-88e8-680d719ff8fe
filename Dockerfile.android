# Dockerfile for Android Application Isolation
FROM python:3.10-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1
ENV FLASK_ENV=production
ENV APPIUM_PORT=4724
ENV FLASK_PORT=8081
ENV JAVA_HOME=/usr/lib/jvm/default-java
ENV ANDROID_HOME=/opt/android-sdk
ENV PATH=$PATH:$ANDROID_HOME/tools:$ANDROID_HOME/platform-tools

# Install system dependencies
RUN apt-get update && apt-get install -y \
    build-essential \
    curl \
    git \
    wget \
    unzip \
    default-jdk \
    libusb-1.0-0 \
    usbutils \
    libgl1-mesa-dri \
    libglib2.0-0 \
    libsm6 \
    libxext6 \
    libxrender-dev \
    libgomp1 \
    libgthread-2.0-0 \
    ffmpeg \
    && rm -rf /var/lib/apt/lists/*

# Install Node.js and Appium
RUN curl -fsSL https://deb.nodesource.com/setup_18.x | bash - \
    && apt-get install -y nodejs
RUN npm install -g appium@2.0.0
RUN npm install -g @appium/doctor
RUN appium driver install uiautomator2

# Install Android SDK (minimal setup)
RUN mkdir -p $ANDROID_HOME && \
    cd $ANDROID_HOME && \
    wget -q https://dl.google.com/android/repository/commandlinetools-linux-9477386_latest.zip && \
    unzip commandlinetools-linux-9477386_latest.zip && \
    rm commandlinetools-linux-9477386_latest.zip && \
    mkdir -p cmdline-tools/latest && \
    mv cmdline-tools/* cmdline-tools/latest/ || true

# Create app directory
WORKDIR /app

# Copy requirements and install Python dependencies
COPY requirements.txt .
RUN pip install --no-cache-dir -r requirements.txt

# Copy application code
COPY app_android/ ./app_android/
COPY run_android.py .
COPY config_android.py .
COPY utils/ ./utils/
COPY templates/ ./templates/
COPY static/ ./static/

# Create necessary directories
RUN mkdir -p logs uploads temp

# Expose ports
EXPOSE 8081 4724

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:${FLASK_PORT:-8081}/health || exit 1

# Start command - use environment variables for dynamic port configuration
CMD ["sh", "-c", "python run_android.py --port ${FLASK_PORT:-8081} --appium-port ${APPIUM_PORT:-4724}"]