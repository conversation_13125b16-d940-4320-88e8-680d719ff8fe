# Production Deployment Guide - SaaS Mobile App Automation Platform

## Overview

This guide provides comprehensive instructions for deploying the SaaS Mobile App Automation platform to production servers without Docker. This deployment method offers better performance, direct hardware access, and simplified maintenance for production environments.

## Architecture Overview

### Production Architecture
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Load Balancer │    │   Web Server    │    │   Database      │
│   (Nginx/HAProxy│────│   (SaaS Server) │────│   (PostgreSQL)  │
│   SSL Termination│    │   Port 8080     │    │   Port 5432     │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                │
                       ┌─────────────────┐
                       │   Redis Cache   │
                       │   Port 6379     │
                       └─────────────────┘
                                │
                ┌───────────────┼───────────────┐
                │               │               │
        ┌───────────────┐ ┌───────────────┐ ┌───────────────┐
        │ Android App   │ │   iOS App     │ │ Agent Manager │
        │ Port 8081+    │ │ Port 8088+    │ │ WebSocket     │
        └───────────────┘ └───────────────┘ └───────────────┘
```

## Server Requirements

### Minimum Hardware Requirements
- **CPU**: 8 cores (16 threads recommended)
- **RAM**: 32GB (64GB recommended for high load)
- **Storage**: 500GB SSD (1TB recommended)
- **Network**: 1Gbps connection
- **OS**: Ubuntu 20.04 LTS or CentOS 8+

### Recommended Hardware for Production
- **CPU**: 16 cores (32 threads)
- **RAM**: 64GB
- **Storage**: 1TB NVMe SSD
- **Network**: 10Gbps connection
- **Backup Storage**: Additional 2TB for backups

## Pre-Installation Setup

### 1. System Preparation

```bash
# Update system packages
sudo apt update && sudo apt upgrade -y

# Install essential packages
sudo apt install -y curl wget git build-essential software-properties-common

# Create application user
sudo useradd -m -s /bin/bash mobileapp
sudo usermod -aG sudo mobileapp

# Create application directories
sudo mkdir -p /opt/mobileapp
sudo mkdir -p /var/log/mobileapp
sudo mkdir -p /var/lib/mobileapp
sudo chown -R mobileapp:mobileapp /opt/mobileapp /var/log/mobileapp /var/lib/mobileapp
```

### 2. Install Python 3.9+

```bash
# Install Python 3.9
sudo add-apt-repository ppa:deadsnakes/ppa
sudo apt update
sudo apt install -y python3.9 python3.9-venv python3.9-dev python3-pip

# Set Python 3.9 as default
sudo update-alternatives --install /usr/bin/python3 python3 /usr/bin/python3.9 1

# Install pip for Python 3.9
curl https://bootstrap.pypa.io/get-pip.py | sudo python3.9
```

### 3. Install Node.js 18+

```bash
# Install Node.js 18
curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
sudo apt-get install -y nodejs

# Verify installation
node --version
npm --version
```

### 4. Install Database (PostgreSQL)

```bash
# Install PostgreSQL 14
sudo apt install -y postgresql-14 postgresql-client-14 postgresql-contrib-14

# Start and enable PostgreSQL
sudo systemctl start postgresql
sudo systemctl enable postgresql

# Create database and user
sudo -u postgres psql << EOF
CREATE DATABASE mobileapp_saas;
CREATE USER mobileapp_user WITH ENCRYPTED PASSWORD 'secure_password_here';
GRANT ALL PRIVILEGES ON DATABASE mobileapp_saas TO mobileapp_user;
ALTER USER mobileapp_user CREATEDB;
\q
EOF
```

### 5. Install Redis

```bash
# Install Redis
sudo apt install -y redis-server

# Configure Redis
sudo sed -i 's/^# maxmemory <bytes>/maxmemory 2gb/' /etc/redis/redis.conf
sudo sed -i 's/^# maxmemory-policy noeviction/maxmemory-policy allkeys-lru/' /etc/redis/redis.conf

# Start and enable Redis
sudo systemctl start redis-server
sudo systemctl enable redis-server
```

### 6. Install Nginx

```bash
# Install Nginx
sudo apt install -y nginx

# Start and enable Nginx
sudo systemctl start nginx
sudo systemctl enable nginx
```

## Application Deployment

### 1. Clone and Setup Application

```bash
# Switch to application user
sudo su - mobileapp

# Clone repository
cd /opt/mobileapp
git clone <repository-url> .

# Create Python virtual environment
python3.9 -m venv venv
source venv/bin/activate

# Install Python dependencies
pip install --upgrade pip
pip install -r requirements_saas.txt

# Install additional production dependencies
pip install gunicorn psycopg2-binary redis
```

### 2. Configure Application

```bash
# Create production configuration
cat > /opt/mobileapp/config/production.py << 'EOF'
import os

class ProductionConfig:
    # Database Configuration
    SQLALCHEMY_DATABASE_URI = 'postgresql://mobileapp_user:secure_password_here@localhost:5432/mobileapp_saas'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    SQLALCHEMY_ENGINE_OPTIONS = {
        'pool_size': 20,
        'pool_recycle': 3600,
        'pool_pre_ping': True
    }
    
    # Redis Configuration
    REDIS_URL = 'redis://localhost:6379/0'
    
    # Security Configuration
    SECRET_KEY = os.environ.get('SECRET_KEY', 'your-super-secret-key-here')
    JWT_SECRET_KEY = os.environ.get('JWT_SECRET_KEY', 'your-jwt-secret-key-here')
    
    # Rate Limiting
    RATELIMIT_STORAGE_URL = 'redis://localhost:6379/1'
    RATELIMIT_DEFAULT = '1000 per hour'
    
    # Session Configuration
    SESSION_TYPE = 'redis'
    SESSION_REDIS = 'redis://localhost:6379/2'
    SESSION_PERMANENT = False
    SESSION_USE_SIGNER = True
    
    # Application Configuration
    DEBUG = False
    TESTING = False
    
    # Logging
    LOG_LEVEL = 'INFO'
    LOG_FILE = '/var/log/mobileapp/app.log'
    
    # Port Configuration
    PORT_RANGE_START = 8081
    PORT_RANGE_END = 8200
    
    # File Upload
    MAX_CONTENT_LENGTH = 100 * 1024 * 1024  # 100MB
    UPLOAD_FOLDER = '/var/lib/mobileapp/uploads'
    
    # SSL Configuration (if using HTTPS)
    SSL_DISABLE = False
EOF

# Create environment file
cat > /opt/mobileapp/.env << 'EOF'
FLASK_ENV=production
FLASK_APP=saas_unified_server.py
SECRET_KEY=your-super-secret-key-change-this-in-production
JWT_SECRET_KEY=your-jwt-secret-key-change-this-in-production
DATABASE_URL=postgresql://mobileapp_user:secure_password_here@localhost:5432/mobileapp_saas
REDIS_URL=redis://localhost:6379/0
EOF

# Set proper permissions
chmod 600 /opt/mobileapp/.env
```

### 3. Initialize Database

```bash
# Activate virtual environment
source /opt/mobileapp/venv/bin/activate

# Initialize database
cd /opt/mobileapp
python3 -c "
from saas_unified_server import app, db
with app.app_context():
    db.create_all()
    print('Database initialized successfully')
"

# Create admin user
python3 setup_test_user.py --username admin --email <EMAIL> --password secure_admin_password
```

### 4. Install Mobile Development Tools

#### Android Development Setup

```bash
# Install Java 11
sudo apt install -y openjdk-11-jdk

# Download and install Android SDK
wget https://dl.google.com/android/repository/commandlinetools-linux-8512546_latest.zip
unzip commandlinetools-linux-8512546_latest.zip -d /opt/mobileapp/android-sdk

# Set environment variables
echo 'export ANDROID_HOME=/opt/mobileapp/android-sdk' >> ~/.bashrc
echo 'export PATH=$PATH:$ANDROID_HOME/cmdline-tools/latest/bin:$ANDROID_HOME/platform-tools' >> ~/.bashrc
source ~/.bashrc

# Install Android SDK components
yes | sdkmanager --sdk_root=$ANDROID_HOME "platform-tools" "platforms;android-30" "build-tools;30.0.3"

# Install ADB
sudo apt install -y android-tools-adb android-tools-fastboot
```

#### iOS Development Setup (macOS only)

```bash
# Install Xcode Command Line Tools
xcode-select --install

# Install iOS development tools
brew install libimobiledevice
brew install ideviceinstaller
brew install ios-webkit-debug-proxy

# Install WebDriverAgent dependencies
npm install -g ios-deploy
```

### 5. Install Appium

```bash
# Install Appium globally
npm install -g appium@2.0.0

# Install Appium drivers
appium driver install uiautomator2
appium driver install xcuitest  # macOS only

# Verify Appium installation
appium doctor
```

## Service Configuration

### 1. Create Systemd Services

#### Main SaaS Server Service

```bash
sudo tee /etc/systemd/system/mobileapp-saas.service << 'EOF'
[Unit]
Description=Mobile App Automation SaaS Server
After=network.target postgresql.service redis.service
Requires=postgresql.service redis.service

[Service]
Type=exec
User=mobileapp
Group=mobileapp
WorkingDirectory=/opt/mobileapp
Environment=PATH=/opt/mobileapp/venv/bin
EnvironmentFile=/opt/mobileapp/.env
ExecStart=/opt/mobileapp/venv/bin/gunicorn --bind 127.0.0.1:8080 --workers 4 --worker-class eventlet --worker-connections 1000 --timeout 300 --keep-alive 2 --max-requests 1000 --max-requests-jitter 100 --preload --access-logfile /var/log/mobileapp/access.log --error-logfile /var/log/mobileapp/error.log saas_unified_server:app
ExecReload=/bin/kill -s HUP $MAINPID
Restart=always
RestartSec=10
KillMode=mixed
TimeoutStopSec=5
PrivateTmp=true
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
EOF
```

#### Android App Service

```bash
sudo tee /etc/systemd/system/mobileapp-android.service << 'EOF'
[Unit]
Description=Mobile App Automation Android Service
After=network.target mobileapp-saas.service
Requires=mobileapp-saas.service

[Service]
Type=exec
User=mobileapp
Group=mobileapp
WorkingDirectory=/opt/mobileapp
Environment=PATH=/opt/mobileapp/venv/bin
EnvironmentFile=/opt/mobileapp/.env
ExecStart=/opt/mobileapp/venv/bin/python3 run_android.py --flask-port 8081
Restart=always
RestartSec=10
PrivateTmp=true
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
EOF
```

#### iOS App Service (macOS only)

```bash
sudo tee /etc/systemd/system/mobileapp-ios.service << 'EOF'
[Unit]
Description=Mobile App Automation iOS Service
After=network.target mobileapp-saas.service
Requires=mobileapp-saas.service

[Service]
Type=exec
User=mobileapp
Group=mobileapp
WorkingDirectory=/opt/mobileapp
Environment=PATH=/opt/mobileapp/venv/bin
EnvironmentFile=/opt/mobileapp/.env
ExecStart=/opt/mobileapp/venv/bin/python3 run.py --flask-port 8088
Restart=always
RestartSec=10
PrivateTmp=true
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
EOF
```

#### Appium Service

```bash
sudo tee /etc/systemd/system/appium.service << 'EOF'
[Unit]
Description=Appium Server
After=network.target

[Service]
Type=exec
User=mobileapp
Group=mobileapp
WorkingDirectory=/opt/mobileapp
ExecStart=/usr/bin/appium server --port 4723 --log-level info --log /var/log/mobileapp/appium.log
Restart=always
RestartSec=10
PrivateTmp=true
NoNewPrivileges=true

[Install]
WantedBy=multi-user.target
EOF
```

### 2. Configure Nginx

```bash
sudo tee /etc/nginx/sites-available/mobileapp << 'EOF'
# Rate limiting
limit_req_zone $binary_remote_addr zone=api:10m rate=10r/s;
limit_req_zone $binary_remote_addr zone=login:10m rate=5r/m;

# Upstream servers
upstream saas_backend {
    server 127.0.0.1:8080;
    keepalive 32;
}

upstream android_backend {
    server 127.0.0.1:8081;
    keepalive 16;
}

upstream ios_backend {
    server 127.0.0.1:8088;
    keepalive 16;
}

server {
    listen 80;
    server_name your-domain.com www.your-domain.com;
    
    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com www.your-domain.com;
    
    # SSL Configuration
    ssl_certificate /etc/ssl/certs/your-domain.crt;
    ssl_certificate_key /etc/ssl/private/your-domain.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Security headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    
    # Client max body size
    client_max_body_size 100M;
    
    # Timeouts
    proxy_connect_timeout 60s;
    proxy_send_timeout 60s;
    proxy_read_timeout 60s;
    
    # Main SaaS application
    location / {
        limit_req zone=api burst=20 nodelay;
        
        proxy_pass http://saas_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket support
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
    
    # Login endpoint with stricter rate limiting
    location /api/auth/login {
        limit_req zone=login burst=5 nodelay;
        
        proxy_pass http://saas_backend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Android app routes
    location /android/ {
        proxy_pass http://android_backend/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # iOS app routes
    location /ios/ {
        proxy_pass http://ios_backend/;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
    
    # Static files
    location /static/ {
        alias /opt/mobileapp/static/;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # Health check
    location /health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }
}
EOF

# Enable the site
sudo ln -sf /etc/nginx/sites-available/mobileapp /etc/nginx/sites-enabled/
sudo rm -f /etc/nginx/sites-enabled/default

# Test Nginx configuration
sudo nginx -t
```

### 3. SSL Certificate Setup

#### Using Let's Encrypt (Recommended)

```bash
# Install Certbot
sudo apt install -y certbot python3-certbot-nginx

# Obtain SSL certificate
sudo certbot --nginx -d your-domain.com -d www.your-domain.com

# Set up automatic renewal
sudo crontab -e
# Add this line:
0 12 * * * /usr/bin/certbot renew --quiet
```

#### Using Custom SSL Certificate

```bash
# Copy your SSL certificate files
sudo cp your-domain.crt /etc/ssl/certs/
sudo cp your-domain.key /etc/ssl/private/
sudo chmod 644 /etc/ssl/certs/your-domain.crt
sudo chmod 600 /etc/ssl/private/your-domain.key
```

## Firewall Configuration

```bash
# Install UFW
sudo apt install -y ufw

# Configure firewall rules
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp

# Allow internal communication
sudo ufw allow from 127.0.0.1 to any port 8080
sudo ufw allow from 127.0.0.1 to any port 8081
sudo ufw allow from 127.0.0.1 to any port 8088
sudo ufw allow from 127.0.0.1 to any port 5432
sudo ufw allow from 127.0.0.1 to any port 6379

# Enable firewall
sudo ufw --force enable
```

## Logging Configuration

### 1. Configure Log Rotation

```bash
sudo tee /etc/logrotate.d/mobileapp << 'EOF'
/var/log/mobileapp/*.log {
    daily
    missingok
    rotate 30
    compress
    delaycompress
    notifempty
    create 644 mobileapp mobileapp
    postrotate
        systemctl reload mobileapp-saas
    endscript
}
EOF
```

### 2. Configure Rsyslog

```bash
sudo tee /etc/rsyslog.d/50-mobileapp.conf << 'EOF'
# Mobile App Automation logs
if $programname == 'mobileapp-saas' then /var/log/mobileapp/saas.log
if $programname == 'mobileapp-android' then /var/log/mobileapp/android.log
if $programname == 'mobileapp-ios' then /var/log/mobileapp/ios.log
& stop
EOF

sudo systemctl restart rsyslog
```

## Monitoring Setup

### 1. Install Monitoring Tools

```bash
# Install system monitoring
sudo apt install -y htop iotop nethogs

# Install process monitoring
sudo apt install -y supervisor
```

### 2. Configure Health Checks

```bash
# Create health check script
sudo tee /opt/mobileapp/scripts/health_check.sh << 'EOF'
#!/bin/bash

# Check main services
services=("mobileapp-saas" "postgresql" "redis-server" "nginx")

for service in "${services[@]}"; do
    if ! systemctl is-active --quiet "$service"; then
        echo "ERROR: $service is not running"
        exit 1
    fi
done

# Check HTTP endpoints
if ! curl -f -s http://localhost:8080/health > /dev/null; then
    echo "ERROR: SaaS server health check failed"
    exit 1
fi

echo "All services are healthy"
exit 0
EOF

chmod +x /opt/mobileapp/scripts/health_check.sh

# Add to crontab for monitoring
(crontab -l 2>/dev/null; echo "*/5 * * * * /opt/mobileapp/scripts/health_check.sh") | crontab -
```

## Backup Configuration

### 1. Database Backup

```bash
# Create backup script
sudo tee /opt/mobileapp/scripts/backup_db.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/var/lib/mobileapp/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/mobileapp_saas_$DATE.sql"

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Create database backup
pg_dump -h localhost -U mobileapp_user -d mobileapp_saas > "$BACKUP_FILE"

# Compress backup
gzip "$BACKUP_FILE"

# Remove backups older than 30 days
find "$BACKUP_DIR" -name "*.sql.gz" -mtime +30 -delete

echo "Database backup completed: ${BACKUP_FILE}.gz"
EOF

chmod +x /opt/mobileapp/scripts/backup_db.sh

# Schedule daily backups
(crontab -l 2>/dev/null; echo "0 2 * * * /opt/mobileapp/scripts/backup_db.sh") | crontab -
```

### 2. Application Backup

```bash
# Create application backup script
sudo tee /opt/mobileapp/scripts/backup_app.sh << 'EOF'
#!/bin/bash

BACKUP_DIR="/var/lib/mobileapp/backups"
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="$BACKUP_DIR/mobileapp_files_$DATE.tar.gz"

# Create backup directory
mkdir -p "$BACKUP_DIR"

# Create application files backup
tar -czf "$BACKUP_FILE" \
    --exclude='venv' \
    --exclude='__pycache__' \
    --exclude='*.pyc' \
    --exclude='.git' \
    --exclude='logs' \
    --exclude='temp*' \
    -C /opt/mobileapp .

# Remove backups older than 7 days
find "$BACKUP_DIR" -name "mobileapp_files_*.tar.gz" -mtime +7 -delete

echo "Application backup completed: $BACKUP_FILE"
EOF

chmod +x /opt/mobileapp/scripts/backup_app.sh

# Schedule weekly backups
(crontab -l 2>/dev/null; echo "0 3 * * 0 /opt/mobileapp/scripts/backup_app.sh") | crontab -
```

## Service Management

### 1. Enable and Start Services

```bash
# Reload systemd
sudo systemctl daemon-reload

# Enable services
sudo systemctl enable mobileapp-saas
sudo systemctl enable mobileapp-android
sudo systemctl enable mobileapp-ios  # macOS only
sudo systemctl enable appium
sudo systemctl enable nginx

# Start services
sudo systemctl start mobileapp-saas
sudo systemctl start mobileapp-android
sudo systemctl start mobileapp-ios  # macOS only
sudo systemctl start appium
sudo systemctl restart nginx
```

### 2. Service Management Commands

```bash
# Check service status
sudo systemctl status mobileapp-saas
sudo systemctl status mobileapp-android
sudo systemctl status mobileapp-ios
sudo systemctl status appium
sudo systemctl status nginx

# View service logs
sudo journalctl -u mobileapp-saas -f
sudo journalctl -u mobileapp-android -f
sudo journalctl -u mobileapp-ios -f
sudo journalctl -u appium -f

# Restart services
sudo systemctl restart mobileapp-saas
sudo systemctl restart mobileapp-android
sudo systemctl restart mobileapp-ios
sudo systemctl restart appium
sudo systemctl reload nginx

# Stop services
sudo systemctl stop mobileapp-saas
sudo systemctl stop mobileapp-android
sudo systemctl stop mobileapp-ios
sudo systemctl stop appium
```

## Performance Optimization

### 1. Database Optimization

```bash
# Optimize PostgreSQL configuration
sudo tee -a /etc/postgresql/14/main/postgresql.conf << 'EOF'

# Performance tuning
shared_buffers = 8GB
effective_cache_size = 24GB
maintenance_work_mem = 2GB
checkpoint_completion_target = 0.9
wal_buffers = 16MB
default_statistics_target = 100
random_page_cost = 1.1
effective_io_concurrency = 200
work_mem = 64MB
min_wal_size = 2GB
max_wal_size = 8GB
max_worker_processes = 16
max_parallel_workers_per_gather = 4
max_parallel_workers = 16
max_parallel_maintenance_workers = 4
EOF

# Restart PostgreSQL
sudo systemctl restart postgresql
```

### 2. Redis Optimization

```bash
# Optimize Redis configuration
sudo tee -a /etc/redis/redis.conf << 'EOF'

# Performance tuning
maxmemory 4gb
maxmemory-policy allkeys-lru
tcp-keepalive 300
timeout 0
tcp-backlog 511
save 900 1
save 300 10
save 60 10000
stop-writes-on-bgsave-error yes
rdbcompression yes
rdbchecksum yes
EOF

# Restart Redis
sudo systemctl restart redis-server
```

### 3. System Optimization

```bash
# Optimize system limits
sudo tee /etc/security/limits.d/mobileapp.conf << 'EOF'
mobileapp soft nofile 65536
mobileapp hard nofile 65536
mobileapp soft nproc 32768
mobileapp hard nproc 32768
EOF

# Optimize kernel parameters
sudo tee /etc/sysctl.d/99-mobileapp.conf << 'EOF'
# Network optimization
net.core.somaxconn = 65535
net.core.netdev_max_backlog = 5000
net.ipv4.tcp_max_syn_backlog = 65535
net.ipv4.tcp_fin_timeout = 30
net.ipv4.tcp_keepalive_time = 1200
net.ipv4.tcp_max_tw_buckets = 400000

# Memory optimization
vm.swappiness = 10
vm.dirty_ratio = 15
vm.dirty_background_ratio = 5

# File system optimization
fs.file-max = 2097152
EOF

# Apply kernel parameters
sudo sysctl -p /etc/sysctl.d/99-mobileapp.conf
```

## Security Hardening

### 1. Application Security

```bash
# Set proper file permissions
sudo chown -R mobileapp:mobileapp /opt/mobileapp
sudo chmod -R 755 /opt/mobileapp
sudo chmod 600 /opt/mobileapp/.env
sudo chmod 600 /opt/mobileapp/config/production.py

# Secure log files
sudo chown -R mobileapp:mobileapp /var/log/mobileapp
sudo chmod -R 640 /var/log/mobileapp

# Secure data directory
sudo chown -R mobileapp:mobileapp /var/lib/mobileapp
sudo chmod -R 750 /var/lib/mobileapp
```

### 2. Database Security

```bash
# Secure PostgreSQL
sudo -u postgres psql << 'EOF'
-- Remove default postgres user privileges
ALTER USER postgres WITH NOLOGIN;

-- Create read-only user for monitoring
CREATE USER mobileapp_readonly WITH ENCRYPTED PASSWORD 'readonly_password_here';
GRANT CONNECT ON DATABASE mobileapp_saas TO mobileapp_readonly;
GRANT USAGE ON SCHEMA public TO mobileapp_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO mobileapp_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO mobileapp_readonly;
\q
EOF

# Configure PostgreSQL authentication
sudo sed -i 's/#listen_addresses = .*/listen_addresses = \'localhost\'/' /etc/postgresql/14/main/postgresql.conf
sudo sed -i 's/local   all             all                                     peer/local   all             all                                     md5/' /etc/postgresql/14/main/pg_hba.conf

# Restart PostgreSQL
sudo systemctl restart postgresql
```

### 3. Network Security

```bash
# Configure fail2ban
sudo apt install -y fail2ban

sudo tee /etc/fail2ban/jail.local << 'EOF'
[DEFAULT]
bantime = 3600
findtime = 600
maxretry = 5

[sshd]
enabled = true

[nginx-http-auth]
enabled = true

[nginx-limit-req]
enabled = true
filter = nginx-limit-req
action = iptables-multiport[name=ReqLimit, port="http,https", protocol=tcp]
logpath = /var/log/nginx/error.log
maxretry = 10
findtime = 600
bantime = 7200
EOF

sudo systemctl enable fail2ban
sudo systemctl start fail2ban
```

## Deployment Verification

### 1. Service Health Checks

```bash
# Check all services
sudo systemctl status mobileapp-saas mobileapp-android mobileapp-ios appium nginx postgresql redis-server

# Check service logs for errors
sudo journalctl -u mobileapp-saas --since "1 hour ago" | grep -i error
sudo journalctl -u nginx --since "1 hour ago" | grep -i error
```

### 2. Application Testing

```bash
# Test HTTP endpoints
curl -I https://your-domain.com/health
curl -I https://your-domain.com/

# Test API endpoints
curl -X POST https://your-domain.com/api/auth/login \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"secure_admin_password"}'
```

### 3. Performance Testing

```bash
# Install Apache Bench
sudo apt install -y apache2-utils

# Test concurrent connections
ab -n 1000 -c 10 https://your-domain.com/

# Test API performance
ab -n 100 -c 5 -p login_data.json -T application/json https://your-domain.com/api/auth/login
```

## Maintenance Procedures

### 1. Regular Maintenance Tasks

```bash
# Create maintenance script
sudo tee /opt/mobileapp/scripts/maintenance.sh << 'EOF'
#!/bin/bash

echo "Starting maintenance tasks..."

# Update system packages
sudo apt update && sudo apt upgrade -y

# Clean up old log files
find /var/log/mobileapp -name "*.log.*" -mtime +30 -delete

# Clean up temporary files
find /opt/mobileapp/temp* -type f -mtime +7 -delete 2>/dev/null || true

# Vacuum database
sudo -u postgres psql -d mobileapp_saas -c "VACUUM ANALYZE;"

# Restart services to clear memory
sudo systemctl restart mobileapp-saas
sudo systemctl restart mobileapp-android
sudo systemctl restart mobileapp-ios

echo "Maintenance tasks completed."
EOF

chmod +x /opt/mobileapp/scripts/maintenance.sh

# Schedule monthly maintenance
(crontab -l 2>/dev/null; echo "0 4 1 * * /opt/mobileapp/scripts/maintenance.sh") | crontab -
```

### 2. Update Procedures

```bash
# Create update script
sudo tee /opt/mobileapp/scripts/update.sh << 'EOF'
#!/bin/bash

echo "Starting application update..."

# Backup current version
/opt/mobileapp/scripts/backup_app.sh

# Stop services
sudo systemctl stop mobileapp-saas mobileapp-android mobileapp-ios

# Update code
cd /opt/mobileapp
git pull origin main

# Update dependencies
source venv/bin/activate
pip install --upgrade -r requirements_saas.txt

# Run database migrations (if any)
python3 -c "from saas_unified_server import app, db; app.app_context().push(); db.create_all()"

# Start services
sudo systemctl start mobileapp-saas mobileapp-android mobileapp-ios

# Verify deployment
sleep 10
curl -f http://localhost:8080/health

echo "Application update completed."
EOF

chmod +x /opt/mobileapp/scripts/update.sh
```

## Troubleshooting

### Common Issues and Solutions

1. **Service Won't Start**
   ```bash
   # Check service status and logs
   sudo systemctl status mobileapp-saas
   sudo journalctl -u mobileapp-saas -n 50
   
   # Check configuration
   sudo -u mobileapp /opt/mobileapp/venv/bin/python3 -c "from saas_unified_server import app; print('Config OK')"
   ```

2. **Database Connection Issues**
   ```bash
   # Test database connection
   sudo -u postgres psql -d mobileapp_saas -c "SELECT version();"
   
   # Check PostgreSQL logs
   sudo tail -f /var/log/postgresql/postgresql-14-main.log
   ```

3. **High Memory Usage**
   ```bash
   # Monitor memory usage
   free -h
   ps aux --sort=-%mem | head -20
   
   # Restart services to clear memory
   sudo systemctl restart mobileapp-saas
   ```

4. **SSL Certificate Issues**
   ```bash
   # Check certificate validity
   openssl x509 -in /etc/ssl/certs/your-domain.crt -text -noout
   
   # Test SSL configuration
   openssl s_client -connect your-domain.com:443
   ```

### Emergency Procedures

1. **Service Recovery**
   ```bash
   # Quick service restart
   sudo systemctl restart mobileapp-saas mobileapp-android mobileapp-ios nginx
   
   # Full system recovery
   sudo systemctl restart postgresql redis-server
   sudo systemctl restart mobileapp-saas mobileapp-android mobileapp-ios
   ```

2. **Database Recovery**
   ```bash
   # Restore from backup
   sudo systemctl stop mobileapp-saas
   sudo -u postgres dropdb mobileapp_saas
   sudo -u postgres createdb mobileapp_saas
   sudo -u postgres psql mobileapp_saas < /var/lib/mobileapp/backups/latest_backup.sql
   sudo systemctl start mobileapp-saas
   ```

## Support and Documentation

### Log Locations
- Application logs: `/var/log/mobileapp/`
- Nginx logs: `/var/log/nginx/`
- PostgreSQL logs: `/var/log/postgresql/`
- System logs: `/var/log/syslog`

### Configuration Files
- Application config: `/opt/mobileapp/config/production.py`
- Environment variables: `/opt/mobileapp/.env`
- Nginx config: `/etc/nginx/sites-available/mobileapp`
- Service files: `/etc/systemd/system/mobileapp-*.service`

### Monitoring URLs
- Health check: `https://your-domain.com/health`
- Application: `https://your-domain.com/`
- Admin interface: `https://your-domain.com/admin` (if implemented)

---

**Note**: This deployment guide provides a comprehensive production setup. Adjust configurations based on your specific requirements, security policies, and infrastructure constraints. Always test deployments in a staging environment before applying to production.