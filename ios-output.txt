2025-08-14 20:08:38,738 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-14 20:08:38,739 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-14 20:08:38,740 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/ios_data/test_cases
2025-08-14 20:08:38,740 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios
2025-08-14 20:08:38,740 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_ios
2025-08-14 20:08:38,741 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/ios_data/reference_images
2025-08-14 20:08:38,741 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/ios_data/test_suites
2025-08-14 20:08:38,742 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_ios/suites
2025-08-14 20:08:38,742 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings_ios
2025-08-14 20:08:38,743 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_ios
2025-08-14 20:08:38,744 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-08-14 20:08:38,744 - __main__ - INFO - Using intelligent port management for iOS platform...
2025-08-14 20:08:38,744 - __main__ - WARNING - Port manager not available, falling back to basic cleanup
2025-08-14 20:08:38,744 - __main__ - INFO - Using default ports - killing existing processes to avoid conflicts
2025-08-14 20:08:38,744 - __main__ - INFO - Killing any existing Appium and iproxy processes...
2025-08-14 20:08:40,833 - __main__ - INFO - Existing processes terminated
2025-08-14 20:08:42,577 - utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/app/data/global_values.db
2025-08-14 20:08:42,578 - utils.global_values_db - INFO - Global values database initialized successfully
2025-08-14 20:08:42,578 - utils.global_values_db - INFO - Using global values from config.py
2025-08-14 20:08:42,578 - utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 2, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
2025-08-14 20:08:42,581 - utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
2025-08-14 20:08:42,581 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-08-14 20:08:42,620 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-08-14 20:08:43,282 - utils.database - INFO - === UPDATING TEST_STEPS TABLE SCHEMA ===
2025-08-14 20:08:43,283 - utils.database - INFO - Test_steps table schema updated successfully
2025-08-14 20:08:43,283 - utils.database - INFO - === UPDATING SCREENSHOTS TABLE SCHEMA ===
2025-08-14 20:08:43,284 - utils.database - INFO - Screenshots table schema updated successfully
2025-08-14 20:08:43,284 - utils.database - INFO - === UPDATING EXECUTION_TRACKING TABLE SCHEMA ===
2025-08-14 20:08:43,284 - utils.database - INFO - step_idx column already exists in execution_tracking table
2025-08-14 20:08:43,284 - utils.database - INFO - action_type column already exists in execution_tracking table
2025-08-14 20:08:43,284 - utils.database - INFO - action_params column already exists in execution_tracking table
2025-08-14 20:08:43,284 - utils.database - INFO - action_id column already exists in execution_tracking table
2025-08-14 20:08:43,285 - utils.database - INFO - Successfully updated execution_tracking table schema
2025-08-14 20:08:43,285 - utils.database - INFO - Database initialized successfully
2025-08-14 20:08:43,285 - utils.database - INFO - Checking initial database state...
2025-08-14 20:08:43,286 - utils.database - INFO - Database state: 0 suites, 0 cases, 0 steps, 0 screenshots, 0 tracking entries
2025-08-14 20:08:43,308 - app - INFO - Using directories from config.py:
2025-08-14 20:08:43,308 - app - INFO -   - TEST_CASES_DIR: /Users/<USER>/Documents/automation-tool/ios_data/test_cases
2025-08-14 20:08:43,308 - app - INFO -   - REFERENCE_IMAGES_DIR: /Users/<USER>/Documents/automation-tool/ios_data/reference_images
2025-08-14 20:08:43,308 - app - INFO -   - SCREENSHOTS_DIR: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_ios
2025-08-14 20:08:43,308 - utils.test_case_manager - INFO - TestCaseManager initialized with directory: /Users/<USER>/Documents/automation-tool/ios_data/test_cases
2025-08-14 20:08:43,308 - utils.test_case_manager - INFO - TestCaseManager initialized with directory: /Users/<USER>/Documents/automation-tool/ios_data/test_cases
[2025-08-14 20:08:43,398] INFO in appium_device_controller: Initialized AppiumDeviceController with Appium port: 4723, WDA port: 8200
[2025-08-14 20:08:43,412] WARNING in appium_device_controller: Appium server check failed: HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x1189add30>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-08-14 20:08:43,413] INFO in appium_device_controller: Checking for existing Appium and iproxy processes...
[2025-08-14 20:08:43,462] INFO in appium_device_controller: Attempted to kill Appium processes
[2025-08-14 20:08:43,509] INFO in appium_device_controller: Attempted to kill iproxy processes (default ports only)
[2025-08-14 20:08:45,516] INFO in appium_device_controller: No Appium server detected. Starting a new one...
[2025-08-14 20:08:45,516] INFO in appium_device_controller: Using local Appium installation at: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium
[2025-08-14 20:08:46,550] INFO in appium_device_controller: Installed Appium drivers: 
[2025-08-14 20:08:46,551] INFO in appium_device_controller: Installing XCUITest driver...
[31mError: ✖ A driver named "xcuitest" is already installed. Did you mean to update? Run "appium driver update". See installed drivers with "appium driver list --installed".[39m
[2025-08-14 20:08:47,568] ERROR in appium_device_controller: Error checking/installing drivers: Command '['/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium', 'driver', 'install', 'xcuitest']' returned non-zero exit status 1.
[2025-08-14 20:08:47,569] INFO in appium_device_controller: Enabling inspector plugin if available
[2025-08-14 20:08:47,569] INFO in appium_device_controller: Appium server output will be logged to: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/appium_server.log
[2025-08-14 20:08:47,587] INFO in appium_device_controller: Started Appium server using command: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/node_modules/.bin/appium --base-path /wd/hub --port 4723 --relaxed-security --use-drivers xcuitest,uiautomator2 --use-plugins=inspector --session-override --allow-cors --allow-insecure chromedriver_autodownload
[2025-08-14 20:08:49,592] WARNING in appium_device_controller: Waiting for Appium server to start (attempt 1/15): HTTPConnectionPool(host='127.0.0.1', port=4723): Max retries exceeded with url: /wd/hub/status (Caused by NewConnectionError('<urllib3.connection.HTTPConnection object at 0x1188ac690>: Failed to establish a new connection: [Errno 61] Connection refused'))
[2025-08-14 20:08:51,613] INFO in appium_device_controller: Appium server started successfully
[2025-08-14 20:08:51,613] INFO in appium_device_controller: Appium server status: {'value': {'ready': True, 'message': 'The server is ready to accept new connections', 'build': {'version': '2.17.1', 'git-sha': '09884586a9b2aa7477a372e366971de55fdfb10b', 'built': '2025-08-13 23:51:22 +1000'}}}
[2025-08-14 20:08:51,613] INFO in appium_device_controller: Appium server is ready
Starting Mobile App Automation Tool...
Configuration:
  - Flask server port: 8080
  - Appium server port: 4723
  - WebDriverAgent port: 8200
Open your web browser and navigate to: http://localhost:8080
 * Serving Flask app 'app'
 * Debug mode: on
[2025-08-14 20:08:51,696] INFO in _internal: [31m[1mWARNING: This is a development server. Do not use it in a production deployment. Use a production WSGI server instead.[0m
 * Running on all addresses (0.0.0.0)
 * Running on http://127.0.0.1:8080
 * Running on http://************:8080
[2025-08-14 20:08:51,697] INFO in _internal: [33mPress CTRL+C to quit[0m
