import os
from pathlib import Path
import importlib.util
import sys
import logging

logger = logging.getLogger(__name__)

# Base directory
BASE_DIR = Path(__file__).resolve().parent.parent

# Platform-specific suffixes for isolation
PLATFORM_SUFFIX = "_android"
INSTANCE_SUFFIX = os.environ.get('INSTANCE_DB_SUFFIX', '')
DATABASE_SUFFIX = f"{PLATFORM_SUFFIX}{INSTANCE_SUFFIX}"

# Database-backed directory paths
# Check if the database module is available
try:
    # First, make sure app_android module is in the path
    app_android_dir = BASE_DIR / 'app_android'
    if str(app_android_dir) not in sys.path:
        sys.path.insert(0, str(app_android_dir))

    # Import the directory paths database
    from app_android.utils.directory_paths_db import DirectoryPathsDB
    directory_paths_db = DirectoryPathsDB()
    
    # Define default directories with platform suffix
    DEFAULT_DIRECTORIES = {
        'TEST_CASES': Path('/Users/<USER>/Documents/automation-tool/android_data/test_cases'),
        'REPORTS': BASE_DIR / 'reports_android',
        'SCREENSHOTS': BASE_DIR / 'screenshots_android',
        'REFERENCE_IMAGES': Path('/Users/<USER>/Documents/automation-tool/android_data/reference_images'),
        'TEST_SUITES': Path('/Users/<USER>/Documents/automation-tool/android_data/test_suites'),
        'RESULTS': BASE_DIR / 'reports_android' / 'suites',
        'RECORDINGS': BASE_DIR / 'recordings_android',
        'TEMP_FILES': BASE_DIR / 'temp_android',
    }
    
    # Get paths from database or use defaults
    DIRECTORIES = {}
    for name, default_path in DEFAULT_DIRECTORIES.items():
        # Get path from database or use default
        db_path = directory_paths_db.get_path(name)
        if db_path:
            # Check if it's an absolute or relative path
            path_obj = Path(db_path)
            if path_obj.is_absolute():
                DIRECTORIES[name] = path_obj
            else:
                DIRECTORIES[name] = BASE_DIR / db_path
            logger.info(f"Using database path for {name}: {DIRECTORIES[name]}")
        else:
            DIRECTORIES[name] = default_path
            logger.info(f"Using default path for {name}: {DIRECTORIES[name]}")
            
            # Initialize the database with default value
            if 'directory_paths_db' in locals():
                try:
                    if default_path.is_absolute():
                        directory_paths_db.save_path(name, str(default_path))
                    else:
                        directory_paths_db.save_path(name, str(default_path.relative_to(BASE_DIR)))
                except ValueError:
                    # If it can't be made relative, store absolute path
                    directory_paths_db.save_path(name, str(default_path))
    
    # Files to push directory from database or default
    db_files_to_push = directory_paths_db.get_path('FILES_TO_PUSH')
    if db_files_to_push:
        path_obj = Path(db_files_to_push)
        if path_obj.is_absolute():
            FILES_TO_PUSH_DIR = path_obj
        else:
            FILES_TO_PUSH_DIR = BASE_DIR / db_files_to_push
        logger.info(f"Using database path for FILES_TO_PUSH: {FILES_TO_PUSH_DIR}")
    else:
        FILES_TO_PUSH_DIR = Path('/Users/<USER>/Documents/automation-tool/files_to_push')
        logger.info(f"Using default path for FILES_TO_PUSH: {FILES_TO_PUSH_DIR}")
        
        # Initialize the database with default value
        if 'directory_paths_db' in locals():
            directory_paths_db.save_path('FILES_TO_PUSH', str(FILES_TO_PUSH_DIR))
except Exception as e:
    # Fall back to default directories if database access fails
    logger.warning(f"Error accessing directory paths database, using defaults: {str(e)}")
    
    # Directory configuration with defaults
    DIRECTORIES = {
        'TEST_CASES': Path('/Users/<USER>/Documents/automation-tool/android_data/test_cases'),
        'REPORTS': BASE_DIR / 'reports_android',
        'SCREENSHOTS': BASE_DIR / 'screenshots_android',
        'REFERENCE_IMAGES': Path('/Users/<USER>/Documents/automation-tool/android_data/reference_images'),
        'TEST_SUITES': Path('/Users/<USER>/Documents/automation-tool/android_data/test_suites'),
        'RESULTS': BASE_DIR / 'reports_android' / 'suites',
        'RECORDINGS': BASE_DIR / 'recordings_android',
        'TEMP_FILES': BASE_DIR / 'temp_android',
    }
    
    FILES_TO_PUSH_DIR = Path('/Users/<USER>/Documents/automation-tool/files_to_push')

# Create directories if they don't exist
for dir_path in DIRECTORIES.values():
    dir_path.mkdir(parents=True, exist_ok=True)

# Create files to push directory
FILES_TO_PUSH_DIR.mkdir(parents=True, exist_ok=True)

# Android-specific scale factors (different from iOS)
ANDROID_SCALE_FACTORS = {
    # Android device scale factors
    'default': 0.5,  # Default scale factor for Android
    'Pixel': 0.5,
    'Samsung Galaxy': 0.5,
    'OnePlus': 0.5,
    'Xiaomi': 0.5,
    'Huawei': 0.5,
    'Android Emulator': 0.5,
}

# Android template matching scales
ANDROID_TEMPLATE_SCALES = {
    # Android template matching scales
    'default': 1.0,   # Default: no scaling
    'Pixel': 1.0,
    'Samsung Galaxy': 1.0,
    'OnePlus': 1.0,
    'Xiaomi': 1.0,
    'Huawei': 1.0,
    'Android Emulator': 1.0,
}

# Global values for Android testing
GLOBAL_VALUES = {
    'Auto Rerun Failed': False,
    'Connection Retry Attempts': 3,
    'Connection Retry Delay': 2,
    'Max Step Execution Time': 300,
    'Test Case Delay': 15,
    'default_element_timeout': 60,
}

# Port configuration
FLASK_PORT = int(os.getenv('FLASK_PORT', 8081))  # Default Android port
APPIUM_PORT = int(os.getenv('APPIUM_PORT', 4724))  # Android uses port 4724, iOS uses 4723
# Android doesn't use WDA, but keeping for compatibility
WDA_PORT = int(os.getenv('WDA_PORT', 8300))

# Appium configuration
APPIUM_CONFIG = {
    'HOST': '127.0.0.1',
    'PORT': APPIUM_PORT,
    'BASE_PATH': '/wd/hub'
}

# WDA configuration (not used for Android but kept for compatibility)
WDA_CONFIG = {
    'HOST': '127.0.0.1',
    'PORT': WDA_PORT,
    'BASE_PATH': ''
}

# ADB configuration for Android
ADB_CONFIG = {
    'TIMEOUT': 10,  # seconds
    'MAX_RETRIES': 3
}

# Flask configuration
class FlaskConfig:
    SECRET_KEY = os.getenv('FLASK_SECRET_KEY', 'mobile-automation-android-secret-key')
    TESTING = False
    DEBUG = False
    PORT = FLASK_PORT
    HOST = '0.0.0.0'
    TEST_CASES_DIR = str(DIRECTORIES['TEST_CASES'])
    REFERENCE_IMAGES_DIR = str(DIRECTORIES['REFERENCE_IMAGES'])
    SCREENSHOTS_DIR = str(DIRECTORIES['SCREENSHOTS'])
    REPORTS_DIR = str(DIRECTORIES['REPORTS'])
    TEST_SUITES_DIR = str(DIRECTORIES['TEST_SUITES'])
    RESULTS_DIR = str(DIRECTORIES['RESULTS'])
    RECORDINGS_DIR = str(DIRECTORIES['RECORDINGS'])
    TEMP_FILES_DIR = str(DIRECTORIES['TEMP_FILES'])
    FILES_TO_PUSH_DIR = str(FILES_TO_PUSH_DIR)

# Export directory paths for easy access
TEST_CASES_DIR = DIRECTORIES['TEST_CASES']
REFERENCE_IMAGES_DIR = DIRECTORIES['REFERENCE_IMAGES']
SCREENSHOTS_DIR = DIRECTORIES['SCREENSHOTS']
REPORTS_DIR = DIRECTORIES['REPORTS']
TEST_SUITES_DIR = DIRECTORIES['TEST_SUITES']
RESULTS_DIR = DIRECTORIES['RESULTS']
RECORDINGS_DIR = DIRECTORIES['RECORDINGS']
TEMP_FILES_DIR = DIRECTORIES['TEMP_FILES']