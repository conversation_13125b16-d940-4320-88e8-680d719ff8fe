from .base_action import BaseAction
import time
import os
import logging
import sys
import cv2
from pathlib import Path
# Import parameter_utils utilities with fallback
try:
    from app_android.utils.parameter_utils import substitute_parameters
except ImportError:
    try:
        from ..utils.parameter_utils import substitute_parameters
    except ImportError:
        # Fallback function if import fails
        def substitute_parameters(*args, **kwargs):
            return args[0] if args else None

# Import our enhanced text detection utility
try:
    import sys
    import os
    current_dir = os.path.dirname(os.path.abspath(__file__))
    parent_dir = os.path.dirname(current_dir)
    utils_dir = os.path.join(parent_dir, 'utils')
    if utils_dir not in sys.path:
        sys.path.insert(0, utils_dir)
    from text_detection import detect_text_in_image, scale_coordinates
except ImportError as e:
    import logging
    logging.getLogger(__name__).warning(f"Could not import text_detection: {e}")
    # Create dummy text_detection functions
    def detect_text_in_image(*args, **kwargs):
        return None
    def scale_coordinates(*args, **kwargs):
        return None

class WaitTillAction(BaseAction):
    """Handler for wait till element actions"""

    def execute(self, params):
        """
        Execute wait till element action

        Args:
            params: Dictionary containing:
                - locator_type: Type of locator (id, xpath, etc.)
                - locator_value: Value of the locator
                - timeout: (Optional) Timeout in seconds
                - condition: (Optional) Condition to wait for (exists, visible, etc.)

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        locator_type = params.get('locator_type')
        locator_value = params.get('locator_value')
        # Get timeout from global settings or use provided value
        default_timeout = self.get_global_timeout()
        timeout = params.get('timeout', default_timeout)
        condition = params.get('condition', 'exists')  # Default condition is 'exists'

        # For image locator type, try to get the image from multiple possible parameters
        if locator_type == 'image':
            # Try multiple possible image parameter names
            if not locator_value:
                locator_value = params.get('image_filename', '')

            if not locator_value:
                locator_value = params.get('image', '')

            # Log all parameters for debugging
            self.logger.info(f"Wait Till Image parameters: locator_value={params.get('locator_value')}, " +
                           f"image_filename={params.get('image_filename')}, " +
                           f"image={params.get('image')}")

        if not locator_type:
            return {"status": "error", "message": "Missing locator type parameter"}

        if not locator_value:
            return {"status": "error", "message": f"Missing locator value parameter for locator type: {locator_type}"}

        try:
            if hasattr(self.controller, 'wait_for_element'):
                # Controller has a dedicated method for waiting
                result = self.controller.wait_for_element(locator_type, locator_value, timeout, condition)

                # Handle different return types
                if isinstance(result, dict):
                    return result
                elif isinstance(result, bool):
                    if result:
                        return {
                            "status": "success",
                            "message": f"Element found: {locator_type}='{locator_value}'",
                            "found": True
                        }
                    else:
                        return {
                            "status": "error",
                            "message": f"Timed out waiting for element: {locator_type}='{locator_value}'",
                            "found": False
                        }
                elif result is not None:
                    # Result is likely the element itself
                    return {
                        "status": "success",
                        "message": f"Element found: {locator_type}='{locator_value}'",
                        "found": True
                    }
                else:
                    return {
                        "status": "error",
                        "message": f"Element not found: {locator_type}='{locator_value}'",
                        "found": False
                    }
            else:
                # Implement a basic wait function if the controller doesn't have one
                start_time = time.time()
                found = False
                interval = params.get('interval', 1)  # Get interval from params or use default of 1 second

                while time.time() - start_time < timeout:
                    try:
                        # Handle image locator type differently
                        if locator_type == 'image':
                            # Get threshold from params or use default
                            threshold = params.get('threshold', 0.7)

                            # Check if image_filename is provided (from the frontend)
                            image_filename = params.get('image_filename')
                            if image_filename and not locator_value:
                                locator_value = image_filename
                                self.logger.info(f"Using image_filename '{image_filename}' as locator_value")

                            # Log the image we're looking for
                            self.logger.info(f"Looking for image '{locator_value}' with threshold={threshold}")

                            try:
                                # Try to use Airtest's wait() function directly like the tap image action does
                                from airtest.core.api import wait, Template, TargetNotFoundError

                                # Resolve the image path
                                image_path = locator_value

                                # Make sure image has a file extension
                                if not os.path.splitext(image_path)[1]:
                                    self.logger.warning(f"Image parameter '{image_path}' has no file extension, trying to add .png")
                                    image_path = f"{image_path}.png"

                                # Try multiple paths to find the image
                                potential_paths = [
                                    image_path,  # Direct path as provided
                                    os.path.abspath(image_path),  # Absolute path
                                ]

                                # Check if it's an absolute path
                                if not os.path.isabs(image_path) or not os.path.exists(image_path):
                                    # Try to resolve from reference_images directory
                                    try:
                                        # Try from config
                                        try:
                                            from config_android import DIRECTORIES
                                            reference_dir = DIRECTORIES.get('REFERENCE_IMAGES', '')
                                            if reference_dir:
                                                # Try with the full path
                                                full_path = os.path.join(reference_dir, image_path)
                                                potential_paths.append(full_path)

                                                # Try with just the basename
                                                basename_path = os.path.join(reference_dir, os.path.basename(image_path))
                                                potential_paths.append(basename_path)
                                        except (ImportError, Exception) as e:
                                            self.logger.warning(f"Could not resolve reference image from config: {e}")

                                        # Try from project root
                                        try:
                                            # Get the project root directory (parent of app directory)
                                            project_root = Path(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
                                            reference_dir = project_root / 'reference_images'

                                            # Try with the full path
                                            full_path = os.path.join(reference_dir, image_path)
                                            potential_paths.append(full_path)

                                            # Try with just the basename
                                            basename_path = os.path.join(reference_dir, os.path.basename(image_path))
                                            potential_paths.append(basename_path)

                                            # Try in app/reference_images
                                            app_ref_path = os.path.join(project_root, 'app', 'reference_images', image_path)
                                            potential_paths.append(app_ref_path)

                                            # Try in current directory
                                            current_dir_path = os.path.join('reference_images', image_path)
                                            potential_paths.append(current_dir_path)
                                        except Exception as e:
                                            self.logger.warning(f"Could not resolve reference image from project root: {e}")

                                        # Log all paths being checked
                                        self.logger.info(f"Checking these image paths:")
                                        for idx, path in enumerate(potential_paths):
                                            exists = os.path.exists(path)
                                            status = "EXISTS" if exists else "NOT FOUND"
                                            self.logger.info(f"  [{idx+1}] {path} - {status}")

                                        # Find the first path that exists
                                        for path in potential_paths:
                                            if os.path.exists(path):
                                                image_path = path
                                                self.logger.info(f"Found image at: {image_path}")
                                                break
                                    except Exception as e:
                                        self.logger.warning(f"Error resolving image path: {e}")

                                # Create template with the specified threshold
                                template = Template(image_path, threshold=threshold)
                                self.logger.info(f"Using Airtest wait() with template: {image_path}, threshold={threshold}")

                                # Calculate remaining time for this attempt
                                remaining_time = timeout - (time.time() - start_time)
                                if remaining_time <= 0:
                                    self.logger.warning(f"Timeout reached, breaking wait loop")
                                    break

                                # Use Airtest's wait() function with a short timeout
                                attempt_timeout = min(2.0, remaining_time)
                                self.logger.info(f"Attempting wait with timeout={attempt_timeout}s")

                                try:
                                    # This will raise TargetNotFoundError if image not found within timeout
                                    position = wait(template, timeout=attempt_timeout)

                                    # Validate coordinates to prevent infinity or NaN values
                                    try:
                                        import sys
                                        import os
                                        current_dir = os.path.dirname(os.path.abspath(__file__))
                                        parent_dir = os.path.dirname(current_dir)
                                        utils_dir = os.path.join(parent_dir, 'utils')
                                        if utils_dir not in sys.path:
                                            sys.path.insert(0, utils_dir)
                                        from coordinate_validator import validate_coordinates
                                    except ImportError:
                                        def validate_coordinates(coords):
                                            return coords if coords else None
                                    valid_coords = validate_coordinates(position)

                                    if valid_coords:
                                        self.logger.info(f"Image found at valid position: {valid_coords}")
                                        found = True
                                        break
                                    else:
                                        self.logger.error(f"Image found but coordinates are invalid: {position}")
                                        # Continue the loop to try again if time permits
                                except TargetNotFoundError:
                                    self.logger.info(f"Image not found in this attempt, will retry if time permits")
                                    # Continue the loop to try again if time permits

                            except Exception as e:
                                self.logger.error(f"Error using Airtest wait(): {e}")
                                # Fall back to find_image if Airtest wait() fails
                                if hasattr(self.controller, 'find_image'):
                                    self.logger.info(f"Falling back to controller.find_image()")
                                    position = self.controller.find_image(
                                        locator_value,
                                        threshold=threshold,
                                        timeout=min(2, timeout - (time.time() - start_time))
                                    )
                                    if position:
                                        # Validate coordinates to prevent infinity or NaN values
                                        try:
                                            import sys
                                            import os
                                            current_dir = os.path.dirname(os.path.abspath(__file__))
                                            parent_dir = os.path.dirname(current_dir)
                                            utils_dir = os.path.join(parent_dir, 'utils')
                                            if utils_dir not in sys.path:
                                                sys.path.insert(0, utils_dir)
                                            from coordinate_validator import validate_coordinates
                                        except ImportError:
                                            def validate_coordinates(coords):
                                                return coords if coords else None
                                        valid_coords = validate_coordinates(position)

                                        if valid_coords:
                                            self.logger.info(f"Image found at valid position: {valid_coords}")
                                            found = True
                                            break
                                        else:
                                            self.logger.error(f"Image found but coordinates are invalid: {position}")
                                            # Continue the loop to try again if time permits

                        # Handle text locator type using OCR
                        elif locator_type == 'text':
                            # Apply parameter substitution to the text value
                            original_text_to_find = locator_value
                            text_to_find = substitute_parameters(original_text_to_find)

                            # Log if parameter substitution occurred
                            if text_to_find != original_text_to_find:
                                self.logger.info(f"Parameter substitution applied: '{original_text_to_find}' -> '{text_to_find}'")

                            self.logger.info(f"Looking for text '{text_to_find}' on screen")

                            # Take a screenshot
                            try:
                                screenshot_result = self.controller.take_screenshot()
                                if screenshot_result['status'] != 'success' or not screenshot_result['path'] or not os.path.exists(screenshot_result['path']):
                                    self.logger.warning(f"Failed to take screenshot: {screenshot_result.get('message', 'Unknown error')}")
                                    time.sleep(interval)
                                    continue

                                screenshot_path = screenshot_result['path']
                                self.logger.info(f"Took screenshot: {screenshot_path}")
                            except Exception as e:
                                self.logger.error(f"Error taking screenshot: {e}")
                                time.sleep(interval)
                                continue

                            # Create a directory for debug images
                            debug_dir = os.path.join(os.path.dirname(screenshot_path), 'text_detection')
                            os.makedirs(debug_dir, exist_ok=True)

                            try:
                                # Find the text in the screenshot using our enhanced text detection
                                result = detect_text_in_image(
                                    screenshot_path,
                                    text_to_find,
                                    output_dir=debug_dir
                                )

                                if result:
                                    self.logger.info(f"Found text '{text_to_find}' at coordinates: {result['coordinates']}")
                                    found = True
                                    break
                                else:
                                    self.logger.info(f"Text '{text_to_find}' not found in this attempt, will retry if time permits")
                            except Exception as e:
                                self.logger.error(f"Error finding text: {e}")
                                import traceback
                                self.logger.error(traceback.format_exc())
                        # For other locator types
                        elif hasattr(self.controller, 'find_element'):
                            element = self.controller.find_element(locator_type, locator_value)
                            if element:
                                found = True
                                break
                        elif hasattr(self.controller, 'driver') and self.controller.driver:
                            # Try using Appium driver directly
                            element = self.controller.driver.find_element(locator_type, locator_value)
                            if element:
                                found = True
                                break
                    except Exception as e:
                        # Element not found yet, continue waiting
                        self.logger.debug(f"Element not found yet: {e}")
                        pass

                    # Sleep between attempts using the specified interval
                    time.sleep(interval)

                if found:
                    # Provide more detailed success message based on locator type
                    if locator_type == 'text':
                        # Include both original and substituted text in the message if parameter substitution occurred
                        if 'original_text_to_find' in locals() and text_to_find != original_text_to_find:
                            return {
                                "status": "success",
                                "message": f"Text '{text_to_find}' (substituted from '{original_text_to_find}') found on screen",
                                "found": True
                            }
                        else:
                            return {
                                "status": "success",
                                "message": f"Text '{locator_value}' found on screen",
                                "found": True
                            }
                    elif locator_type == 'image':
                        return {
                            "status": "success",
                            "message": f"Image '{locator_value}' found on screen",
                            "found": True
                        }
                    else:
                        return {
                            "status": "success",
                            "message": f"Element found: {locator_type}='{locator_value}'",
                            "found": True
                        }
                else:
                    # Provide more detailed error message based on locator type
                    if locator_type == 'text':
                        # Include both original and substituted text in the message if parameter substitution occurred
                        if 'original_text_to_find' in locals() and text_to_find != original_text_to_find:
                            return {
                                "status": "error",
                                "message": f"Text '{text_to_find}' (substituted from '{original_text_to_find}') not found within timeout of {timeout}s",
                                "found": False
                            }
                        else:
                            return {
                                "status": "error",
                                "message": f"Text '{locator_value}' not found within timeout of {timeout}s",
                                "found": False
                            }
                    elif locator_type == 'image':
                        return {
                            "status": "error",
                            "message": f"Image '{locator_value}' not found within timeout of {timeout}s",
                            "found": False
                        }
                    else:
                        return {
                            "status": "error",
                            "message": f"Timed out waiting for element: {locator_type}='{locator_value}'",
                            "found": False
                        }

        except Exception as e:
            self.logger.error(f"Error executing wait till action: {e}")
            return {"status": "error", "message": f"Wait till action failed: {str(e)}"}