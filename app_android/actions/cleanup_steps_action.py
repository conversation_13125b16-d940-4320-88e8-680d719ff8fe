from .base_action import BaseAction
import logging
import traceback
import time
import json
import os

class CleanupStepsAction(BaseAction):
    """Handler for executing cleanup steps that always run regardless of test case status"""

    def execute(self, params):
        """
        Execute cleanup steps - these always run regardless of test case status

        Args:
            params: Dictionary containing:
                - test_case_id: ID of the test case to execute as cleanup
                - test_case_steps: Steps of the test case to execute
                - filename: Name of the test case file to load cleanup steps from

        Returns:
            dict: Result with status and message
        """
        if not self.controller:
            return {"status": "error", "message": "No device controller available"}

        # Get the test case ID from params
        test_case_id = params.get('test_case_id')
        test_case_steps = params.get('test_case_steps', [])
        filename = params.get('filename')

        self.logger.info(f"🧹 Starting cleanup steps execution for test case: {test_case_id or filename}")
        print(f"🧹 Starting cleanup steps execution for test case: {test_case_id or filename}")

        # If no test case steps provided, try to load from filename
        if not test_case_steps and filename:
            test_case_steps = self._load_cleanup_steps_from_file(filename)

        if not test_case_steps:
            self.logger.warning("No cleanup steps found to execute")
            print("⚠️ No cleanup steps found to execute")
            return {"status": "success", "message": "No cleanup steps found to execute"}

        # Create a new action factory instance
        try:
            from .action_factory import ActionFactory
        except ImportError:
            try:
                from app_android.actions.action_factory import ActionFactory
            except ImportError:
                import sys
                import os
                current_dir = os.path.dirname(os.path.abspath(__file__))
                parent_dir = os.path.dirname(current_dir)
                if parent_dir not in sys.path:
                    sys.path.insert(0, parent_dir)
                from action_factory import ActionFactory

        action_factory = ActionFactory(self.controller)

        # Execute all cleanup steps
        results = []
        success_count = 0
        step_index = 0
        step_retry_count = {}

        self.logger.info(f"🧹 Executing {len(test_case_steps)} cleanup steps")
        print(f"🧹 Executing {len(test_case_steps)} cleanup steps")

        # Emit event to show multiStep cleanup action as running
        self._emit_multistep_action_status('running', test_case_id, f'Executing {len(test_case_steps)} cleanup steps...')

        for step in test_case_steps:
            try:
                step_type = step.get('type', 'unknown')
                step_id = step.get('action_id', f'cleanup_step_{step_index}')
                
                self.logger.info(f"🧹 Executing cleanup step {step_index + 1}/{len(test_case_steps)}: {step_type} (ID: {step_id})")
                print(f"🧹 Executing cleanup step {step_index + 1}/{len(test_case_steps)}: {step_type} (ID: {step_id})")

                # Emit SocketIO event for cleanup step start
                self._emit_cleanup_step_event('running', step_index, len(test_case_steps), step_type,
                                            f"Executing cleanup step {step_index + 1}/{len(test_case_steps)}: {step_type}",
                                            step_id, test_case_id)

                # Execute the cleanup step
                result = action_factory.execute_action(step_type, step)

                if isinstance(result, dict):
                    success = result.get('status') == 'success'
                    message = result.get('message', 'No message')
                else:
                    success = bool(result)
                    message = str(result)

                if success:
                    self.logger.info(f"✅ Cleanup step {step_index + 1} completed successfully: {message}")
                    print(f"✅ Cleanup step {step_index + 1} completed successfully")
                    success_count += 1

                    # Emit SocketIO event for successful step
                    self._emit_cleanup_step_event('success', step_index, len(test_case_steps), step_type,
                                                f"✅ Cleanup step {step_index + 1} completed successfully",
                                                step_id, test_case_id)

                    results.append({
                        "status": "success",
                        "message": message,
                        "step_index": step_index,
                        "step_type": step_type,
                        "step_id": step_id
                    })
                else:
                    self.logger.warning(f"⚠️ Cleanup step {step_index + 1} failed: {message}")
                    print(f"⚠️ Cleanup step {step_index + 1} failed: {message}")

                    # Emit SocketIO event for failed step
                    self._emit_cleanup_step_event('error', step_index, len(test_case_steps), step_type,
                                                f"⚠️ Cleanup step {step_index + 1} failed: {message}",
                                                step_id, test_case_id)

                    results.append({
                        "status": "failed",
                        "message": message,
                        "step_index": step_index,
                        "step_type": step_type,
                        "step_id": step_id
                    })

                step_index += 1

                # Add a small delay between cleanup steps for stability
                time.sleep(0.5)

            except Exception as e:
                self.logger.error(f"❌ Error executing cleanup step {step_index + 1}: {e}")
                self.logger.error(traceback.format_exc())
                print(f"❌ Error executing cleanup step {step_index + 1}: {e}")
                results.append({
                    "status": "error",
                    "message": f"Error executing cleanup step {step_index + 1}: {str(e)}",
                    "step_index": step_index
                })
                # For cleanup steps, continue even if there's an error
                step_index += 1

        # Calculate final status
        total_steps = len(test_case_steps)
        failed_steps = total_steps - success_count

        if failed_steps == 0:
            final_status = "success"
            final_message = f"All {total_steps} cleanup steps completed successfully"
            self.logger.info(f"✅ {final_message}")
            print(f"✅ {final_message}")
        else:
            final_status = "warning"  # Use warning instead of error for cleanup
            final_message = f"Cleanup completed with {failed_steps} failed steps out of {total_steps}"
            self.logger.warning(f"⚠️ {final_message}")
            print(f"⚠️ {final_message}")

        # Emit final completion event
        self._emit_cleanup_step_event('complete', total_steps, total_steps, 'cleanup',
                                    final_message, 'cleanup_complete', test_case_id)

        # Emit event to show multiStep cleanup action as completed
        multistep_status = 'success' if final_status == 'success' else 'warning'
        self._emit_multistep_action_status(multistep_status, test_case_id, final_message)

        return {
            "status": final_status,
            "message": final_message,
            "total_steps": total_steps,
            "successful_steps": success_count,
            "failed_steps": failed_steps,
            "results": results
        }

    def _load_cleanup_steps_from_file(self, filename):
        """
        Load cleanup steps from a test case file by detecting multiStep actions with cleanup=true
        and individual actions with is_cleanup_step=true

        Args:
            filename: Name of the test case file

        Returns:
            list: List of cleanup steps to execute
        """
        cleanup_steps = []
        
        try:
            # Get the test cases directory from config
            try:
                from app_android.config_android import TEST_CASES_DIR
            except ImportError:
                # Fallback to default path
                TEST_CASES_DIR = "/Users/<USER>/Documents/automation-tool/android_data/test_cases"

            # Ensure filename has .json extension
            if not filename.endswith('.json'):
                filename = f"{filename}.json"

            test_case_path = os.path.join(TEST_CASES_DIR, filename)
            
            self.logger.info(f"🔍 Loading cleanup steps from: {test_case_path}")
            print(f"🔍 Loading cleanup steps from: {test_case_path}")

            if not os.path.exists(test_case_path):
                self.logger.warning(f"Test case file not found: {test_case_path}")
                print(f"⚠️ Test case file not found: {test_case_path}")
                return cleanup_steps

            with open(test_case_path, 'r') as f:
                test_case_data = json.load(f)

            actions = test_case_data.get('actions', [])
            self.logger.info(f"📊 Analyzing {len(actions)} actions for cleanup detection")
            print(f"📊 Analyzing {len(actions)} actions for cleanup detection")

            for action in actions:
                # Check for multiStep actions with cleanup=true
                if action.get('type') == 'multiStep' and action.get('cleanup', False):
                    self.logger.info(f"🔍 Found multiStep cleanup action: {action.get('action_id', 'unknown')}")
                    print(f"🔍 Found multiStep cleanup action: {action.get('action_id', 'unknown')}")
                    
                    # multiStep actions can have either 'steps' or 'test_case_steps'
                    steps = action.get('steps', []) or action.get('test_case_steps', [])
                    
                    # Add all steps from the multiStep cleanup action
                    for step in steps:
                        if step.get('is_cleanup_step', False):
                            cleanup_steps.append(step)
                            self.logger.info(f"  ➕ Added cleanup step: {step.get('type', 'unknown')}")
                            print(f"  ➕ Added cleanup step: {step.get('type', 'unknown')}")

                # Check for individual actions with is_cleanup_step=true
                elif action.get('is_cleanup_step', False):
                    cleanup_steps.append(action)
                    self.logger.info(f"🔍 Found individual cleanup step: {action.get('action_id', 'unknown')} ({action.get('type', 'unknown')})")
                    print(f"🔍 Found individual cleanup step: {action.get('action_id', 'unknown')} ({action.get('type', 'unknown')})")

            self.logger.info(f"📊 Total cleanup steps detected: {len(cleanup_steps)}")
            print(f"📊 Total cleanup steps detected: {len(cleanup_steps)}")

        except Exception as e:
            self.logger.error(f"❌ Error loading cleanup steps from file {filename}: {e}")
            self.logger.error(traceback.format_exc())
            print(f"❌ Error loading cleanup steps from file {filename}: {e}")

        return cleanup_steps

    def set_controller(self, controller):
        """Set the device controller for this action"""
        self.controller = controller
        if hasattr(self, 'enhanced_finder') and self.enhanced_finder:
            self.enhanced_finder.controller = controller

    def _emit_cleanup_step_event(self, status, step_index, total_steps, action_type, message, action_id, test_case_id):
        """Emit SocketIO event for cleanup step progress"""
        try:
            # Try to get socketio from the global app context
            from app_android.app import socketio
            if socketio and hasattr(socketio, 'emit'):
                event_data = {
                    'status': status,
                    'step_index': step_index,
                    'total_steps': total_steps,
                    'action_type': action_type,
                    'message': message,
                    'action_id': action_id,
                    'test_case_id': test_case_id
                }
                socketio.emit('cleanup_step_result', event_data)
                self.logger.debug(f"Emitted cleanup_step_result event: {event_data}")
            else:
                self.logger.debug("SocketIO not available for cleanup step events")
        except Exception as e:
            self.logger.warning(f"Failed to emit cleanup step event: {e}")

    def _emit_multistep_action_status(self, status, test_case_id, message):
        """Emit SocketIO event to update multiStep cleanup action status in UI"""
        try:
            # Try to get socketio from the global app context
            from app_android.app import socketio
            if socketio and hasattr(socketio, 'emit'):
                event_data = {
                    'status': status,
                    'test_case_id': test_case_id,
                    'message': message,
                    'action_type': 'multiStep',
                    'cleanup': True,
                    'timestamp': time.time()
                }
                socketio.emit('multistep_cleanup_status', event_data)
                self.logger.debug(f"Emitted multistep_cleanup_status event: {event_data}")
            else:
                self.logger.debug("SocketIO not available for multiStep status events")
        except Exception as e:
            self.logger.warning(f"Failed to emit multiStep status event: {e}")
