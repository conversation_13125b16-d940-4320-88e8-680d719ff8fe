"""
Simple Action Executor for Retry Functionality
"""
import logging
import time
import json
from datetime import datetime

logger = logging.getLogger(__name__)

class ActionExecutor:
    """Simple action executor for retry functionality"""
    
    def __init__(self):
        self.device_controller = None
        self._initialize_device_controller()
    
    def _initialize_device_controller(self):
        """Initialize the device controller"""
        try:
            from app_android.utils.appium_device_controller import AppiumDeviceController
            from app_android import config_android
            appium_port = getattr(config_android, 'APPIUM_PORT', 4724)
            self.device_controller = AppiumDeviceController(appium_port=appium_port)
            logger.info("Device controller initialized for action executor")
        except Exception as e:
            logger.error(f"Failed to initialize device controller: {str(e)}")
            self.device_controller = None
    
    def execute_action(self, action, step_index=0):
        """
        Execute a single action
        
        Args:
            action (dict): Action definition
            step_index (int): Step index for tracking
            
        Returns:
            dict: Execution result
        """
        start_time = time.time()
        
        try:
            if not self.device_controller:
                return {
                    'success': False,
                    'error': 'Device controller not available',
                    'duration': 0
                }
            
            action_type = action.get('type', '')
            logger.info(f"Executing action: {action_type}")
            
            # Execute based on action type
            if action_type == 'launch_app':
                result = self._execute_launch_app(action)
            elif action_type == 'terminate_app':
                result = self._execute_terminate_app(action)
            elif action_type == 'click':
                result = self._execute_click(action)
            elif action_type == 'wait':
                result = self._execute_wait(action)
            elif action_type == 'addLog':
                result = self._execute_add_log(action)
            elif action_type == 'takeScreenshot':
                result = self._execute_take_screenshot(action)
            else:
                # For unknown action types, try to execute using the device controller
                result = self._execute_generic_action(action)
            
            duration = int((time.time() - start_time) * 1000)  # Convert to milliseconds
            result['duration'] = duration
            
            logger.info(f"Action {action_type} completed in {duration}ms with result: {result.get('success')}")
            return result
            
        except Exception as e:
            duration = int((time.time() - start_time) * 1000)
            logger.error(f"Error executing action {action.get('type')}: {str(e)}")
            return {
                'success': False,
                'error': str(e),
                'duration': duration
            }
    
    def _execute_launch_app(self, action):
        """Execute launch app action"""
        try:
            bundle_id = action.get('bundle_id') or action.get('app_id')
            if not bundle_id:
                return {'success': False, 'error': 'No bundle ID provided'}
            
            # Use device controller to launch app
            result = self.device_controller.launch_app(bundle_id)
            return {'success': True, 'message': f'Launched app: {bundle_id}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_terminate_app(self, action):
        """Execute terminate app action"""
        try:
            bundle_id = action.get('bundle_id') or action.get('app_id')
            if not bundle_id:
                return {'success': False, 'error': 'No bundle ID provided'}
            
            # Use device controller to terminate app
            result = self.device_controller.terminate_app(bundle_id)
            return {'success': True, 'message': f'Terminated app: {bundle_id}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_click(self, action):
        """Execute click action"""
        try:
            # Get locator information
            locator_type = action.get('locator_type', 'xpath')
            locator_value = action.get('locator_value', '')
            
            if not locator_value:
                return {'success': False, 'error': 'No locator value provided'}
            
            # Use device controller to click element
            result = self.device_controller.click_element(locator_type, locator_value)
            return {'success': True, 'message': f'Clicked element: {locator_value}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_wait(self, action):
        """Execute wait action"""
        try:
            wait_time = action.get('wait_time', 1)
            if isinstance(wait_time, str):
                wait_time = float(wait_time)
            
            time.sleep(wait_time / 1000.0)  # Convert ms to seconds
            return {'success': True, 'message': f'Waited for {wait_time}ms'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_add_log(self, action):
        """Execute add log action"""
        try:
            log_message = action.get('log_message', 'Log entry')
            logger.info(f"Add Log: {log_message}")
            return {'success': True, 'message': f'Added log: {log_message}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_take_screenshot(self, action):
        """Execute take screenshot action"""
        try:
            screenshot_name = action.get('screenshot_name', 'screenshot')
            # Use device controller to take screenshot
            result = self.device_controller.take_screenshot(screenshot_name)
            return {'success': True, 'message': f'Screenshot taken: {screenshot_name}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}
    
    def _execute_generic_action(self, action):
        """Execute generic action using device controller"""
        try:
            # For now, just return success for unknown actions
            # In a real implementation, you would map this to the appropriate device controller method
            action_type = action.get('type', 'unknown')
            logger.info(f"Executing generic action: {action_type}")
            return {'success': True, 'message': f'Executed action: {action_type}'}
        except Exception as e:
            return {'success': False, 'error': str(e)}

    def cleanup_after_execution(self):
        """
        Clean up temporary files and debug images after test execution
        This should be called at the end of test runs
        """
        try:
            from .utils.file_utils import auto_cleanup_after_test_execution
            cleaned_count = auto_cleanup_after_test_execution()
            logger.info(f"Post-execution cleanup completed: {cleaned_count} files cleaned")
            return cleaned_count
        except Exception as e:
            logger.error(f"Error during post-execution cleanup: {e}")
            return 0
