"""
Healenium Configuration Module

This module provides configuration and management for Healenium self-healing capabilities
integrated with the Mobile App Automation Tool.
"""

import os
import json
import logging
import subprocess
import time
import requests
from typing import Dict, Any, Optional
from .global_values_db import global_values_db

logger = logging.getLogger(__name__)

class HealeniumConfig:
    """Configuration manager for Healenium integration"""
    
    def __init__(self):
        self.healenium_enabled = True  # Enable by default
        self.proxy_url = "http://localhost:8085"
        self.backend_url = "http://localhost:7878"
        self.appium_server_url = "http://host.docker.internal:4724/wd/hub"
        self.recovery_tries = 1  # Keep at 1 to prevent excessive retries
        self.score_cap = 0.6
        self.heal_enabled = True
        self.docker_compose_file = None
        self.container_status = {}
        self.health_check_timeout = 5  # Timeout for health checks
        self.last_health_check = 0
        self.health_check_interval = 30  # Check health every 30 seconds

        # Load configuration from database
        self.load_config()
    
    def load_config(self):
        """Load Healenium configuration from database"""
        try:
            # Get global values from database
            global_values = global_values_db.get_all_values()
            
            # Load Healenium settings with defaults enabled
            self.healenium_enabled = global_values.get('Healenium Enabled', True)  # Default to enabled
            self.proxy_url = global_values.get('Healenium Proxy URL', "http://localhost:8085")
            self.backend_url = global_values.get('Healenium Backend URL', "http://localhost:7878")
            self.appium_server_url = global_values.get('Healenium Appium Server URL', "http://host.docker.internal:4724/wd/hub")
            self.recovery_tries = int(global_values.get('Healenium Recovery Tries', 1))
            self.score_cap = float(global_values.get('Healenium Score Cap', 0.6))
            self.heal_enabled = global_values.get('Healenium Heal Enabled', True)
            
            logger.info(f"Loaded Healenium configuration: enabled={self.healenium_enabled}")
            
        except Exception as e:
            logger.error(f"Error loading Healenium configuration: {e}")
            # Use default values if loading fails
    
    def save_config(self, config_data: Dict[str, Any]):
        """Save Healenium configuration to database"""
        try:
            # Update instance variables
            self.healenium_enabled = config_data.get('healenium_enabled', False)
            self.proxy_url = config_data.get('proxy_url', "http://localhost:8085")
            self.backend_url = config_data.get('backend_url', "http://localhost:7878")
            self.appium_server_url = config_data.get('appium_server_url', "http://host.docker.internal:4724/wd/hub")
            self.recovery_tries = int(config_data.get('recovery_tries', 1))
            self.score_cap = float(config_data.get('score_cap', 0.6))
            self.heal_enabled = config_data.get('heal_enabled', True)
            
            # Save to database
            global_values_db.set_value('Healenium Enabled', self.healenium_enabled)
            global_values_db.set_value('Healenium Proxy URL', self.proxy_url)
            global_values_db.set_value('Healenium Backend URL', self.backend_url)
            global_values_db.set_value('Healenium Appium Server URL', self.appium_server_url)
            global_values_db.set_value('Healenium Recovery Tries', self.recovery_tries)
            global_values_db.set_value('Healenium Score Cap', self.score_cap)
            global_values_db.set_value('Healenium Heal Enabled', self.heal_enabled)
            
            logger.info("Healenium configuration saved successfully")
            return True
            
        except Exception as e:
            logger.error(f"Error saving Healenium configuration: {e}")
            return False
    
    def get_config_dict(self) -> Dict[str, Any]:
        """Get current configuration as dictionary"""
        return {
            'healenium_enabled': self.healenium_enabled,
            'proxy_url': self.proxy_url,
            'backend_url': self.backend_url,
            'appium_server_url': self.appium_server_url,
            'recovery_tries': self.recovery_tries,
            'score_cap': self.score_cap,
            'heal_enabled': self.heal_enabled,
            'container_status': self.container_status
        }
    
    def check_docker_availability(self) -> bool:
        """Check if Docker is available and running"""
        try:
            result = subprocess.run(['docker', '--version'], 
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                logger.info("Docker is available")
                return True
            else:
                logger.warning("Docker command failed")
                return False
        except (subprocess.TimeoutExpired, FileNotFoundError) as e:
            logger.warning(f"Docker not available: {e}")
            return False
    
    def check_healenium_containers(self) -> Dict[str, str]:
        """Check status of Healenium Docker containers"""
        containers = {
            'postgres-db': 'stopped',
            'hlm-proxy': 'stopped', 
            'hlm-backend': 'stopped',
            'selector-imitator': 'stopped'
        }
        
        if not self.check_docker_availability():
            return containers
        
        try:
            # Check container status
            result = subprocess.run(['docker', 'ps', '--format', 'table {{.Names}}\t{{.Status}}'], 
                                  capture_output=True, text=True, timeout=10)
            
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                for line in lines:
                    if line.strip():
                        parts = line.split('\t')
                        if len(parts) >= 2:
                            name = parts[0].strip()
                            status = parts[1].strip()
                            
                            # Check if this is a Healenium container
                            for container_name in containers.keys():
                                if container_name in name:
                                    containers[container_name] = 'running' if 'Up' in status else 'stopped'
                                    break
            
            self.container_status = containers
            return containers
            
        except Exception as e:
            logger.error(f"Error checking container status: {e}")
            return containers
    
    def start_healenium_containers(self) -> bool:
        """Start Healenium Docker containers"""
        if not self.check_docker_availability():
            logger.error("Docker is not available")
            return False
        
        try:
            # Create docker-compose file for Healenium
            compose_content = self._generate_docker_compose()
            compose_file = os.path.join(os.path.dirname(__file__), '..', '..', 'docker-compose-healenium.yaml')
            
            with open(compose_file, 'w') as f:
                f.write(compose_content)
            
            self.docker_compose_file = compose_file
            
            # Start containers
            result = subprocess.run(['docker-compose', '-f', compose_file, 'up', '-d'], 
                                  capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                logger.info("Healenium containers started successfully")
                # Wait a moment for containers to initialize
                time.sleep(5)
                return True
            else:
                logger.error(f"Failed to start Healenium containers: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error starting Healenium containers: {e}")
            return False
    
    def stop_healenium_containers(self) -> bool:
        """Stop Healenium Docker containers"""
        if not self.docker_compose_file or not os.path.exists(self.docker_compose_file):
            logger.warning("Docker compose file not found")
            return False
        
        try:
            result = subprocess.run(['docker-compose', '-f', self.docker_compose_file, 'down'], 
                                  capture_output=True, text=True, timeout=60)
            
            if result.returncode == 0:
                logger.info("Healenium containers stopped successfully")
                return True
            else:
                logger.error(f"Failed to stop Healenium containers: {result.stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Error stopping Healenium containers: {e}")
            return False
    
    def _generate_docker_compose(self) -> str:
        """Generate docker-compose.yaml content for Healenium"""
        return f"""version: '3.8'

services:
  postgres-db:
    image: postgres:13
    container_name: healenium-postgres
    environment:
      - POSTGRES_DB=healenium
      - POSTGRES_USER=healenium_user
      - POSTGRES_PASSWORD=YDk2nmNs4s9aCP6K
      - POSTGRES_SCHEMA=healenium
    volumes:
      - ./db/init:/docker-entrypoint-initdb.d
    ports:
      - "5432:5432"
    networks:
      - healenium

  hlm-backend:
    image: healenium/hlm-backend:3.4.3
    container_name: healenium-backend
    environment:
      - SPRING_POSTGRES_DB=healenium
      - SPRING_POSTGRES_SCHEMA=healenium
      - SPRING_POSTGRES_USER=healenium_user
      - SPRING_POSTGRES_PASSWORD=YDk2nmNs4s9aCP6K
      - SPRING_POSTGRES_DB_HOST=postgres-db
      - KEY_SELECTOR_URL=false
      - COLLECT_METRICS=true
      - HLM_LOG_LEVEL=info
    ports:
      - "7878:7878"
    depends_on:
      - postgres-db
    networks:
      - healenium

  selector-imitator:
    image: healenium/hlm-selector-imitator:1.4
    container_name: healenium-selector-imitator
    environment:
      - BACKEND_URL=http://hlm-backend:7878
      - APPIUM_URL={self.appium_server_url}
    ports:
      - "8000:8000"
    depends_on:
      - hlm-backend
    networks:
      - healenium

  hlm-proxy:
    image: healenium/hlm-proxy:1.4.3
    container_name: healenium-proxy
    environment:
      - RECOVERY_TRIES={self.recovery_tries}
      - SCORE_CAP={self.score_cap}
      - HEAL_ENABLED={str(self.heal_enabled).lower()}
      - SELENIUM_SERVER_URL={self.appium_server_url}
      - HEALENIUM_SERVER_URL={self.backend_url}
      - HEALENIUM_SERVICE=http://hlm-backend:7878
      - IMITATE_SERVICE=http://selector-imitator:8000
      - HLM_LOG_LEVEL=info
    ports:
      - "8085:8085"
    depends_on:
      - hlm-backend
      - selector-imitator
    networks:
      - healenium

networks:
  healenium:
    driver: bridge
"""

    def check_healenium_health(self) -> Dict[str, bool]:
        """Check health of Healenium services with caching to prevent excessive calls"""
        import time
        
        # Use cached result if recent check was performed
        current_time = time.time()
        if (current_time - self.last_health_check) < self.health_check_interval:
            return getattr(self, '_cached_health_status', {
                'proxy': False, 'backend': False, 'database': False
            })
        
        health_status = {
            'proxy': False,
            'backend': False,
            'database': False
        }
        
        # Check proxy health with reduced timeout
        try:
            response = requests.get(f"{self.proxy_url}/wd/hub/status", timeout=self.health_check_timeout)
            health_status['proxy'] = response.status_code == 200
            logger.debug(f"Healenium proxy health check: {health_status['proxy']}")
        except Exception as e:
            logger.debug(f"Healenium proxy health check failed: {e}")
        
        # Check backend health with reduced timeout
        try:
            response = requests.get(f"{self.backend_url}/healenium/health", timeout=self.health_check_timeout)
            health_status['backend'] = response.status_code == 200
            logger.debug(f"Healenium backend health check: {health_status['backend']}")
        except Exception as e:
            logger.debug(f"Healenium backend health check failed: {e}")
        
        # Database health is checked indirectly through backend
        health_status['database'] = health_status['backend']
        
        # Cache the result
        self._cached_health_status = health_status
        self.last_health_check = current_time
        
        return health_status

# Global instance
healenium_config = HealeniumConfig()
