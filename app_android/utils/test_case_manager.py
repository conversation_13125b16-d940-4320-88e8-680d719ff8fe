"""
Test Case Manager for Android Mobile Automation
Handles loading, saving, and managing test case files
"""

import os
import json
import logging
import uuid
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Any, Optional

logger = logging.getLogger(__name__)

class TestCaseManager:
    """Manages test case files and operations"""
    
    def __init__(self, test_cases_dir: str):
        """
        Initialize TestCaseManager
        
        Args:
            test_cases_dir: Directory path where test case files are stored
        """
        self.test_cases_dir = Path(test_cases_dir)
        self.logger = logging.getLogger(__name__)
        
        # Ensure test cases directory exists
        self.test_cases_dir.mkdir(parents=True, exist_ok=True)
        self.logger.info(f"TestCaseManager initialized with directory: {self.test_cases_dir}")
    
    def get_test_cases(self) -> List[Dict[str, Any]]:
        """
        Get list of all test cases with metadata
        
        Returns:
            List of test case metadata dictionaries
        """
        test_cases = []
        
        try:
            # Get all JSON files in the test cases directory
            json_files = list(self.test_cases_dir.glob("*.json"))
            
            for json_file in json_files:
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        test_case_data = json.load(f)
                    
                    # Extract metadata
                    metadata = {
                        'filename': json_file.name,
                        'name': test_case_data.get('name', json_file.stem),
                        'description': test_case_data.get('description', ''),
                        'created_at': test_case_data.get('created_at', ''),
                        'modified_at': test_case_data.get('modified_at', ''),
                        'actions_count': len(test_case_data.get('actions', [])),
                        'device_id': test_case_data.get('device_id', ''),
                        'tags': test_case_data.get('tags', [])
                    }
                    
                    test_cases.append(metadata)
                    
                except Exception as e:
                    self.logger.warning(f"Error reading test case file {json_file}: {e}")
                    continue
            
            # Sort by name
            test_cases.sort(key=lambda x: x['name'].lower())
            
            self.logger.info(f"Found {len(test_cases)} test cases")
            return test_cases
            
        except Exception as e:
            self.logger.error(f"Error getting test cases: {e}")
            return []
    
    def load_test_case(self, filename: str) -> Optional[Dict[str, Any]]:
        """
        Load a specific test case by filename
        
        Args:
            filename: Name of the test case file (with or without .json extension)
            
        Returns:
            Test case data dictionary or None if not found
        """
        try:
            # Ensure filename has .json extension
            if not filename.endswith('.json'):
                filename += '.json'
            
            file_path = self.test_cases_dir / filename
            
            if not file_path.exists():
                self.logger.warning(f"Test case file not found: {file_path}")
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                test_case_data = json.load(f)
            
            self.logger.info(f"Loaded test case: {filename}")
            return test_case_data
            
        except Exception as e:
            self.logger.error(f"Error loading test case {filename}: {e}")
            return None
    
    def save_test_case(self, test_case_data: Dict[str, Any], filename: str = None, is_save_as: bool = False) -> Optional[str]:
        """
        Save test case data to file
        
        Args:
            test_case_data: Test case data dictionary
            filename: Optional filename (if None, will generate from test case name)
            is_save_as: Whether this is a "Save As" operation
            
        Returns:
            Saved filename or None if failed
        """
        try:
            # Generate filename if not provided
            if not filename:
                test_case_name = test_case_data.get('name', f'test_case_{uuid.uuid4().hex[:8]}')
                # Sanitize filename
                safe_name = "".join(c for c in test_case_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
                safe_name = safe_name.replace(' ', '_')
                filename = f"{safe_name}.json"
            
            # Ensure filename has .json extension
            if not filename.endswith('.json'):
                filename += '.json'
            
            # Update timestamps
            current_time = datetime.now().isoformat()
            if 'created_at' not in test_case_data:
                test_case_data['created_at'] = current_time
            test_case_data['modified_at'] = current_time
            
            # Save to file
            file_path = self.test_cases_dir / filename
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(test_case_data, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"Saved test case: {filename}")
            return filename
            
        except Exception as e:
            self.logger.error(f"Error saving test case: {e}")
            return None
    
    def delete_test_case(self, filename: str) -> bool:
        """
        Delete a test case file
        
        Args:
            filename: Name of the test case file to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Ensure filename has .json extension
            if not filename.endswith('.json'):
                filename += '.json'
            
            file_path = self.test_cases_dir / filename
            
            if not file_path.exists():
                self.logger.warning(f"Test case file not found for deletion: {file_path}")
                return False
            
            file_path.unlink()
            self.logger.info(f"Deleted test case: {filename}")
            return True
            
        except Exception as e:
            self.logger.error(f"Error deleting test case {filename}: {e}")
            return False
    
    def rename_test_case(self, filename: str, new_name: str) -> Optional[str]:
        """
        Rename a test case file
        
        Args:
            filename: Current filename
            new_name: New name for the test case
            
        Returns:
            New filename or None if failed
        """
        try:
            # Load existing test case
            test_case_data = self.load_test_case(filename)
            if not test_case_data:
                return None
            
            # Update name in data
            test_case_data['name'] = new_name
            
            # Generate new filename
            safe_name = "".join(c for c in new_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            safe_name = safe_name.replace(' ', '_')
            new_filename = f"{safe_name}.json"
            
            # Save with new filename
            saved_filename = self.save_test_case(test_case_data, new_filename)
            
            if saved_filename:
                # Delete old file
                self.delete_test_case(filename)
                self.logger.info(f"Renamed test case from {filename} to {saved_filename}")
                return saved_filename
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error renaming test case {filename}: {e}")
            return None
    
    def duplicate_test_case(self, filename: str) -> Optional[str]:
        """
        Duplicate a test case file
        
        Args:
            filename: Name of the test case file to duplicate
            
        Returns:
            New filename or None if failed
        """
        try:
            # Load existing test case
            test_case_data = self.load_test_case(filename)
            if not test_case_data:
                return None
            
            # Update name and remove timestamps
            original_name = test_case_data.get('name', filename.replace('.json', ''))
            test_case_data['name'] = f"{original_name}_copy"
            test_case_data.pop('created_at', None)
            test_case_data.pop('modified_at', None)
            
            # Save as new file
            saved_filename = self.save_test_case(test_case_data, is_save_as=True)
            
            if saved_filename:
                self.logger.info(f"Duplicated test case {filename} as {saved_filename}")
                return saved_filename
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error duplicating test case {filename}: {e}")
            return None
    
    def clean_duplicates(self) -> int:
        """
        Clean up duplicate test case files
        
        Returns:
            Number of duplicates removed
        """
        try:
            test_cases = self.get_test_cases()
            names_seen = set()
            duplicates_removed = 0
            
            for test_case in test_cases:
                name = test_case['name']
                if name in names_seen:
                    # This is a duplicate
                    if self.delete_test_case(test_case['filename']):
                        duplicates_removed += 1
                        self.logger.info(f"Removed duplicate test case: {test_case['filename']}")
                else:
                    names_seen.add(name)
            
            self.logger.info(f"Cleaned up {duplicates_removed} duplicate test cases")
            return duplicates_removed
            
        except Exception as e:
            self.logger.error(f"Error cleaning duplicates: {e}")
            return 0

    def get_all_action_types(self) -> List[str]:
        """
        Get all unique action types from existing test cases

        Returns:
            List of unique action types
        """
        action_types = set()

        try:
            # Get all JSON files in the test cases directory
            json_files = list(self.test_cases_dir.glob("*.json"))

            for json_file in json_files:
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        test_case_data = json.load(f)

                    # Extract action types from actions
                    actions = test_case_data.get('actions', [])
                    for action in actions:
                        action_type = action.get('type', action.get('action_type'))
                        if action_type:
                            action_types.add(action_type)

                except Exception as e:
                    self.logger.warning(f"Error reading test case file {json_file}: {e}")
                    continue

            # Convert to sorted list
            result = sorted(list(action_types))
            self.logger.info(f"Found {len(result)} unique action types")
            return result

        except Exception as e:
            self.logger.error(f"Error getting action types: {e}")
            return []

    def get_all_locator_types(self) -> List[str]:
        """
        Get all unique locator types from existing test cases

        Returns:
            List of unique locator types
        """
        locator_types = set()

        try:
            # Get all JSON files in the test cases directory
            json_files = list(self.test_cases_dir.glob("*.json"))

            for json_file in json_files:
                try:
                    with open(json_file, 'r', encoding='utf-8') as f:
                        test_case_data = json.load(f)

                    # Extract locator types from actions
                    actions = test_case_data.get('actions', [])
                    for action in actions:
                        locator_type = action.get('locator_type')
                        if locator_type:
                            locator_types.add(locator_type)

                except Exception as e:
                    self.logger.warning(f"Error reading test case file {json_file}: {e}")
                    continue

            # Convert to sorted list
            result = sorted(list(locator_types))
            self.logger.info(f"Found {len(result)} unique locator types")
            return result

        except Exception as e:
            self.logger.error(f"Error getting locator types: {e}")
            return []
