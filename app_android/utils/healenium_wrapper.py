"""
Healenium Wrapper Module

This module provides a wrapper around the Appium WebDriver to integrate
Healenium self-healing capabilities seamlessly with existing automation.
"""

import logging
from typing import Optional, Any, Dict
from appium import webdriver
from appium.webdriver.common.appiumby import AppiumBy
from selenium.webdriver.remote.webelement import WebElement
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException
from .healenium_config import healenium_config

logger = logging.getLogger(__name__)

class HealeniumWebDriver:
    """
    Wrapper class that provides Healenium self-healing capabilities
    while maintaining compatibility with existing Appium WebDriver usage.
    """
    
    def __init__(self, original_driver: webdriver.Remote):
        """
        Initialize the Healenium wrapper
        
        Args:
            original_driver: The original Appium WebDriver instance
        """
        self.original_driver = original_driver
        self.healenium_driver = None
        self.healenium_enabled = healenium_config.healenium_enabled
        self.fallback_enabled = True
        
        # Initialize Healenium driver if enabled
        if self.healenium_enabled:
            self._init_healenium_driver()
    
    def _init_healenium_driver(self):
        """Initialize Healenium driver if enabled and healthy"""
        try:
            if not healenium_config.healenium_enabled:
                logger.info("Healenium is disabled, using original driver")
                return
            
            # Check if Healenium services are healthy
            health_status = healenium_config.check_healenium_health()
            if not health_status.get('proxy', False):
                logger.warning("Healenium proxy is not healthy, disabling Healenium for this session")
                self.healenium_enabled = False
                return
            
            if not health_status.get('backend', False):
                logger.warning("Healenium backend is not healthy, disabling Healenium for this session")
                self.healenium_enabled = False
                return
            
            # Get current capabilities from original driver
            capabilities = self.original_driver.capabilities.copy()
            
            # Create new driver instance pointing to Healenium proxy
            logger.info(f"Creating Healenium driver with proxy URL: {healenium_config.proxy_url}")
            
            self.healenium_driver = webdriver.Remote(
                command_executor=f"{healenium_config.proxy_url}/wd/hub",
                desired_capabilities=capabilities
            )
            
            logger.info("Healenium driver initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Healenium driver: {e}")
            self.healenium_driver = None
            self.healenium_enabled = False  # Disable for this session to prevent repeated failures
    
    def _get_active_driver(self) -> webdriver.Remote:
        """Get the active driver (Healenium if available, otherwise original)"""
        if self.healenium_enabled and self.healenium_driver:
            return self.healenium_driver
        return self.original_driver
    
    def _execute_with_fallback(self, method_name: str, *args, **kwargs):
        """
        Execute a method with fallback to original driver if Healenium fails
        Implements circuit breaker pattern to prevent excessive Healenium retries
        
        Args:
            method_name: Name of the method to execute
            *args: Positional arguments for the method
            **kwargs: Keyword arguments for the method
        """
        active_driver = self._get_active_driver()
        
        # Implement timeout for Healenium operations to prevent hangs
        import time
        start_time = time.time()
        healenium_timeout = 30  # 30 second timeout for Healenium operations
        
        try:
            # Try with active driver (Healenium or original)
            method = getattr(active_driver, method_name)
            result = method(*args, **kwargs)
            
            # Check if operation took too long with Healenium
            if (self.healenium_enabled and 
                active_driver == self.healenium_driver and 
                time.time() - start_time > healenium_timeout):
                logger.warning(f"Healenium operation '{method_name}' took {time.time() - start_time:.1f}s, consider disabling")
            
            return result
            
        except Exception as e:
            error_msg = str(e).lower()

            # Check for session termination errors
            if any(err in error_msg for err in [
                "nosuchdriver", "no such session", "session is either terminated",
                "session not found", "invalid session id", "session has been terminated"
            ]):
                logger.error(f"Session terminated during '{method_name}': {e}")
                # Clear the dead Healenium driver if it was the active one
                if (self.healenium_enabled and
                    self.healenium_driver and
                    active_driver == self.healenium_driver):
                    logger.warning("Clearing dead Healenium driver due to session termination")
                    self.healenium_driver = None
                    self.healenium_enabled = False

            # If using Healenium and it fails, fallback to original driver
            if (self.healenium_enabled and
                self.healenium_driver and
                active_driver == self.healenium_driver and
                self.fallback_enabled):

                logger.warning(f"Healenium method '{method_name}' failed, falling back to original driver: {e}")

                try:
                    method = getattr(self.original_driver, method_name)
                    result = method(*args, **kwargs)
                    return result
                except Exception as fallback_error:
                    logger.error(f"Fallback to original driver also failed: {fallback_error}")
                    raise fallback_error
            else:
                # Re-raise the original exception
                raise e
    
    # WebDriver method proxies with self-healing support
    def find_element(self, by: str, value: str) -> WebElement:
        """Find element with self-healing support"""
        return self._execute_with_fallback('find_element', by, value)
    
    def find_elements(self, by: str, value: str) -> list:
        """Find elements with self-healing support"""
        return self._execute_with_fallback('find_elements', by, value)
    
    def tap(self, positions: list, duration: Optional[int] = None):
        """Tap with self-healing support"""
        return self._execute_with_fallback('tap', positions, duration)
    
    def swipe(self, start_x: int, start_y: int, end_x: int, end_y: int, duration: int = 1000):
        """Swipe with self-healing support"""
        return self._execute_with_fallback('swipe', start_x, start_y, end_x, end_y, duration)
    
    def scroll(self, origin_el: WebElement, destination_el: WebElement, duration: int = 1000):
        """Scroll with self-healing support"""
        return self._execute_with_fallback('scroll', origin_el, destination_el, duration)
    
    def get_screenshot_as_png(self) -> bytes:
        """Get screenshot with fallback support"""
        return self._execute_with_fallback('get_screenshot_as_png')
    
    def get_screenshot_as_file(self, filename: str) -> bool:
        """Save screenshot to file with fallback support"""
        return self._execute_with_fallback('get_screenshot_as_file', filename)
    

    
    @property
    def current_activity(self) -> str:
        """Get current activity with fallback support"""
        return self._execute_with_fallback('current_activity')
    
    def quit(self):
        """Quit both drivers"""
        try:
            if self.healenium_driver:
                self.healenium_driver.quit()
        except Exception as e:
            logger.warning(f"Error quitting Healenium driver: {e}")
        
        try:
            if self.original_driver:
                self.original_driver.quit()
        except Exception as e:
            logger.warning(f"Error quitting original driver: {e}")
    
    def close(self):
        """Close current window in both drivers"""
        try:
            if self.healenium_driver:
                self.healenium_driver.close()
        except Exception as e:
            logger.warning(f"Error closing Healenium driver: {e}")
        
        try:
            if self.original_driver:
                self.original_driver.close()
        except Exception as e:
            logger.warning(f"Error closing original driver: {e}")
    
    # Delegate all other attributes to the active driver
    def __getattr__(self, name):
        """Delegate unknown attributes to the active driver"""
        active_driver = self._get_active_driver()

        if hasattr(active_driver, name):
            attr = getattr(active_driver, name)

            # Special handling for properties that should return values directly
            if name in ['page_source', 'current_activity', 'current_package', 'session_id', 'capabilities']:
                # For these properties, return the value directly without wrapping
                try:
                    if callable(attr):
                        # If it's a method, call it and return the result
                        return attr()
                    else:
                        # If it's a property, return the value
                        return attr
                except Exception as e:
                    logger.warning(f"Error accessing {name} property: {e}")
                    # Fallback to original driver if Healenium fails
                    if self.fallback_enabled and hasattr(self.original_driver, name):
                        fallback_attr = getattr(self.original_driver, name)
                        if callable(fallback_attr):
                            return fallback_attr()
                        else:
                            return fallback_attr
                    raise

            # If it's a method, wrap it with fallback logic
            elif callable(attr):
                def wrapper(*args, **kwargs):
                    return self._execute_with_fallback(name, *args, **kwargs)
                return wrapper
            else:
                return attr

        raise AttributeError(f"'{type(self).__name__}' object has no attribute '{name}'")
    
    def enable_healenium(self):
        """Enable Healenium self-healing"""
        self.healenium_enabled = True
        if not self.healenium_driver:
            self._init_healenium_driver()
        logger.info("Healenium self-healing enabled")
    
    def disable_healenium(self):
        """Disable Healenium self-healing"""
        self.healenium_enabled = False
        logger.info("Healenium self-healing disabled")
    
    def enable_fallback(self):
        """Enable fallback to original driver"""
        self.fallback_enabled = True
        logger.info("Fallback to original driver enabled")
    
    def disable_fallback(self):
        """Disable fallback to original driver"""
        self.fallback_enabled = False
        logger.info("Fallback to original driver disabled")
    
    def get_healenium_status(self) -> Dict[str, Any]:
        """Get current Healenium status"""
        return {
            'healenium_enabled': self.healenium_enabled,
            'healenium_driver_available': self.healenium_driver is not None,
            'fallback_enabled': self.fallback_enabled,
            'active_driver': 'healenium' if (self.healenium_enabled and self.healenium_driver) else 'original',
            'healenium_health': healenium_config.check_healenium_health() if self.healenium_enabled else {}
        }


def create_healenium_driver(original_driver: webdriver.Remote) -> HealeniumWebDriver:
    """
    Factory function to create a Healenium-wrapped driver
    
    Args:
        original_driver: The original Appium WebDriver instance
        
    Returns:
        HealeniumWebDriver instance with self-healing capabilities
    """
    return HealeniumWebDriver(original_driver)
