import os
import logging
import json
from pathlib import Path

logger = logging.getLogger(__name__)

def get_reports_directory():
    """Get reports directory with platform isolation"""
    try:
        from app_android.utils.directory_paths_db import DirectoryPathsDB
        db = DirectoryPathsDB()
        reports_dir = db.get_reports_directory()
        if reports_dir:
            return reports_dir
    except Exception:
        pass
    
    # Try config.json
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
            if 'reports_directory' in config:
                return config['reports_directory']
    except Exception:
        pass
    
    # Default with platform suffix
    return 'reports_android'

def get_test_cases_directory():
    """Get test cases directory with platform isolation"""
    try:
        from app_android.utils.directory_paths_db import DirectoryPathsDB
        db = DirectoryPathsDB()
        test_cases_dir = db.get_test_cases_directory()
        if test_cases_dir:
            return test_cases_dir
    except Exception:
        pass
    
    # Try config.json
    try:
        with open('config.json', 'r') as f:
            config = json.load(f)
            if 'test_cases_directory' in config:
                return config['test_cases_directory']
    except Exception:
        pass
    
    # Default with platform suffix
    return 'test_cases_android'

def get_test_cases_directory():
    """
    Get the test cases directory path consistently across the application.

    Returns:
        str: The absolute path to the test cases directory
    """
    try:
        # First try to get from directory_paths_db
        try:
            import sys
            current_dir = os.path.dirname(os.path.abspath(__file__))
            if current_dir not in sys.path:
                sys.path.insert(0, current_dir)
            from directory_paths_db import directory_paths_db
            test_cases_dir = directory_paths_db.get_path('TEST_CASES')
            if test_cases_dir:
                logger.info(f"Using test cases directory from database: {test_cases_dir}")
                # Make sure it's an absolute path
                if not os.path.isabs(test_cases_dir):
                    base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
                    test_cases_dir = os.path.join(base_dir, test_cases_dir)
                return test_cases_dir
        except Exception as e:
            logger.error(f"Error getting test cases directory from database: {str(e)}")

        # Next try to get from config.json
        try:
            base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
            config_file = os.path.join(base_dir, 'config.json')
            if os.path.exists(config_file):
                with open(config_file, 'r') as f:
                    config_data = json.load(f)
                    test_cases_dir = config_data.get('TEST_CASES_DIR')
                    if test_cases_dir:
                        logger.info(f"Using test cases directory from config.json: {test_cases_dir}")
                        return test_cases_dir
        except Exception as e:
            logger.error(f"Error reading config.json: {str(e)}")

        # Fallback to default
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        test_cases_dir = os.path.join(base_dir, 'test_cases')
        logger.info(f"Using default test cases directory: {test_cases_dir}")
        return test_cases_dir
    except Exception as e:
        logger.error(f"Error in get_test_cases_directory: {str(e)}")
        # Ultimate fallback
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        return os.path.join(base_dir, 'test_cases')
