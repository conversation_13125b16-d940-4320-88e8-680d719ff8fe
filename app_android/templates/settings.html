<!-- Global Values Section -->
            <div class="card mt-4">
                <div class="card-header">
                    <h3 class="mb-0">Global Values</h3>
                </div>
                <div class="card-body">
                    <p class="card-text">Define global parameters that can be used in your test cases.</p>

                    <!-- Test Run Retry Setting (specifically highlighted) -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h5><i class="bi bi-arrow-repeat"></i> Test Retry Settings</h5>
                                <div class="form-group">
                                    <label for="testRunRetry">Test Run Retry</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="testRunRetry" placeholder="2" min="0" max="10">
                                        <span class="input-group-text"><i class="bi bi-info-circle" title="Number of times to retry a failed test"></i></span>
                                    </div>
                                    <small class="form-text text-muted">
                                        Failed test cases will be automatically retried this many times. Screenshots are preserved between retries.
                                    </small>
                                </div>
                                
                                <!-- Auto Rerun Failed Toggle Switch -->
                                <div class="form-group mt-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="autoRerunFailed">
                                        <label class="form-check-label" for="autoRerunFailed">Auto Rerun Failed</label>
                                    </div>
                                    <small class="form-text text-muted">
                                        When enabled, failed tests will be automatically rerun without requiring manual intervention. The system will retry up to the "Test Run Retry" value or until all tests pass.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Delay Settings Section -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h5><i class="bi bi-hourglass-split"></i> Delay Settings</h5>

                                <!-- Test Case Delay Setting -->
                                <div class="form-group mb-3">
                                    <label for="testCaseDelay">Delay Between Test Cases (seconds)</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="testCaseDelay" placeholder="10" min="0" max="60">
                                        <span class="input-group-text"><i class="bi bi-info-circle" title="Delay in seconds between test cases in a test suite"></i></span>
                                    </div>
                                    <small class="form-text text-muted">
                                        The system will wait this many seconds between executing test cases in a test suite. Default: 10 seconds.
                                    </small>
                                </div>

                                <!-- Step Delay Setting -->
                                <div class="form-group mb-3">
                                    <label for="stepDelay">Step Delay (seconds)</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="stepDelay" placeholder="2" min="0" max="10" step="0.5">
                                        <span class="input-group-text"><i class="bi bi-info-circle" title="Delay in seconds before executing multi-step actions"></i></span>
                                    </div>
                                    <small class="form-text text-muted">
                                        The system will wait this many seconds before executing multi-step actions. Default: 2 seconds.
                                    </small>
                                </div>

                                <!-- Hook Delay Setting -->
                                <div class="form-group mb-3">
                                    <label for="hookDelay">Hook Delay (seconds)</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="hookDelay" placeholder="2" min="0" max="10" step="0.5">
                                        <span class="input-group-text"><i class="bi bi-info-circle" title="Delay in seconds before executing hook actions"></i></span>
                                    </div>
                                    <small class="form-text text-muted">
                                        The system will wait this many seconds before executing hook actions. Default: 2 seconds.
                                    </small>
                                </div>

                                <!-- Speedy Execution Toggle -->
                                <div class="form-group mt-3">
                                    <div class="form-check form-switch">
                                        <input class="form-check-input" type="checkbox" id="speedyExecution">
                                        <label class="form-check-label" for="speedyExecution">
                                            <i class="bi bi-lightning-charge"></i> Speedy Execution Mode
                                        </label>
                                    </div>
                                    <small class="form-text text-muted">
                                        When enabled, step delays are reduced to 100ms for faster test execution. Useful for debugging or when running tests on stable environments.
                                    </small>
                                </div>

                                <!-- Retry Delay Setting -->
                                <div class="form-group">
                                    <label for="retryDelay">Retry Delay (seconds)</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="retryDelay" placeholder="2" min="0" max="10" step="0.5">
                                        <span class="input-group-text"><i class="bi bi-info-circle" title="Delay in seconds before retrying actions"></i></span>
                                    </div>
                                    <small class="form-text text-muted">
                                        The system will wait this many seconds before retrying actions. Default: 2 seconds.
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Max Step Execution Time Setting -->
                    <div class="row mb-3">
                        <div class="col-md-12">
                            <div class="alert alert-info">
                                <h5><i class="bi bi-alarm"></i> Step Timeout</h5>
                                <div class="form-group">
                                    <label for="maxStepExecutionTime">Maximum Step Execution Time (seconds)</label>
                                    <div class="input-group">
                                        <input type="number" class="form-control" id="maxStepExecutionTime" placeholder="300" min="10" max="1800">
                                        <span class="input-group-text"><i class="bi bi-info-circle" title="Maximum time allowed for a step to execute before stopping the test run"></i></span>
                                    </div>
                                    <small class="form-text text-muted">
                                        If any step takes longer than this time to execute, the entire test run will be stopped. This prevents hanging on steps that might never complete. Default: 300 seconds (5 minutes).
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- All Global Values table -->
                    <div class="table-responsive">
                        <table class="table table-hover table-bordered" id="globalValuesTable">
                            <thead class="table-light">
                                <tr>
                                    <th width="35%">Parameter Name</th>
                                    <th width="50%">Value</th>
                                    <th width="15%" class="text-center">Actions</th>
                                </tr>
                            </thead>
                            <tbody id="globalValuesTableBody">
                                <!-- This will be populated dynamically -->
                            </tbody>
                        </table>
                    </div>

                    <div class="d-flex justify-content-between mt-3">
                        <button type="button" class="btn btn-primary" id="addGlobalParameterBtn">
                            <i class="bi bi-plus-circle"></i> Add Parameter
                        </button>
                        <button type="button" class="btn btn-success" id="saveGlobalValuesBtn">
                            <i class="bi bi-save"></i> Save Global Values
                        </button>
                    </div>
                </div>
            </div>