/**
 * Bulk Locator Search and Replace Manager
 * Handles the user interface and API calls for bulk locator operations
 */
class BulkLocatorManager {
    constructor() {
        this.searchResults = [];
        this.currentBackups = [];
        this.isOperationInProgress = false;
        
        this.initializeElements();
        this.setupEventListeners();
        this.loadAvailableBackups();
    }
    
    initializeElements() {
        // Input elements
        this.folderPathInput = document.getElementById('folderPathInput');
        this.locatorValueInput = document.getElementById('locatorValueInput');
        this.newValueInput = document.getElementById('newValueInput');
        this.oldValueDisplay = document.getElementById('oldValueDisplay');
        
        // Button elements
        this.browseFolderBtn = document.getElementById('browseFolderBtn');
        this.validateLocatorBtn = document.getElementById('validateLocatorBtn');
        this.searchLocatorsBtn = document.getElementById('searchLocatorsBtn');
        this.searchReplaceBtn = document.getElementById('searchReplaceBtn');
        this.undoChangesBtn = document.getElementById('undoChangesBtn');
        this.confirmReplaceBtn = document.getElementById('confirmReplaceBtn');
        this.confirmUndoBtn = document.getElementById('confirmUndoBtn');
        
        // Display elements
        this.searchStatusBadge = document.getElementById('searchStatusBadge');
        this.progressContainer = document.getElementById('progressContainer');
        this.operationProgress = document.getElementById('operationProgress');
        this.progressText = document.getElementById('progressText');
        this.searchResultsContainer = document.getElementById('searchResultsContainer');
        this.searchResultsBody = document.getElementById('searchResultsBody');
        this.resultsCount = document.getElementById('resultsCount');
        this.alertContainer = document.getElementById('alertContainer');
        
        // Modal elements
        this.searchReplaceModal = new bootstrap.Modal(document.getElementById('searchReplaceModal'));
        this.undoModal = new bootstrap.Modal(document.getElementById('undoModal'));
        this.affectedFilesList = document.getElementById('affectedFilesList');
        this.totalMatchesSpan = document.getElementById('totalMatchesSpan');
        this.totalFilesSpan = document.getElementById('totalFilesSpan');
        this.confirmReplaceCheck = document.getElementById('confirmReplaceCheck');
        this.backupSelect = document.getElementById('backupSelect');
    }
    
    setupEventListeners() {
        // Button event listeners
        this.validateLocatorBtn.addEventListener('click', () => this.validateLocator());
        this.searchLocatorsBtn.addEventListener('click', () => this.performSearch());
        this.searchReplaceBtn.addEventListener('click', () => this.showReplaceModal());
        this.undoChangesBtn.addEventListener('click', () => this.showUndoModal());
        this.confirmReplaceBtn.addEventListener('click', () => this.performReplace());
        this.confirmUndoBtn.addEventListener('click', () => this.performUndo());
        
        // Input event listeners
        this.locatorValueInput.addEventListener('input', () => this.onLocatorValueChange());
        this.newValueInput.addEventListener('input', () => this.validateReplaceForm());
        this.confirmReplaceCheck.addEventListener('change', () => this.validateReplaceForm());
        this.backupSelect.addEventListener('change', () => this.validateUndoForm());
        
        // Enter key support
        this.locatorValueInput.addEventListener('keypress', (e) => {
            if (e.key === 'Enter') this.performSearch();
        });
        
        // Browse folder button (placeholder for future file browser integration)
        this.browseFolderBtn.addEventListener('click', () => {
            this.showAlert('info', 'File browser integration coming soon. For now, enter the folder path manually.');
        });
    }
    
    onLocatorValueChange() {
        const hasValue = this.locatorValueInput.value.trim().length > 0;
        this.searchLocatorsBtn.disabled = !hasValue;
        
        if (!hasValue) {
            this.clearSearchResults();
        }
    }
    
    async validateLocator() {
        const locatorValue = this.locatorValueInput.value.trim();
        
        if (!locatorValue) {
            this.showAlert('warning', 'Please enter a locator value to validate.');
            return;
        }
        
        try {
            this.setButtonLoading(this.validateLocatorBtn, true);
            
            const response = await fetch('/api/bulk_locator/validate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    locator_value: locatorValue
                })
            });
            
            const result = await response.json();
            
            if (result.valid) {
                this.showAlert('success', 
                    `Valid locator detected as: <strong>${result.detected_type}</strong>`);
            } else {
                this.showAlert('danger', `Invalid locator: ${result.error}`);
            }
            
        } catch (error) {
            console.error('Error validating locator:', error);
            this.showAlert('danger', 'Error validating locator. Please try again.');
        } finally {
            this.setButtonLoading(this.validateLocatorBtn, false);
        }
    }
    
    async performSearch() {
        const folderPath = this.folderPathInput.value.trim();
        const locatorValue = this.locatorValueInput.value.trim();
        
        if (!locatorValue) {
            this.showAlert('warning', 'Please enter a locator value to search for.');
            return;
        }
        
        try {
            this.setOperationInProgress(true);
            this.showProgress('Searching for locators...', 30);
            
            const response = await fetch('/api/bulk_locator/search', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    folder_path: folderPath,
                    locator_value: locatorValue
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.searchResults = result.results;
                this.displaySearchResults(result.results);
                this.showProgress('Search completed', 100);
                
                setTimeout(() => {
                    this.hideProgress();
                    this.setOperationInProgress(false);
                }, 1000);
                
                if (result.results.length > 0) {
                    this.searchReplaceBtn.disabled = false;
                    this.showAlert('success', 
                        `Found ${result.total_matches} matches across ${this.getUniqueFileCount(result.results)} files.`);
                } else {
                    this.showAlert('info', 'No matches found for the specified locator value.');
                }
            } else {
                throw new Error(result.error || 'Search failed');
            }
            
        } catch (error) {
            console.error('Error performing search:', error);
            this.showAlert('danger', `Search failed: ${error.message}`);
            this.hideProgress();
            this.setOperationInProgress(false);
        }
    }
    
    displaySearchResults(results) {
        this.searchResultsBody.innerHTML = '';
        
        if (results.length === 0) {
            this.searchResultsContainer.style.display = 'none';
            return;
        }
        
        results.forEach(result => {
            const row = document.createElement('tr');
            row.innerHTML = `
                <td>
                    <span class="badge bg-secondary">${result.filename}</span>
                </td>
                <td>
                    <span class="badge bg-primary">${result.action_type}</span>
                </td>
                <td>
                    <code>${result.locator_field}</code>
                </td>
                <td>
                    <code class="text-break">${this.escapeHtml(result.locator_value)}</code>
                </td>
                <td>
                    <span class="badge bg-info">${result.line_number}</span>
                </td>
            `;
            this.searchResultsBody.appendChild(row);
        });
        
        this.resultsCount.textContent = `${results.length} matches found`;
        this.searchResultsContainer.style.display = 'block';
    }
    
    showReplaceModal() {
        if (this.searchResults.length === 0) {
            this.showAlert('warning', 'Please perform a search first.');
            return;
        }
        
        // Populate modal with search results info
        const uniqueFiles = this.getUniqueFiles(this.searchResults);
        this.affectedFilesList.innerHTML = uniqueFiles.map(file => 
            `<li><i class="bi bi-file-earmark-text"></i> ${file}</li>`
        ).join('');
        
        this.oldValueDisplay.value = this.locatorValueInput.value;
        this.newValueInput.value = '';
        this.totalMatchesSpan.textContent = this.searchResults.length;
        this.totalFilesSpan.textContent = uniqueFiles.length;
        
        this.confirmReplaceCheck.checked = false;
        this.validateReplaceForm();
        
        this.searchReplaceModal.show();
    }
    
    validateReplaceForm() {
        const hasNewValue = this.newValueInput.value.trim().length > 0;
        const isConfirmed = this.confirmReplaceCheck.checked;
        
        this.confirmReplaceBtn.disabled = !(hasNewValue && isConfirmed);
    }
    
    async performReplace() {
        const oldValue = this.oldValueDisplay.value;
        const newValue = this.newValueInput.value.trim();
        
        try {
            this.setButtonLoading(this.confirmReplaceBtn, true);
            
            const response = await fetch('/api/bulk_locator/replace', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    search_results: this.searchResults,
                    old_value: oldValue,
                    new_value: newValue
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.searchReplaceModal.hide();
                this.showAlert('success', 
                    `Successfully updated ${result.updated_count} locators across ${result.updated_files.length} files. ` +
                    `Backup created: ${result.backup_timestamp}`);
                
                // Enable undo button and refresh backups
                this.undoChangesBtn.disabled = false;
                this.loadAvailableBackups();
                
                // Clear search results
                this.clearSearchResults();
                
            } else {
                throw new Error(result.error || 'Replace operation failed');
            }
            
        } catch (error) {
            console.error('Error performing replace:', error);
            this.showAlert('danger', `Replace failed: ${error.message}`);
        } finally {
            this.setButtonLoading(this.confirmReplaceBtn, false);
        }
    }
    
    async showUndoModal() {
        await this.loadAvailableBackups();
        this.validateUndoForm();
        this.undoModal.show();
    }
    
    async loadAvailableBackups() {
        try {
            const response = await fetch('/api/bulk_locator/backups');
            const result = await response.json();
            
            if (result.success) {
                this.currentBackups = result.backups;
                this.populateBackupSelect(result.backups);
                this.undoChangesBtn.disabled = result.backups.length === 0;
            }
            
        } catch (error) {
            console.error('Error loading backups:', error);
        }
    }
    
    populateBackupSelect(backups) {
        this.backupSelect.innerHTML = '';
        
        if (backups.length === 0) {
            this.backupSelect.innerHTML = '<option value="">No backups available</option>';
            return;
        }
        
        this.backupSelect.innerHTML = '<option value="">Select a backup to restore...</option>';
        
        backups.forEach(backup => {
            const option = document.createElement('option');
            option.value = backup.timestamp;
            option.textContent = `${backup.formatted_date} (${backup.file_count} files)`;
            this.backupSelect.appendChild(option);
        });
    }
    
    validateUndoForm() {
        this.confirmUndoBtn.disabled = !this.backupSelect.value;
    }
    
    async performUndo() {
        const backupTimestamp = this.backupSelect.value;
        
        try {
            this.setButtonLoading(this.confirmUndoBtn, true);
            
            const response = await fetch('/api/bulk_locator/undo', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    backup_timestamp: backupTimestamp
                })
            });
            
            const result = await response.json();
            
            if (result.success) {
                this.undoModal.hide();
                this.showAlert('success', 
                    `Successfully restored ${result.restored_count} files from backup.`);
                
                // Refresh backups list
                this.loadAvailableBackups();
                
            } else {
                throw new Error(result.error || 'Undo operation failed');
            }
            
        } catch (error) {
            console.error('Error performing undo:', error);
            this.showAlert('danger', `Undo failed: ${error.message}`);
        } finally {
            this.setButtonLoading(this.confirmUndoBtn, false);
        }
    }

    // Utility methods
    clearSearchResults() {
        this.searchResults = [];
        this.searchResultsContainer.style.display = 'none';
        this.searchReplaceBtn.disabled = true;
        this.searchResultsBody.innerHTML = '';
        this.resultsCount.textContent = '0 matches found';
    }

    getUniqueFiles(results) {
        const files = new Set();
        results.forEach(result => files.add(result.filename));
        return Array.from(files);
    }

    getUniqueFileCount(results) {
        return this.getUniqueFiles(results).length;
    }

    setOperationInProgress(inProgress) {
        this.isOperationInProgress = inProgress;
        this.searchLocatorsBtn.disabled = inProgress;
        this.searchReplaceBtn.disabled = inProgress || this.searchResults.length === 0;
        this.undoChangesBtn.disabled = inProgress || this.currentBackups.length === 0;
    }

    showProgress(text, percentage) {
        this.progressText.textContent = text;
        this.operationProgress.style.width = `${percentage}%`;
        this.operationProgress.setAttribute('aria-valuenow', percentage);
        this.progressContainer.style.display = 'block';
    }

    hideProgress() {
        this.progressContainer.style.display = 'none';
    }

    setButtonLoading(button, loading) {
        if (loading) {
            button.disabled = true;
            const originalText = button.innerHTML;
            button.setAttribute('data-original-text', originalText);
            button.innerHTML = '<span class="spinner-border spinner-border-sm me-2"></span>Loading...';
        } else {
            button.disabled = false;
            const originalText = button.getAttribute('data-original-text');
            if (originalText) {
                button.innerHTML = originalText;
                button.removeAttribute('data-original-text');
            }
        }
    }

    showAlert(type, message) {
        const alertId = 'alert-' + Date.now();
        const alertHtml = `
            <div class="alert alert-${type} alert-dismissible fade show" id="${alertId}" role="alert">
                ${message}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        `;

        this.alertContainer.innerHTML = alertHtml;

        // Auto-dismiss success and info alerts after 5 seconds
        if (type === 'success' || type === 'info') {
            setTimeout(() => {
                const alert = document.getElementById(alertId);
                if (alert) {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }
            }, 5000);
        }
    }

    escapeHtml(text) {
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }
}

// Initialize the bulk locator manager when the page loads
document.addEventListener('DOMContentLoaded', function() {
    if (document.getElementById('folderPathInput')) {
        window.bulkLocatorManager = new BulkLocatorManager();
    }
});
