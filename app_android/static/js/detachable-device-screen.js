/**
 * Detachable Device Screen Manager
 * Creates a completely separate window for device screen monitoring
 * with platform identification and independent operation
 */
class DetachableDeviceScreenManager {
    constructor(platform = 'Android') {
        this.platform = platform;
        this.detachedWindow = null;
        this.deviceScreen = null;
        this.overlayCanvas = null;
        this.loadingOverlay = null;
        this.observer = null;
        this.syncInterval = null;
        this.isDetached = false;
        this.windowFeatures = 'width=400,height=700,scrollbars=no,resizable=yes,status=no,location=no,toolbar=no,menubar=no';
        
        // Find the original device screen elements
        this.findDeviceScreenElements();
        
        // Set up event listeners
        this.setupEventListeners();
    }

    /**
     * Find the original device screen elements in the main window
     */
    findDeviceScreenElements() {
        this.deviceScreen = document.getElementById('deviceScreen');
        this.overlayCanvas = document.getElementById('overlayCanvas');
        this.loadingOverlay = document.querySelector('.loading-overlay');
        
        if (!this.deviceScreen) {
            console.warn('Device screen element not found');
        }
    }

    /**
     * Set up event listeners for window communication
     */
    setupEventListeners() {
        // Listen for window close events
        window.addEventListener('beforeunload', () => {
            this.closeDetachedWindow();
        });
        
        // Listen for messages from the detached window
        window.addEventListener('message', (event) => {
            if (event.data.type === 'DETACHED_WINDOW_CLOSED') {
                this.handleDetachedWindowClosed();
            }
        });
    }

    /**
     * Create and open the detached device screen window
     */
    detachDeviceScreen() {
        if (this.isDetached || !this.deviceScreen) {
            console.warn('Device screen already detached or not found');
            return;
        }

        // Create the detached window
        this.detachedWindow = window.open('', 'DeviceScreen', this.windowFeatures);
        
        if (!this.detachedWindow) {
            alert('Popup blocked! Please allow popups for this site to use the detachable device screen.');
            return;
        }

        // Set up the detached window content
        this.setupDetachedWindowContent();
        
        // Set up synchronization
        this.setupDeviceScreenSync();
        
        this.isDetached = true;
        
        // Hide the original device screen container (optional)
        const originalContainer = document.querySelector('.device-screen-container');
        if (originalContainer) {
            originalContainer.style.opacity = '0.3';
            originalContainer.style.pointerEvents = 'none';
        }
        
        console.log(`${this.platform} device screen detached successfully`);
    }

    /**
     * Set up the content of the detached window
     */
    setupDetachedWindowContent() {
        const doc = this.detachedWindow.document;
        
        // Set up the HTML structure
        doc.open();
        doc.write(`
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <title>${this.platform} Device Screen</title>
                <style>
                    body {
                        margin: 0;
                        padding: 0;
                        background: #1a1a1a;
                        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                        overflow: hidden;
                    }
                    
                    .header {
                        background: linear-gradient(135deg, #4CAF50 0%, #45a049 100%);
                        color: white;
                        padding: 10px 15px;
                        text-align: center;
                        font-weight: 600;
                        font-size: 14px;
                        box-shadow: 0 2px 10px rgba(0,0,0,0.3);
                        position: relative;
                    }
                    
                    .platform-badge {
                        display: inline-block;
                        background: rgba(255,255,255,0.2);
                        padding: 4px 12px;
                        border-radius: 20px;
                        font-size: 12px;
                        margin-left: 10px;
                    }
                    
                    .close-btn {
                        position: absolute;
                        right: 10px;
                        top: 50%;
                        transform: translateY(-50%);
                        background: rgba(255,255,255,0.2);
                        border: none;
                        color: white;
                        width: 24px;
                        height: 24px;
                        border-radius: 50%;
                        cursor: pointer;
                        font-size: 14px;
                        display: flex;
                        align-items: center;
                        justify-content: center;
                    }
                    
                    .close-btn:hover {
                        background: rgba(255,255,255,0.3);
                    }
                    
                    .device-container {
                        position: relative;
                        width: 100%;
                        height: calc(100vh - 44px);
                        display: flex;
                        align-items: center;
                        justify-content: center;
                        background: #2a2a2a;
                    }
                    
                    .device-screen {
                        max-width: 100%;
                        max-height: 100%;
                        border-radius: 8px;
                        box-shadow: 0 4px 20px rgba(0,0,0,0.5);
                        background: #000;
                    }
                    
                    .loading-overlay {
                        position: absolute;
                        top: 50%;
                        left: 50%;
                        transform: translate(-50%, -50%);
                        color: #888;
                        font-size: 14px;
                        text-align: center;
                    }
                    
                    .status-indicator {
                        position: absolute;
                        top: 10px;
                        left: 10px;
                        width: 8px;
                        height: 8px;
                        border-radius: 50%;
                        background: #4CAF50;
                        animation: pulse 2s infinite;
                    }
                    
                    @keyframes pulse {
                        0% { opacity: 1; }
                        50% { opacity: 0.5; }
                        100% { opacity: 1; }
                    }
                    
                    .error-state {
                        background: #f44336 !important;
                    }
                </style>
            </head>
            <body>
                <div class="header">
                    <span>${this.platform} Device Screen</span>
                    <span class="platform-badge">${this.platform}</span>
                    <button class="close-btn" onclick="closeWindow()" title="Close Window">×</button>
                </div>
                <div class="device-container">
                    <div class="status-indicator" id="statusIndicator"></div>
                    <img id="detachedDeviceScreen" class="device-screen" alt="${this.platform} Device Screen" />
                    <div class="loading-overlay" id="loadingOverlay">
                        Connecting to ${this.platform} device...
                    </div>
                </div>
                
                <script>
                    function closeWindow() {
                        // Notify parent window
                        if (window.opener) {
                            window.opener.postMessage({type: 'DETACHED_WINDOW_CLOSED'}, '*');
                        }
                        window.close();
                    }
                    
                    // Handle window close event
                    window.addEventListener('beforeunload', function() {
                        if (window.opener) {
                            window.opener.postMessage({type: 'DETACHED_WINDOW_CLOSED'}, '*');
                        }
                    });
                    
                    // Update status indicator based on image load
                    const deviceScreen = document.getElementById('detachedDeviceScreen');
                    const statusIndicator = document.getElementById('statusIndicator');
                    const loadingOverlay = document.getElementById('loadingOverlay');
                    
                    deviceScreen.onload = function() {
                        statusIndicator.classList.remove('error-state');
                        loadingOverlay.style.display = 'none';
                    };
                    
                    deviceScreen.onerror = function() {
                        statusIndicator.classList.add('error-state');
                        loadingOverlay.style.display = 'block';
                        loadingOverlay.textContent = 'Connection lost - Reconnecting...';
                    };
                </script>
            </body>
            </html>
        `);
        doc.close();
        
        // Focus the new window
        this.detachedWindow.focus();
    }

    /**
     * Set up synchronization between original and detached device screens
     */
    setupDeviceScreenSync() {
        if (!this.detachedWindow || !this.deviceScreen) return;
        
        const detachedScreen = this.detachedWindow.document.getElementById('detachedDeviceScreen');
        
        // Initial sync
        if (this.deviceScreen.src) {
            detachedScreen.src = this.deviceScreen.src;
        }
        
        // Set up MutationObserver to watch for changes
        this.observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
                if (mutation.type === 'attributes' && mutation.attributeName === 'src') {
                    if (this.detachedWindow && !this.detachedWindow.closed) {
                        const detachedScreen = this.detachedWindow.document.getElementById('detachedDeviceScreen');
                        if (detachedScreen && this.deviceScreen) {
                            detachedScreen.src = this.deviceScreen.src;
                        }
                    }
                }
            });
        });
        
        // Start observing
        this.observer.observe(this.deviceScreen, {
            attributes: true,
            attributeFilter: ['src']
        });
        
        // Set up periodic sync as fallback
        this.syncInterval = setInterval(() => {
            if (this.detachedWindow && !this.detachedWindow.closed) {
                const detachedScreen = this.detachedWindow.document.getElementById('detachedDeviceScreen');
                if (detachedScreen && this.deviceScreen && detachedScreen.src !== this.deviceScreen.src) {
                    detachedScreen.src = this.deviceScreen.src;
                }
            } else {
                // Window was closed, clean up
                this.handleDetachedWindowClosed();
            }
        }, 1000);
    }

    /**
     * Handle when the detached window is closed
     */
    handleDetachedWindowClosed() {
        this.isDetached = false;
        
        // Clean up observers and intervals
        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }
        
        if (this.syncInterval) {
            clearInterval(this.syncInterval);
            this.syncInterval = null;
        }
        
        // Restore the original device screen container
        const originalContainer = document.querySelector('.device-screen-container');
        if (originalContainer) {
            originalContainer.style.opacity = '1';
            originalContainer.style.pointerEvents = 'auto';
        }
        
        this.detachedWindow = null;
        console.log(`${this.platform} device screen reattached`);
    }

    /**
     * Close the detached window programmatically
     */
    closeDetachedWindow() {
        if (this.detachedWindow && !this.detachedWindow.closed) {
            this.detachedWindow.close();
        }
        this.handleDetachedWindowClosed();
    }

    /**
     * Check if the device screen is currently detached
     */
    isDeviceScreenDetached() {
        return this.isDetached && this.detachedWindow && !this.detachedWindow.closed;
    }

    /**
     * Toggle between attached and detached states
     */
    toggleDetachment() {
        if (this.isDeviceScreenDetached()) {
            this.closeDetachedWindow();
        } else {
            this.detachDeviceScreen();
        }
    }
}

// Export for use in other modules
window.DetachableDeviceScreenManager = DetachableDeviceScreenManager;
