<!-- Add this floating status panel after the main content -->
<div id="execution-status-panel" class="execution-status-panel">
  <div class="status-header">
    <h3>Execution Status</h3>
    <button class="minimize-btn" id="minimize-status-panel">_</button>
  </div>
  <div class="status-content">
    <div class="progress-container">
      <div class="progress-bar" id="execution-progress-bar"></div>
    </div>
    <div class="status-info">
      <span id="current-action-label">Ready</span>
      <span id="progress-text">0/0</span>
    </div>
    <div class="status-log" id="execution-log"></div>
  </div>
</div>

<!-- Add this after the test case actions table -->
<div class="panel panel-default">
  <div class="panel-heading">
    <h3 class="panel-title">Action Log</h3>
  </div>
  <div class="panel-body">
    <div id="action-log" class="action-log-container">
      <!-- Action logs will appear here -->
    </div>
  </div>
</div>

<!-- Add this near your Action Log section -->
<div class="panel panel-default">
  <div class="panel-heading">
    <h3 class="panel-title">Terminal Log</h3>
  </div>
  <div class="panel-body">
    <div id="terminal-log" class="terminal-log-container">
      <!-- Terminal log entries will be displayed here -->
    </div>
  </div>
</div>

<!-- Add this to your HTML head section -->
<script src="/static/js/execution-manager.js"></script>

<!-- Replace any existing Execute All button with this -->
<button id="execute-all-btn" class="btn btn-primary">
  <i class="fa fa-play"></i> Execute All
</button>

<!-- Make sure your Stop Execution button has this ID -->
<button id="stop-execution-btn" class="btn btn-danger">
  <i class="fa fa-stop"></i> Stop Execution
</button>

<!-- Find where your action rows are generated and ensure they have this structure -->
<tr id="action-{index}" class="action-row">
  <td class="action-status"></td>
  <td>Action description</td>
  <!-- other cells -->
</tr>

<!-- Add this at the bottom of your page, before closing body tag -->
<div id="debug-console" style="position: fixed; bottom: 0; right: 0; width: 600px; height: 300px; background: rgba(0,0,0,0.8); color: #0f0; font-family: monospace; padding: 10px; overflow: auto; z-index: 9999; font-size: 12px; display: none;">
  <div style="position: absolute; top: 5px; right: 5px;">
    <button onclick="document.getElementById('debug-console').style.display = 'none';" style="background: none; border: none; color: white;">✕</button>
  </div>
  <div id="debug-log" style="height: 100%; overflow: auto;"></div>
</div>
<button onclick="document.getElementById('debug-console').style.display = 'block';" style="position: fixed; bottom: 10px; right: 10px; z-index: 9998;">Debug</button>

<script>
// Debug logging function
function debugLog(message, type = 'info') {
  const debugLog = document.getElementById('debug-log');
  if (!debugLog) return;
  
  const entry = document.createElement('div');
  entry.style.borderBottom = '1px solid #333';
  entry.style.padding = '2px 0';
  
  // Color based on type
  if (type === 'error') entry.style.color = '#f00';
  if (type === 'success') entry.style.color = '#0f0';
  if (type === 'warning') entry.style.color = '#ff0';
  
  const time = new Date().toLocaleTimeString();
  entry.textContent = `[${time}] ${message}`;
  
  debugLog.appendChild(entry);
  debugLog.scrollTop = debugLog.scrollHeight;
  
  // Also log to console
  console.log(`DEBUG: ${message}`);
}
</script> 