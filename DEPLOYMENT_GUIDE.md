# Mobile Testing SaaS Platform - Deployment Guide

## ⚠️ IMPORTANT NOTICE

**This deployment guide references files and infrastructure that do not exist yet.** Before following this guide, you need to implement the core SaaS components. See `UPDATED_SAAS_DEPLOYMENT_ANALYSIS.md` for a complete analysis and implementation plan.

### Missing Critical Files:
- `saas_unified_server.py` - Main SaaS application
- `local_device_agent.py` - Local device agent
- `requirements_saas.txt` - SaaS dependencies
- User authentication system
- Multi-tenant database schema

## Overview

This guide will help you deploy a unified SaaS platform for mobile app testing that supports both iOS and Android devices. The platform consists of:

1. **Cloud Server**: Unified web platform with user management and test execution
2. **Local Device Agent**: Lightweight agent that users install to connect their physical devices

## Architecture

```
┌─────────────────┐    WebSocket    ┌──────────────────┐
│   Cloud Server  │◄──────────────►│ Local Device     │
│                 │                 │ Agent (User's    │
│ - Web Dashboard │                 │ Machine)         │
│ - User Auth     │                 │                  │
│ - Test Queue    │                 │ - iOS Devices    │
│ - WebSocket API │                 │ - Android Devices│
└─────────────────┘                 └──────────────────┘
```

## Prerequisites

### Server Requirements
- Python 3.8+
- Redis server
- 2GB+ RAM
- 10GB+ storage
- Public IP address or domain

### Local Device Agent Requirements (for users)
- Python 3.8+
- iOS: Xcode Command Line Tools (for `idevice_id`)
- Android: Android SDK Platform Tools (for `adb`)
- Physical iOS/Android devices connected via USB

## Step 1: Server Setup

### 1.1 Install Dependencies

```bash
# Update system
sudo apt update && sudo apt upgrade -y

# Install Python and pip
sudo apt install python3 python3-pip python3-venv -y

# Install PostgreSQL and Redis
sudo apt install postgresql postgresql-contrib redis-server -y

# Start and enable services
sudo systemctl start postgresql redis-server
sudo systemctl enable postgresql redis-server

# Setup PostgreSQL database
sudo -u postgres psql << EOF
CREATE DATABASE saas_platform;
CREATE USER saas_user WITH PASSWORD 'saas_password';
GRANT ALL PRIVILEGES ON DATABASE saas_platform TO saas_user;
ALTER USER saas_user CREATEDB;
\q
EOF
```

### 1.2 Clone and Setup Project

```bash
# Clone your project
git clone <your-repo-url>
cd MobileApp-AutoTest

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install Python dependencies
pip install -r requirements_saas.txt
```

### 1.3 Configure Environment

Create `.env` file:

```bash
cat > .env << EOF
FLASK_ENV=production
SECRET_KEY=your-super-secret-key-change-this
REDIS_URL=redis://localhost:6379/0
DATABASE_URL=postgresql://saas_user:saas_password@localhost:5432/saas_platform
JWT_SECRET_KEY=your-jwt-secret-key-change-this
SERVER_HOST=0.0.0.0
SERVER_PORT=8080
EOF
```

### 1.4 Initialize Database

```bash
# Run the server once to create database tables
python saas_unified_server.py
# Press Ctrl+C after you see "Database initialized successfully"
```

## Step 2: Production Deployment

### 2.1 Using Gunicorn (Recommended)

```bash
# Install Gunicorn
pip install gunicorn

# Create Gunicorn configuration
cat > gunicorn.conf.py << EOF
bind = "0.0.0.0:8080"
workers = 4
worker_class = "eventlet"
worker_connections = 1000
timeout = 120
keepalive = 2
max_requests = 1000
max_requests_jitter = 100
preload_app = True
EOF

# Run with Gunicorn
gunicorn --config gunicorn.conf.py saas_unified_server:app
```

### 2.2 Using Systemd Service

Create service file:

```bash
sudo cat > /etc/systemd/system/mobile-testing-saas.service << EOF
[Unit]
Description=Mobile Testing SaaS Platform
After=network.target redis.service
Requires=redis.service

[Service]
Type=exec
User=ubuntu
Group=ubuntu
WorkingDirectory=/home/<USER>/MobileApp-AutoTest
Environment=PATH=/home/<USER>/MobileApp-AutoTest/venv/bin
ExecStart=/home/<USER>/MobileApp-AutoTest/venv/bin/gunicorn --config gunicorn.conf.py saas_unified_server:app
Restart=always
RestartSec=10

[Install]
WantedBy=multi-user.target
EOF

# Enable and start service
sudo systemctl daemon-reload
sudo systemctl enable mobile-testing-saas
sudo systemctl start mobile-testing-saas

# Check status
sudo systemctl status mobile-testing-saas
```

### 2.3 Nginx Reverse Proxy (Optional but Recommended)

```bash
# Install Nginx
sudo apt install nginx -y

# Create Nginx configuration
sudo cat > /etc/nginx/sites-available/mobile-testing-saas << EOF
server {
    listen 80;
    server_name your-domain.com;  # Replace with your domain
    
    location / {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_set_header Upgrade \$http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host \$host;
        proxy_set_header X-Real-IP \$remote_addr;
        proxy_set_header X-Forwarded-For \$proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto \$scheme;
        proxy_cache_bypass \$http_upgrade;
        proxy_read_timeout 86400;
    }
}
EOF

# Enable site
sudo ln -s /etc/nginx/sites-available/mobile-testing-saas /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl restart nginx
```

### 2.4 SSL Certificate (Recommended)

```bash
# Install Certbot
sudo apt install certbot python3-certbot-nginx -y

# Get SSL certificate
sudo certbot --nginx -d your-domain.com
```

## Step 3: Firewall Configuration

```bash
# Allow necessary ports
sudo ufw allow 22/tcp    # SSH
sudo ufw allow 80/tcp    # HTTP
sudo ufw allow 443/tcp   # HTTPS
sudo ufw allow 8080/tcp  # Application (if not using Nginx)
sudo ufw enable
```

## Step 4: User Setup Guide

### 4.1 Download Device Agent

Users can download the device agent from your platform:
- Visit: `https://your-domain.com`
- Register/Login to get their token
- Download `local_device_agent.py` from the dashboard

### 4.2 Local Device Agent Setup (For Users)

#### iOS Setup:
```bash
# Install Xcode Command Line Tools
xcode-select --install

# Install libimobiledevice (macOS)
brew install libimobiledevice

# Or on Ubuntu/Linux
sudo apt install libimobiledevice-utils
```

#### Android Setup:
```bash
# Install Android SDK Platform Tools
# Download from: https://developer.android.com/studio/releases/platform-tools

# Add to PATH
export PATH=$PATH:/path/to/platform-tools

# Enable USB Debugging on Android device
# Settings > Developer Options > USB Debugging
```

#### Install Python Dependencies:
```bash
pip install socketio-client requests PyJWT
```

#### Run Device Agent:
```bash
python local_device_agent.py --server https://your-domain.com --token YOUR_TOKEN_FROM_DASHBOARD
```

## Step 5: Testing the Deployment

### 5.1 Server Health Check

```bash
# Check if server is running
curl http://localhost:8080/

# Check WebSocket connection
curl -H "Upgrade: websocket" -H "Connection: Upgrade" http://localhost:8080/socket.io/
```

### 5.2 End-to-End Test

1. Open browser and go to your domain
2. Register a new user account
3. Copy the user token from dashboard
4. Run device agent with the token
5. Verify devices appear in dashboard
6. Create and execute a test

## Step 6: Monitoring and Maintenance

### 6.1 Log Monitoring

```bash
# Application logs
sudo journalctl -u mobile-testing-saas -f

# Nginx logs
sudo tail -f /var/log/nginx/access.log
sudo tail -f /var/log/nginx/error.log

# Redis logs
sudo journalctl -u redis -f
```

### 6.2 Database Backup

```bash
# Create backup script
cat > backup.sh << EOF
#!/bin/bash
DATE=$(date +%Y%m%d_%H%M%S)
cp saas_platform.db backups/saas_platform_\$DATE.db
find backups/ -name "*.db" -mtime +7 -delete
EOF

chmod +x backup.sh

# Add to crontab for daily backups
echo "0 2 * * * /home/<USER>/MobileApp-AutoTest/backup.sh" | crontab -
```

### 6.3 Updates

```bash
# Update application
git pull origin main
source venv/bin/activate
pip install -r requirements_saas.txt
sudo systemctl restart mobile-testing-saas
```

## Step 7: Scaling Considerations

### 7.1 Horizontal Scaling

- Use load balancer (HAProxy/Nginx)
- Redis Cluster for session storage
- Database replication
- Multiple server instances

### 7.2 Performance Optimization

```bash
# Redis optimization
sudo nano /etc/redis/redis.conf
# Set: maxmemory 1gb
# Set: maxmemory-policy allkeys-lru

# System optimization
echo 'net.core.somaxconn = 65535' | sudo tee -a /etc/sysctl.conf
echo 'fs.file-max = 100000' | sudo tee -a /etc/sysctl.conf
sudo sysctl -p
```

## Troubleshooting

### Common Issues

1. **WebSocket Connection Failed**
   - Check firewall settings
   - Verify Redis is running
   - Check application logs

2. **Device Agent Can't Connect**
   - Verify server URL is accessible
   - Check token validity
   - Ensure USB debugging is enabled

3. **High Memory Usage**
   - Monitor Redis memory usage
   - Implement session cleanup
   - Check for memory leaks in logs

### Debug Commands

```bash
# Check service status
sudo systemctl status mobile-testing-saas redis nginx

# Check port usage
sudo netstat -tlnp | grep :8080

# Check Redis connection
redis-cli ping

# Test WebSocket
wscat -c ws://localhost:8080/socket.io/?EIO=4&transport=websocket
```

## Security Considerations

1. **Change Default Secrets**: Update all secret keys in `.env`
2. **Use HTTPS**: Always use SSL certificates in production
3. **Firewall**: Only open necessary ports
4. **Regular Updates**: Keep system and dependencies updated
5. **User Input Validation**: Server validates all user inputs
6. **Rate Limiting**: Implement rate limiting for API endpoints

## Support

For issues and questions:
1. Check application logs
2. Review this deployment guide
3. Check Redis and system status
4. Verify network connectivity

---

**Congratulations!** Your Mobile Testing SaaS Platform is now deployed and ready for users to connect their devices and run tests.