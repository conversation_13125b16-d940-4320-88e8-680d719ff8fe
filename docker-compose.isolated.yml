version: '3.8'

services:
  # Template service for iOS instances (will be dynamically created)
  ios-template:
    build:
      context: .
      dockerfile: Dockerfile.ios
    environment:
      - FLASK_ENV=production
      - APPIUM_PORT=4723
      - FLASK_PORT=8080
      - SESSION_ID=${SESSION_ID:-default}
      - USER_ID=${USER_ID:-1}
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - /dev/bus/usb:/dev/bus/usb
    privileged: true
    networks:
      - isolation-network
    deploy:
      replicas: 0  # Template only, instances created dynamically
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.ios-${SESSION_ID:-default}.rule=PathPrefix(`/ios/${SESSION_ID:-default}`)"
      - "traefik.http.services.ios-${SESSION_ID:-default}.loadbalancer.server.port=8080"

  # Template service for Android instances (will be dynamically created)
  android-template:
    build:
      context: .
      dockerfile: Dockerfile.android
    environment:
      - FLASK_ENV=production
      - APPIUM_PORT=4721
      - FLASK_PORT=8081
      - SESSION_ID=${SESSION_ID:-default}
      - USER_ID=${USER_ID:-1}
      - JAVA_HOME=/usr/lib/jvm/java-11-openjdk-amd64
    volumes:
      - ./logs:/app/logs
      - ./uploads:/app/uploads
      - /dev/bus/usb:/dev/bus/usb
    privileged: true
    networks:
      - isolation-network
    deploy:
      replicas: 0  # Template only, instances created dynamically
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.android-${SESSION_ID:-default}.rule=PathPrefix(`/android/${SESSION_ID:-default}`)"
      - "traefik.http.services.android-${SESSION_ID:-default}.loadbalancer.server.port=8081"

  # Reverse proxy for routing to isolated instances
  traefik:
    image: traefik:v2.10
    command:
      - "--api.insecure=true"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"  # Traefik dashboard
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock:ro
    networks:
      - isolation-network

  # Redis for session management
  redis-isolation:
    image: redis:7-alpine
    ports:
      - "6380:6379"
    volumes:
      - redis-isolation-data:/data
    networks:
      - isolation-network
    command: redis-server --appendonly yes

networks:
  isolation-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16

volumes:
  redis-isolation-data:
    driver: local