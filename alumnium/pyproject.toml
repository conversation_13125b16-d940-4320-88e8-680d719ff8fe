[build-system]
build-backend = "poetry.core.masonry.api"
requires = [
    "poetry-core",
]

[tool.poetry]
authors = [
    "<PERSON> <<EMAIL>>",
    "<PERSON> <<EMAIL>>",
]
description = "Pave the way towards AI-powered test automation."
homepage = "https://alumnium.ai"
license = "MIT"
name = "alumnium"
readme = "README.md"
repository = "https://github.com/alumnium-hq/alumnium"
version = "0.12.0"

[tool.poetry.dependencies]
appium-python-client = "^5.1.1"
langchain = "^0.3"
langchain-anthropic = "^0.2"
langchain-aws = "^0.2"
langchain-deepseek = "^0.1.2"
langchain-google-genai = "^2.0"
langchain-mistralai = "^0.2.10"
langchain-ollama = "^0.3"
langchain-openai = "^0.3"
playwright = "^1.49"
python = "^3.10"
retry2 = "^0.9"
rich = "^14.0.0"
selenium = "^4.0"
sqlalchemy = "^2.0"
xxhash = "^3.5.0"

[tool.poetry.group.dev.dependencies]
behave = "^1.2.6"
behave-html-pretty-formatter = "^1.12"
pre-commit = "^4.2.0"
pyprojectsort = "^0.4.0"
pytest = "^8.3.3"
pytest-html = "^4.1.1"
pytest-retry = "^1.7.0"
python-dotenv = "^1.1.1"
ruff = "^0.11.9"

[tool.pytest.ini_options]
generate_report_on_test = true
pythonpath = "."
retry_delay = 0.5

[tool.ruff]
extend-exclude = [
    ".git",
    ".venv",
    "dist",
    "log",
    "reports",
]
line-length = 119

[tool.ruff.lint]
extend-select = [
    "I",
]
ignore = [
    "E402",
    "F401",
    "F403",
    "F405",
]
select = [
    "E",
    "F",
    "W",
]

[tool.ruff.lint.per-file-ignores]
"examples/behave/features/steps/*" = [
    "F811",
]
