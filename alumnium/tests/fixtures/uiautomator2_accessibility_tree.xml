<?xml version='1.0' encoding='UTF-8' standalone='yes' ?>
<hierarchy index="0" class="hierarchy" rotation="0" width="1080" height="2176">
  <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,0][1080,2176]" displayed="true">
    <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,0][1080,2176]" displayed="true">
      <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,80][1080,2176]" displayed="true">
        <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" resource-id="org.wikipedia.alpha:id/action_bar_root" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,80][1080,2176]" displayed="true">
          <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" resource-id="android:id/content" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,80][1080,2176]" displayed="true">
            <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,80][1080,2176]" displayed="true">
              <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" resource-id="org.wikipedia.alpha:id/fragment_container" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,80][1080,2176]" displayed="true">
                <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" resource-id="org.wikipedia.alpha:id/fragment_main_container" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,80][1080,2176]" displayed="true">
                  <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,248][1080,2176]" displayed="true">
                    <android.view.ViewGroup index="0" package="org.wikipedia.alpha" class="android.view.ViewGroup" text="" resource-id="org.wikipedia.alpha:id/fragment_main_coordinator" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,248][1080,2088]" displayed="true">
                      <android.support.v4.view.ViewPager index="0" package="org.wikipedia.alpha" class="android.support.v4.view.ViewPager" text="" resource-id="org.wikipedia.alpha:id/fragment_main_view_pager" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="true" selected="false" bounds="[0,248][1080,2088]" displayed="true">
                        <android.view.ViewGroup index="0" package="org.wikipedia.alpha" class="android.view.ViewGroup" text="" resource-id="org.wikipedia.alpha:id/feed_swipe_refresh_layout" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,248][1080,2088]" displayed="true">
                          <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="true" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,248][1080,2088]" displayed="true">
                            <android.support.v7.widget.RecyclerView index="0" package="org.wikipedia.alpha" class="android.support.v7.widget.RecyclerView" text="" resource-id="org.wikipedia.alpha:id/fragment_feed_feed" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="true" selected="false" bounds="[0,248][1080,2088]" displayed="true">
                              <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[21,293][1059,437]" displayed="true">
                                <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" resource-id="org.wikipedia.alpha:id/search_container" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[21,293][1059,437]" displayed="true">
                                  <android.widget.ImageView index="0" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" content-desc="Search Wikipedia" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[21,293][165,437]" displayed="true" />
                                  <android.widget.TextView index="1" package="org.wikipedia.alpha" class="android.widget.TextView" text="Search Wikipedia" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[165,336][915,393]" displayed="true" />
                                  <android.widget.ImageView index="2" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" content-desc="Search Wikipedia" resource-id="org.wikipedia.alpha:id/voice_search_button" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="true" password="false" scrollable="false" selected="false" bounds="[915,293][1059,437]" displayed="true" />
                                </android.widget.LinearLayout>
                              </android.widget.FrameLayout>
                              <android.widget.FrameLayout index="1" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[21,467][1059,1295]" displayed="true">
                                <android.widget.RelativeLayout index="0" package="org.wikipedia.alpha" class="android.widget.RelativeLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[21,467][1059,1295]" displayed="true">
                                  <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" resource-id="org.wikipedia.alpha:id/view_list_card_header" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[21,467][1059,611]" displayed="true">
                                    <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[21,467][1059,611]" displayed="true">
                                      <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[21,467][734,611]" displayed="true">
                                        <android.widget.ImageView index="0" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/view_card_header_image" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[69,503][141,575]" displayed="true" />
                                        <android.widget.TextView index="1" package="org.wikipedia.alpha" class="android.widget.TextView" text="In the news" resource-id="org.wikipedia.alpha:id/view_card_header_title" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[165,510][379,567]" displayed="true" />
                                      </android.widget.LinearLayout>
                                      <android.widget.LinearLayout index="1" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[734,479][1059,599]" displayed="true">
                                        <android.widget.TextView index="0" package="org.wikipedia.alpha" class="android.widget.TextView" text="Jun 25, 2025" resource-id="org.wikipedia.alpha:id/view_card_header_subtitle" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[734,514][939,563]" displayed="true" />
                                        <android.widget.ImageView index="1" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" content-desc="More options" resource-id="org.wikipedia.alpha:id/view_list_card_header_menu" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[939,479][1059,599]" displayed="true" />
                                      </android.widget.LinearLayout>
                                    </android.widget.LinearLayout>
                                  </android.widget.FrameLayout>
                                  <android.support.v7.widget.RecyclerView index="1" package="org.wikipedia.alpha" class="android.support.v7.widget.RecyclerView" text="" resource-id="org.wikipedia.alpha:id/view_list_card_list" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="true" selected="false" bounds="[21,611][1059,1295]" displayed="true">
                                    <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[69,611][609,1235]" displayed="true">
                                      <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[69,611][609,1235]" displayed="true">
                                        <android.widget.ImageView index="0" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/horizontal_scroll_list_item_image" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[69,611][609,899]" displayed="true" />
                                        <android.widget.TextView index="1" package="org.wikipedia.alpha" class="android.widget.TextView" text="The Vera C. Rubin Observatory in Chile releases the first light images from its new 8.4-meter (28 ft) telescope." resource-id="org.wikipedia.alpha:id/horizontal_scroll_list_item_text" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[117,938][561,1235]" displayed="true" />
                                      </android.widget.LinearLayout>
                                    </android.widget.FrameLayout>
                                    <android.widget.FrameLayout index="1" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[633,611][1059,1235]" displayed="true">
                                      <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[633,611][1059,1235]" displayed="true">
                                        <android.widget.ImageView index="0" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/horizontal_scroll_list_item_image" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[633,611][1059,899]" displayed="true" />
                                        <android.widget.TextView index="1" package="org.wikipedia.alpha" class="android.widget.TextView" text="In basketball, the Oklahoma City Thunder defeat the Indiana Pacers to win the NBA Finals." resource-id="org.wikipedia.alpha:id/horizontal_scroll_list_item_text" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[681,938][1059,1235]" displayed="true" />
                                      </android.widget.LinearLayout>
                                    </android.widget.FrameLayout>
                                  </android.support.v7.widget.RecyclerView>
                                </android.widget.RelativeLayout>
                              </android.widget.FrameLayout>
                              <android.widget.FrameLayout index="2" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[21,1325][1059,2088]" displayed="true">
                                <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[21,1325][1059,2088]" displayed="true">
                                  <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" resource-id="org.wikipedia.alpha:id/view_featured_article_card_header" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[21,1325][1059,1469]" displayed="true">
                                    <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[21,1325][1059,1469]" displayed="true">
                                      <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[21,1325][734,1469]" displayed="true">
                                        <android.widget.ImageView index="0" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/view_card_header_image" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[69,1361][141,1433]" displayed="true" />
                                        <android.widget.TextView index="1" package="org.wikipedia.alpha" class="android.widget.TextView" text="Featured article" resource-id="org.wikipedia.alpha:id/view_card_header_title" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[165,1368][463,1425]" displayed="true" />
                                      </android.widget.LinearLayout>
                                      <android.widget.LinearLayout index="1" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[734,1337][1059,1457]" displayed="true">
                                        <android.widget.TextView index="0" package="org.wikipedia.alpha" class="android.widget.TextView" text="Jun 25, 2025" resource-id="org.wikipedia.alpha:id/view_card_header_subtitle" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[734,1372][939,1421]" displayed="true" />
                                        <android.widget.ImageView index="1" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" content-desc="More options" resource-id="org.wikipedia.alpha:id/view_list_card_header_menu" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[939,1337][1059,1457]" displayed="true" />
                                      </android.widget.LinearLayout>
                                    </android.widget.LinearLayout>
                                  </android.widget.FrameLayout>
                                  <android.widget.ImageView index="1" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/view_featured_article_card_image" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[21,1469][1059,2045]" displayed="true" />
                                  <android.view.View index="2" package="org.wikipedia.alpha" class="android.view.View" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[21,2045][1059,2048]" displayed="true" />
                                  <android.widget.LinearLayout index="3" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" resource-id="org.wikipedia.alpha:id/view_featured_article_card_text_container" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[21,2048][1059,2088]" displayed="true">
                                    <android.widget.TextView index="0" package="org.wikipedia.alpha" class="android.widget.TextView" text="History of education in Wales (1701–1870)" resource-id="org.wikipedia.alpha:id/view_featured_article_card_article_title" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[69,2048][1011,2088]" displayed="true" />
                                  </android.widget.LinearLayout>
                                </android.widget.LinearLayout>
                              </android.widget.FrameLayout>
                            </android.support.v7.widget.RecyclerView>
                            <android.view.View index="1" package="org.wikipedia.alpha" class="android.view.View" text="" resource-id="org.wikipedia.alpha:id/fragment_feed_header" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,248][1080,536]" displayed="true" />
                          </android.widget.FrameLayout>
                        </android.view.ViewGroup>
                      </android.support.v4.view.ViewPager>
                    </android.view.ViewGroup>
                    <android.widget.FrameLayout index="1" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" resource-id="org.wikipedia.alpha:id/fragment_main_nav_tab_layout" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,2088][1080,2176]" displayed="true">
                      <android.view.ViewGroup index="0" package="org.wikipedia.alpha" class="android.view.ViewGroup" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,2088][1080,2176]" displayed="true">
                        <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" content-desc="Explore" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,2088][504,2176]" displayed="true">
                          <android.view.ViewGroup index="0" package="org.wikipedia.alpha" class="android.view.ViewGroup" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[182,2181][321,2256]" displayed="true">
                            <android.widget.TextView index="0" package="org.wikipedia.alpha" class="android.widget.TextView" text="Explore" resource-id="org.wikipedia.alpha:id/largeLabel" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[182,2181][321,2238]" displayed="true" />
                          </android.view.ViewGroup>
                          <android.widget.ImageView index="1" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/icon" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[216,2112][288,2176]" displayed="true" />
                        </android.widget.FrameLayout>
                        <android.widget.FrameLayout index="1" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" content-desc="My lists" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[504,2088][696,2176]" displayed="true">
                          <android.view.ViewGroup index="0" package="org.wikipedia.alpha" class="android.view.ViewGroup" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[527,2181][672,2256]" displayed="true" />
                          <android.widget.ImageView index="1" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/icon" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[564,2136][636,2176]" displayed="true" />
                        </android.widget.FrameLayout>
                        <android.widget.FrameLayout index="2" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" content-desc="History" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[696,2088][888,2176]" displayed="true">
                          <android.view.ViewGroup index="0" package="org.wikipedia.alpha" class="android.view.ViewGroup" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[725,2181][859,2256]" displayed="true" />
                          <android.widget.ImageView index="1" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/icon" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[756,2136][828,2176]" displayed="true" />
                        </android.widget.FrameLayout>
                        <android.widget.FrameLayout index="3" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" content-desc="Nearby" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[888,2088][1080,2176]" displayed="true">
                          <android.view.ViewGroup index="0" package="org.wikipedia.alpha" class="android.view.ViewGroup" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[917,2181][1050,2256]" displayed="true" />
                          <android.widget.ImageView index="1" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/icon" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[948,2136][1020,2176]" displayed="true" />
                        </android.widget.FrameLayout>
                      </android.view.ViewGroup>
                    </android.widget.FrameLayout>
                  </android.widget.LinearLayout>
                </android.widget.FrameLayout>
              </android.widget.FrameLayout>
              <android.view.ViewGroup index="1" package="org.wikipedia.alpha" class="android.view.ViewGroup" text="" resource-id="org.wikipedia.alpha:id/single_fragment_toolbar" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,80][1080,248]" displayed="true">
                <android.widget.ImageView index="0" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/single_fragment_toolbar_wordmark" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[48,119][348,209]" displayed="true" />
                <android.support.v7.widget.LinearLayoutCompat index="1" package="org.wikipedia.alpha" class="android.support.v7.widget.LinearLayoutCompat" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[936,80][1080,248]" displayed="true">
                  <android.widget.TextView index="0" package="org.wikipedia.alpha" class="android.widget.TextView" text="" content-desc="More options" resource-id="org.wikipedia.alpha:id/menu_overflow_button" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="true" password="false" scrollable="false" selected="false" bounds="[936,92][1080,236]" displayed="true" />
                </android.support.v7.widget.LinearLayoutCompat>
              </android.view.ViewGroup>
            </android.widget.FrameLayout>
          </android.widget.FrameLayout>
        </android.widget.FrameLayout>
      </android.widget.FrameLayout>
    </android.widget.LinearLayout>
    <android.view.View index="1" package="org.wikipedia.alpha" class="android.view.View" text="" resource-id="android:id/statusBarBackground" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,0][1080,80]" displayed="true" />
  </android.widget.FrameLayout>
</hierarchy>
<?xml version='1.0' encoding='UTF-8' standalone='yes' ?>
<hierarchy index="0" class="hierarchy" rotation="0" width="1440" height="2934">
  <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,0][1440,2934]" displayed="true">
    <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,0][1440,2934]" displayed="true">
      <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,145][1440,2934]" displayed="true">
        <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" resource-id="org.wikipedia.alpha:id/action_bar_root" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,145][1440,2934]" displayed="true">
          <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" resource-id="android:id/content" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,145][1440,2934]" displayed="true">
            <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,145][1440,2934]" displayed="true">
              <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" resource-id="org.wikipedia.alpha:id/fragment_container" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,145][1440,2934]" displayed="true">
                <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" resource-id="org.wikipedia.alpha:id/fragment_main_container" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,145][1440,2934]" displayed="true">
                  <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,341][1440,2934]" displayed="true">
                    <android.view.ViewGroup index="0" package="org.wikipedia.alpha" class="android.view.ViewGroup" text="" resource-id="org.wikipedia.alpha:id/fragment_main_coordinator" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,341][1440,2868]" displayed="true">
                      <android.support.v4.view.ViewPager index="0" package="org.wikipedia.alpha" class="android.support.v4.view.ViewPager" text="" resource-id="org.wikipedia.alpha:id/fragment_main_view_pager" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="true" selected="false" bounds="[0,341][1440,2868]" displayed="true">
                        <android.view.ViewGroup index="0" package="org.wikipedia.alpha" class="android.view.ViewGroup" text="" resource-id="org.wikipedia.alpha:id/feed_swipe_refresh_layout" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,341][1440,2868]" displayed="true">
                          <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="true" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,341][1440,2868]" displayed="true">
                            <android.support.v7.widget.RecyclerView index="0" package="org.wikipedia.alpha" class="android.support.v7.widget.RecyclerView" text="" resource-id="org.wikipedia.alpha:id/fragment_feed_feed" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="true" selected="false" bounds="[0,341][1440,2868]" displayed="true">
                              <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[25,394][1415,562]" displayed="true">
                                <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" resource-id="org.wikipedia.alpha:id/search_container" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[25,394][1415,562]" displayed="true">
                                  <android.widget.ImageView index="0" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" content-desc="Search Wikipedia" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[25,394][193,562]" displayed="true" />
                                  <android.widget.TextView index="1" package="org.wikipedia.alpha" class="android.widget.TextView" text="Search Wikipedia" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[193,445][1247,511]" displayed="true" />
                                  <android.widget.ImageView index="2" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" content-desc="Search Wikipedia" resource-id="org.wikipedia.alpha:id/voice_search_button" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="true" password="false" scrollable="false" selected="false" bounds="[1247,394][1415,562]" displayed="true" />
                                </android.widget.LinearLayout>
                              </android.widget.FrameLayout>
                              <android.widget.FrameLayout index="1" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[25,598][1415,1564]" displayed="true">
                                <android.widget.RelativeLayout index="0" package="org.wikipedia.alpha" class="android.widget.RelativeLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[25,598][1415,1564]" displayed="true">
                                  <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" resource-id="org.wikipedia.alpha:id/view_list_card_header" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[25,598][1415,766]" displayed="true">
                                    <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[25,598][1415,766]" displayed="true">
                                      <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[25,598][1034,766]" displayed="true">
                                        <android.widget.ImageView index="0" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/view_card_header_image" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[81,640][165,724]" displayed="true" />
                                        <android.widget.TextView index="1" package="org.wikipedia.alpha" class="android.widget.TextView" text="In the news" resource-id="org.wikipedia.alpha:id/view_card_header_title" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[193,649][441,715]" displayed="true" />
                                      </android.widget.LinearLayout>
                                      <android.widget.LinearLayout index="1" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1034,612][1415,752]" displayed="true">
                                        <android.widget.TextView index="0" package="org.wikipedia.alpha" class="android.widget.TextView" text="Jun 25, 2025" resource-id="org.wikipedia.alpha:id/view_card_header_subtitle" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1034,653][1275,710]" displayed="true" />
                                        <android.widget.ImageView index="1" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" content-desc="More options" resource-id="org.wikipedia.alpha:id/view_list_card_header_menu" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1275,612][1415,752]" displayed="true" />
                                      </android.widget.LinearLayout>
                                    </android.widget.LinearLayout>
                                  </android.widget.FrameLayout>
                                  <android.support.v7.widget.RecyclerView index="1" package="org.wikipedia.alpha" class="android.support.v7.widget.RecyclerView" text="" resource-id="org.wikipedia.alpha:id/view_list_card_list" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="true" selected="false" bounds="[25,766][1415,1564]" displayed="true">
                                    <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[81,766][711,1494]" displayed="true">
                                      <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[81,766][711,1494]" displayed="true">
                                        <android.widget.ImageView index="0" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/horizontal_scroll_list_item_image" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[81,766][711,1102]" displayed="true" />
                                        <android.widget.TextView index="1" package="org.wikipedia.alpha" class="android.widget.TextView" text="The Vera C. Rubin Observatory in Chile releases the first light images from its new 8.4-meter (28 ft) telescope." resource-id="org.wikipedia.alpha:id/horizontal_scroll_list_item_text" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[137,1148][655,1494]" displayed="true" />
                                      </android.widget.LinearLayout>
                                    </android.widget.FrameLayout>
                                    <android.widget.FrameLayout index="1" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[739,766][1369,1494]" displayed="true">
                                      <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[739,766][1369,1494]" displayed="true">
                                        <android.widget.ImageView index="0" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/horizontal_scroll_list_item_image" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[739,766][1369,1102]" displayed="true" />
                                        <android.widget.TextView index="1" package="org.wikipedia.alpha" class="android.widget.TextView" text="In basketball, the Oklahoma City Thunder defeat the Indiana Pacers to win the NBA Finals." resource-id="org.wikipedia.alpha:id/horizontal_scroll_list_item_text" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[795,1148][1313,1494]" displayed="true" />
                                      </android.widget.LinearLayout>
                                    </android.widget.FrameLayout>
                                    <android.widget.FrameLayout index="2" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1397,766][1415,1494]" displayed="true">
                                      <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1397,766][1415,1494]" displayed="true">
                                        <android.widget.ImageView index="0" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/horizontal_scroll_list_item_image" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1397,766][1415,1102]" displayed="true" />
                                      </android.widget.LinearLayout>
                                    </android.widget.FrameLayout>
                                  </android.support.v7.widget.RecyclerView>
                                </android.widget.RelativeLayout>
                              </android.widget.FrameLayout>
                              <android.widget.FrameLayout index="2" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[25,1600][1415,2868]" displayed="true">
                                <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[25,1600][1415,2868]" displayed="true">
                                  <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" resource-id="org.wikipedia.alpha:id/view_featured_article_card_header" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[25,1600][1415,1768]" displayed="true">
                                    <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[25,1600][1415,1768]" displayed="true">
                                      <android.widget.LinearLayout index="0" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[25,1600][1034,1768]" displayed="true">
                                        <android.widget.ImageView index="0" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/view_card_header_image" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[81,1642][165,1726]" displayed="true" />
                                        <android.widget.TextView index="1" package="org.wikipedia.alpha" class="android.widget.TextView" text="Featured article" resource-id="org.wikipedia.alpha:id/view_card_header_title" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[193,1651][534,1717]" displayed="true" />
                                      </android.widget.LinearLayout>
                                      <android.widget.LinearLayout index="1" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1034,1614][1415,1754]" displayed="true">
                                        <android.widget.TextView index="0" package="org.wikipedia.alpha" class="android.widget.TextView" text="Jun 25, 2025" resource-id="org.wikipedia.alpha:id/view_card_header_subtitle" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1034,1655][1275,1712]" displayed="true" />
                                        <android.widget.ImageView index="1" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" content-desc="More options" resource-id="org.wikipedia.alpha:id/view_list_card_header_menu" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1275,1614][1415,1754]" displayed="true" />
                                      </android.widget.LinearLayout>
                                    </android.widget.LinearLayout>
                                  </android.widget.FrameLayout>
                                  <android.widget.ImageView index="1" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/view_featured_article_card_image" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[25,1768][1415,2440]" displayed="true" />
                                  <android.view.View index="2" package="org.wikipedia.alpha" class="android.view.View" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[25,2440][1415,2444]" displayed="true" />
                                  <android.widget.LinearLayout index="3" package="org.wikipedia.alpha" class="android.widget.LinearLayout" text="" resource-id="org.wikipedia.alpha:id/view_featured_article_card_text_container" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[25,2444][1415,2868]" displayed="true">
                                    <android.widget.TextView index="0" package="org.wikipedia.alpha" class="android.widget.TextView" text="History of education in Wales (1701–1870)" resource-id="org.wikipedia.alpha:id/view_featured_article_card_article_title" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[81,2444][1359,2698]" displayed="true" />
                                    <android.widget.TextView index="1" package="org.wikipedia.alpha" class="android.widget.TextView" text="The period between 1701 and the 1870 Elementary Education Act saw an expansion in access to formal education in Wales, though schooling was not yet universal." resource-id="org.wikipedia.alpha:id/view_featured_article_card_extract" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[81,2698][1359,2868]" displayed="true" />
                                  </android.widget.LinearLayout>
                                </android.widget.LinearLayout>
                              </android.widget.FrameLayout>
                            </android.support.v7.widget.RecyclerView>
                            <android.view.View index="1" package="org.wikipedia.alpha" class="android.view.View" text="" resource-id="org.wikipedia.alpha:id/fragment_feed_header" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,341][1440,677]" displayed="true" />
                          </android.widget.FrameLayout>
                        </android.view.ViewGroup>
                      </android.support.v4.view.ViewPager>
                    </android.view.ViewGroup>
                    <android.widget.FrameLayout index="1" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" resource-id="org.wikipedia.alpha:id/fragment_main_nav_tab_layout" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,2868][1440,2934]" displayed="true">
                      <android.view.ViewGroup index="0" package="org.wikipedia.alpha" class="android.view.ViewGroup" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,2868][1440,2934]" displayed="true">
                        <android.widget.FrameLayout index="0" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" content-desc="Explore" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,2868][588,2934]" displayed="true">
                          <android.view.ViewGroup index="0" package="org.wikipedia.alpha" class="android.view.ViewGroup" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[212,2977][375,3064]" displayed="true">
                            <android.widget.TextView index="0" package="org.wikipedia.alpha" class="android.widget.TextView" text="Explore" resource-id="org.wikipedia.alpha:id/largeLabel" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[212,2977][375,3043]" displayed="true" />
                          </android.view.ViewGroup>
                          <android.widget.ImageView index="1" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/icon" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[252,2896][336,2934]" displayed="true" />
                        </android.widget.FrameLayout>
                        <android.widget.FrameLayout index="1" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" content-desc="My lists" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[588,2868][872,2934]" displayed="true">
                          <android.view.ViewGroup index="0" package="org.wikipedia.alpha" class="android.view.ViewGroup" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[646,2977][814,3064]" displayed="true" />
                          <android.widget.ImageView index="1" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/icon" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[688,2924][772,2934]" displayed="true" />
                        </android.widget.FrameLayout>
                        <android.widget.FrameLayout index="2" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" content-desc="History" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[872,2868][1156,2934]" displayed="true">
                          <android.view.ViewGroup index="0" package="org.wikipedia.alpha" class="android.view.ViewGroup" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[936,2977][1092,3064]" displayed="true" />
                          <android.widget.ImageView index="1" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/icon" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[972,2924][1056,2934]" displayed="true" />
                        </android.widget.FrameLayout>
                        <android.widget.FrameLayout index="3" package="org.wikipedia.alpha" class="android.widget.FrameLayout" text="" content-desc="Nearby" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1156,2868][1440,2934]" displayed="true">
                          <android.view.ViewGroup index="0" package="org.wikipedia.alpha" class="android.view.ViewGroup" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1220,2977][1376,3064]" displayed="true" />
                          <android.widget.ImageView index="1" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/icon" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1256,2924][1340,2934]" displayed="true" />
                        </android.widget.FrameLayout>
                      </android.view.ViewGroup>
                    </android.widget.FrameLayout>
                  </android.widget.LinearLayout>
                </android.widget.FrameLayout>
              </android.widget.FrameLayout>
              <android.view.ViewGroup index="1" package="org.wikipedia.alpha" class="android.view.ViewGroup" text="" resource-id="org.wikipedia.alpha:id/single_fragment_toolbar" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,145][1440,341]" displayed="true">
                <android.widget.ImageView index="0" package="org.wikipedia.alpha" class="android.widget.ImageView" text="" resource-id="org.wikipedia.alpha:id/single_fragment_toolbar_wordmark" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[56,191][406,295]" displayed="true" />
                <android.support.v7.widget.LinearLayoutCompat index="1" package="org.wikipedia.alpha" class="android.support.v7.widget.LinearLayoutCompat" text="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[1272,145][1440,341]" displayed="true">
                  <android.widget.TextView index="0" package="org.wikipedia.alpha" class="android.widget.TextView" text="" content-desc="More options" resource-id="org.wikipedia.alpha:id/menu_overflow_button" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" long-clickable="true" password="false" scrollable="false" selected="false" bounds="[1272,159][1440,327]" displayed="true" />
                </android.support.v7.widget.LinearLayoutCompat>
              </android.view.ViewGroup>
            </android.widget.FrameLayout>
          </android.widget.FrameLayout>
        </android.widget.FrameLayout>
      </android.widget.FrameLayout>
    </android.widget.LinearLayout>
    <android.view.View index="2" package="org.wikipedia.alpha" class="android.view.View" text="" resource-id="android:id/navigationBarBackground" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" long-clickable="false" password="false" scrollable="false" selected="false" bounds="[0,3064][1440,3120]" displayed="true" />
  </android.widget.FrameLayout>
</hierarchy>
