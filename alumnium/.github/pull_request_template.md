# Pull Request Template

Thank you for your contribution to Alumnium!
Please fill out this template to help us understand and review your PR efficiently.

## Summary
What is this pull request about?
Provide a brief description of what the PR does, the problem it solves, and any context around the implementation.

## Motivation
Why is this change being made?
Describe the use case, problem, or opportunity that this PR addresses.
Explain how this change contributes to the project's goals or improves functionality.

## Related Issue(s)
If this PR fixes or is related to an open issue, link it here.
Example: Closes #123

Closes #

## Type of Change
Mark the relevant options.

- [ ] Bug fix  
- [ ] New feature (non-breaking changes, test coverage, refactoring)
- [ ] Breaking change (fix, refactoring, or feature that would cause existing functionality to change)
- [ ] Cleanup (documentation, formatting)

## Code or UI Demos (if applicable)
Add screenshots, logs, terminal output, or code snippets that help reviewers understand the change.
Useful especially for UI changes or error fixes.

## Checklist
Please verify the following before submitting:

- [ ] I have read the [Contributing Guidelines](https://github.com/alumnium-hq/alumnium/blob/main/CONTRIBUTING.md)
- [ ] I have added or updated relevant documentation
- [ ] I have written or updated necessary tests
- [ ] I have tested the changes locally
- [ ] The changes follow the project's coding style and structure
- [ ] I have not included any sensitive or credential-related information
- [ ] I have ensured these changes maintain or improve accessibility (for UI changes)
- [ ] I have considered security implications of these changes

## Additional Notes
Anything else the reviewer should be aware of?
For example: limitations, discussions to follow up on, or next steps.
