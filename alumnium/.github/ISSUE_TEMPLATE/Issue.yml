name: 🐛 Issue Report
description: Create an Issue to help us improve.
title: "[Bug]:"
body:
  - type: markdown
    attributes:
      value: |
        Thanks for taking the time to fill out this New Issue! First, let's get some information about the environment you are using:

  - type: input
    id: Alumnium-version
    attributes:
      label: Alumnium Version
      description: What version of Alumnium are you running?
      placeholder: latest
    validations:
      required: true

  - type: input
    id: Python-version
    attributes:
      label: Python Version
      description: What version of Python are you using?
      placeholder: latest
    validations:
      required: true

  - type: dropdown
    id: tool-choice
    attributes:
      label: Which tool are you using Selenium or Playwright?
      options :
        - Selenium
        - Playwright
    validations:
        required: true

  - type: input
    id: tool-version
    attributes :
      label: Selenium or Playwright Version
      description: What version of Playwright or Selenium you are using currently?

  - type: dropdown
    id: Browser-choice
    attributes:
      label: Which Browser are you using?
      options :
        - Chrome
        - Firefox
        - Edge
        - Safari
        - other(Description)
    validations:
        required: true

  - type: input
    id: Browser-version
    attributes:
      label: What is the browser version used?
      description: The browser version on which the test ran

  - type: dropdown
    id: LLM-Version
    attributes:
      label: Which LLM are you using?
      options:
        - <PERSON> 3 Haiku (Anthropic)
        - DeepSeek V3 (DeepSeek)
        - GPT-4.0 Mini (Open AI)
        - Gemini 2.0 Flash (Google)
        - Llama 4.0 Maverick (Meta)
        - Mistral Medium 3 (MistralAI)
        - Mistral Small 3.1 24B (Ollama)
        - Other
      default: 3

  - type: textarea
    attributes:
      placeholder: Can you please provide the HTML DOM of the page or URL of the page where the test is failing
      label: HTML DOM or URL
    validations:
      required: true

  - type: textarea
    attributes:
      placeholder: Can you please provide the issue which you are facing?
      label: Full Issue Description
    validations:
      required: true

  - type: textarea
    attributes:
      placeholder: Can you please provide the steps to reproduce?
      label: Detailed outline of steps needed to reproduce the issue
    validations:
      required: true
