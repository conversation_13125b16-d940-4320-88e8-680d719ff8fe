name: CI
on:
  pull_request_target:
  push:
    branches:
      - main
  workflow_dispatch:

concurrency:
  group: ci-${{ github.event.pull_request.head.ref || github.ref }}
  cancel-in-progress: true

jobs:
  test:
    name: Test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha || github.sha }}
      - uses: actions/setup-python@v5
        with:
          python-version-file: .tool-versions
      - run: pip install poetry==$(cat .tool-versions | grep poetry | cut -d" " -f2)
      - uses: actions/cache@v4
        with:
          path: ./.venv
          key: venv-${{ hashFiles('poetry.lock') }}
      - run: poetry install
      - run: make check-format
      - run: poetry run pytest tests/
      - if: failure() && runner.debug == '1'
        uses: mxschmitt/action-tmate@v3

  examples:
    name: Examples
    runs-on: ${{ matrix.driver == 'appium' && 'macos-latest' || 'ubuntu-latest' }}
    timeout-minutes: 60
    env:
      ALUMNIUM_DRIVER: ${{ matrix.driver }}
      ALUMNIUM_MODEL: ${{ matrix.model }}
      ALUMNIUM_LOG_LEVEL: debug
      ALUMNIUM_LOG_PATH: alumnium.log
      ALUMNIUM_PLAYWRIGHT_HEADLESS: false
      DISPLAY: :99
      ANTHROPIC_API_KEY: ${{ secrets.ANTHROPIC_API_KEY }}
      AWS_ACCESS_KEY: ${{ secrets.AWS_ACCESS_KEY }}
      AWS_SECRET_KEY: ${{ secrets.AWS_SECRET_KEY }}
      AZURE_OPENAI_API_KEY: ${{ secrets.AZURE_OPENAI_API_KEY }}
      AZURE_OPENAI_API_VERSION: ${{ secrets.AZURE_OPENAI_API_VERSION }}
      AZURE_OPENAI_ENDPOINT: ${{ secrets.AZURE_OPENAI_ENDPOINT }}
      DEEPSEEK_API_KEY: ${{ secrets.DEEPSEEK_API_KEY }}
      GOOGLE_API_KEY: ${{ secrets.GOOGLE_API_KEY }}
      MISTRAL_API_KEY: ${{ secrets.MISTRAL_API_KEY }}
      OPENAI_API_KEY: ${{ secrets.OPENAI_API_KEY }}
    strategy:
      fail-fast: false
      matrix:
        driver:
          - appium
          - playwright
          - selenium
        model:
          - anthropic
          - aws_anthropic
          - aws_meta
          - azure_openai
          # Official DeepSeek platform times out!
          # - deepseek
          - google
          - mistralai
          - openai
    steps:
      - id: check-access
        uses: actions-cool/check-user-permission@v2
        with:
          require: write
          username: ${{ github.triggering_actor }}
      - if: steps.check-access.outputs.require-result == 'false'
        run: |
          echo "${{ github.triggering_actor }} does not have permissions on this repo - maintainer will re-run this job."
          echo "Current permission level is ${{ steps.check-access.outputs.user-permission }}."
          echo "Job originally triggered by ${{ github.actor }}."
          exit 1
      - uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha || github.sha }}
      - uses: actions/setup-python@v5
        with:
          python-version-file: .tool-versions
      - run: pip install poetry==$(cat .tool-versions | grep poetry | cut -d" " -f2)
      - uses: actions/cache@v4
        with:
          path: ./.venv
          key: venv-${{ hashFiles('poetry.lock') }}
      - run: poetry install
      - if: matrix.driver == 'playwright'
        run: poetry run playwright install chromium
      - if: matrix.driver == 'appium'
        run: |
          npm install -g appium
          appium driver install xcuitest
          appium &
      - if: matrix.driver != 'appium'
        run: Xvfb ${{ env.DISPLAY }} -screen 0 1600x1200x24 &
      - uses: actions/cache@v4
        with:
          path: .alumnium-cache.sqlite
          key: alumnium-cache-${{ matrix.driver }}-${{ matrix.model }}-${{ github.sha }}
          restore-keys: alumnium-cache-${{ matrix.driver }}-${{ matrix.model }}-
      - run: poetry run behave --format html-pretty --outfile reports/behave.html --format pretty ${{ matrix.driver == 'appium' && '--tags ~@web' || '' }}
      - run: poetry run pytest --retries 1 --html reports/pytest.html examples/
      - if: failure() && runner.debug == '1'
        uses: mxschmitt/action-tmate@v3
      - if: always()
        uses: actions/upload-artifact@v4
        with:
          name: ${{ matrix.driver }}-${{ matrix.model }}
          path: |
            *.log
            reports/

  release:
    name: Release
    needs:
      - test
      - examples
    if: github.ref_name == 'main' && startsWith(github.event.head_commit.message, 'release:')
    uses: ./.github/workflows/release.yml
    permissions:
      attestations: write
      contents: write
      id-token: write
