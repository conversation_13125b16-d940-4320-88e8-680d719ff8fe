from .back_tool import BackTool
from .base_tool import BaseTool
from .click_tool import <PERSON><PERSON><PERSON><PERSON>
from .drag_and_drop_tool import Drag<PERSON>ndDropTool
from .hover_tool import HoverTool
from .press_key_tool import PressKeyTool
from .select_tool import SelectTool
from .type_tool import TypeTool

ALL_TOOLS = {
    "ClickTool": C<PERSON>Tool,
    "DragAndDropTool": DragAndDropTool,
    "HoverTool": HoverTool,
    "PressKeyTool": PressKeyTool,
    "SelectTool": SelectTool,
    "TypeTool": TypeTool,
    "BackTool": BackTool,
}

ALL_APPIUM_TOOLS = {
    "ClickTool": ClickTool,
    "DragAndDropTool": DragAndDropTool,
    "PressKeyTool": <PERSON>KeyTool,
    "SelectTool": SelectTool,
    "TypeTool": TypeTool,
    "BackTool": BackTool,
}
