#!/usr/bin/env python3
"""
Script to reset environment variables for both iOS and Android apps.
Deletes all existing environments and creates new AU and NZ environments
from the provided JSON files.
"""

import json
import sys
import os
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def load_json_environment(file_path):
    """Load environment variables from JSON file"""
    try:
        with open(file_path, 'r') as f:
            data = json.load(f)
        
        # Handle different JSON structures
        if 'data' in data and 'variables' in data['data']:
            # Structure from environment_undefined_2025-08-13.json
            variables = data['data']['variables']
            name = data['data'].get('name', 'Unknown')
        elif 'variables' in data:
            # Structure from environment_NZ-PROD-IP14_2025-08-08.json
            variables = data['variables']
            name = data.get('name', 'Unknown')
        else:
            logger.error(f"Unknown JSON structure in {file_path}")
            return None, []
        
        logger.info(f"Loaded {len(variables)} variables from {file_path} (original name: {name})")
        return name, variables
    except Exception as e:
        logger.error(f"Error loading JSON file {file_path}: {e}")
        return None, []

def reset_ios_environments():
    """Reset iOS environments and variables"""
    try:
        # Import iOS database handler
        sys.path.append('app/utils')
        from directory_paths_db import directory_paths_db as ios_db
        
        logger.info("Resetting iOS environments...")
        
        # Get all existing environments
        existing_envs = ios_db.get_all_environments()
        logger.info(f"Found {len(existing_envs)} existing iOS environments")
        
        # Delete all existing environments (this will cascade delete variables)
        for env in existing_envs:
            logger.info(f"Deleting iOS environment: {env['name']} (ID: {env['id']})")
            ios_db.delete_environment(env['id'])
        
        # Load AU environment data
        au_name, au_variables = load_json_environment('/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/environment_undefined_2025-08-13.json')
        if au_variables:
            # Create AU environment
            au_env_id = ios_db.create_environment('AU')
            if au_env_id:
                logger.info(f"Created iOS AU environment with ID: {au_env_id}")
                
                # Add variables to AU environment
                for var in au_variables:
                    name = var['name']
                    var_type = var.get('type', 'default')
                    initial_value = var.get('initial_value', '')
                    current_value = var.get('current_value', initial_value)
                    
                    var_id = ios_db.add_environment_variable(
                        au_env_id, name, var_type, initial_value, current_value
                    )
                    if var_id:
                        logger.debug(f"Added iOS AU variable: {name} = {current_value}")
                
                logger.info(f"Added {len(au_variables)} variables to iOS AU environment")
        
        # Load NZ environment data
        nz_name, nz_variables = load_json_environment('/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/environment_NZ-PROD-IP14_2025-08-08.json')
        if nz_variables:
            # Create NZ environment
            nz_env_id = ios_db.create_environment('NZ')
            if nz_env_id:
                logger.info(f"Created iOS NZ environment with ID: {nz_env_id}")
                
                # Add variables to NZ environment
                for var in nz_variables:
                    name = var['name']
                    var_type = var.get('type', 'default')
                    initial_value = var.get('initial_value', '')
                    current_value = var.get('current_value', initial_value)
                    
                    var_id = ios_db.add_environment_variable(
                        nz_env_id, name, var_type, initial_value, current_value
                    )
                    if var_id:
                        logger.debug(f"Added iOS NZ variable: {name} = {current_value}")
                
                logger.info(f"Added {len(nz_variables)} variables to iOS NZ environment")
        
        # Set AU as the active environment for iOS
        if au_env_id:
            ios_db.set_active_environment(au_env_id)
            logger.info("Set AU as active iOS environment")
        
        logger.info("✅ iOS environment reset completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error resetting iOS environments: {e}")
        return False

def reset_android_environments():
    """Reset Android environments and variables"""
    try:
        # Import Android database handler
        sys.path.append('app_android/utils')
        from directory_paths_db import directory_paths_db as android_db
        
        logger.info("Resetting Android environments...")
        
        # Get all existing environments
        existing_envs = android_db.get_all_environments()
        logger.info(f"Found {len(existing_envs)} existing Android environments")
        
        # Delete all existing environments (this will cascade delete variables)
        for env in existing_envs:
            logger.info(f"Deleting Android environment: {env['name']} (ID: {env['id']})")
            android_db.delete_environment(env['id'])
        
        # Load AU environment data
        au_name, au_variables = load_json_environment('/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/environment_undefined_2025-08-13.json')
        if au_variables:
            # Create AU environment
            au_env_id = android_db.create_environment('AU')
            if au_env_id:
                logger.info(f"Created Android AU environment with ID: {au_env_id}")
                
                # Add variables to AU environment
                for var in au_variables:
                    name = var['name']
                    var_type = var.get('type', 'default')
                    initial_value = var.get('initial_value', '')
                    current_value = var.get('current_value', initial_value)
                    
                    var_id = android_db.add_environment_variable(
                        au_env_id, name, var_type, initial_value, current_value
                    )
                    if var_id:
                        logger.debug(f"Added Android AU variable: {name} = {current_value}")
                
                logger.info(f"Added {len(au_variables)} variables to Android AU environment")
        
        # Load NZ environment data
        nz_name, nz_variables = load_json_environment('/Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/environment_NZ-PROD-IP14_2025-08-08.json')
        if nz_variables:
            # Create NZ environment
            nz_env_id = android_db.create_environment('NZ')
            if nz_env_id:
                logger.info(f"Created Android NZ environment with ID: {nz_env_id}")
                
                # Add variables to NZ environment
                for var in nz_variables:
                    name = var['name']
                    var_type = var.get('type', 'default')
                    initial_value = var.get('initial_value', '')
                    current_value = var.get('current_value', initial_value)
                    
                    var_id = android_db.add_environment_variable(
                        nz_env_id, name, var_type, initial_value, current_value
                    )
                    if var_id:
                        logger.debug(f"Added Android NZ variable: {name} = {current_value}")
                
                logger.info(f"Added {len(nz_variables)} variables to Android NZ environment")
        
        # Set AU as the active environment for Android
        if au_env_id:
            android_db.set_active_environment(au_env_id)
            logger.info("Set AU as active Android environment")
        
        logger.info("✅ Android environment reset completed successfully")
        return True
        
    except Exception as e:
        logger.error(f"Error resetting Android environments: {e}")
        return False

def main():
    """Main function to reset both iOS and Android environments"""
    print("🚀 Starting environment reset for both iOS and Android...")
    logger.info("🚀 Starting environment reset for both iOS and Android...")

    # Reset iOS environments
    print("Resetting iOS environments...")
    ios_success = reset_ios_environments()
    print(f"iOS reset result: {ios_success}")

    # Reset Android environments
    print("Resetting Android environments...")
    android_success = reset_android_environments()
    print(f"Android reset result: {android_success}")

    if ios_success and android_success:
        print("🎉 Environment reset completed successfully for both platforms!")
        logger.info("🎉 Environment reset completed successfully for both platforms!")
        logger.info("📋 Summary:")
        logger.info("   - Deleted all existing environments and variables")
        logger.info("   - Created 'AU' environment with variables from environment_undefined_2025-08-13.json")
        logger.info("   - Created 'NZ' environment with variables from environment_NZ-PROD-IP14_2025-08-08.json")
        logger.info("   - Set 'AU' as the active environment for both platforms")
        return True
    else:
        print("❌ Environment reset failed for one or both platforms")
        logger.error("❌ Environment reset failed for one or both platforms")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
