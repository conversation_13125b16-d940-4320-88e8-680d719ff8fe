#!/usr/bin/env python3
"""
Test User Setup Script for SaaS Platform
This script creates a test user with sample data for pre-deployment testing.
"""

import os
import sys
import uuid
from datetime import datetime, timedelta

# Add the current directory to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def setup_test_user():
    """Create a test user with sample test suites and test cases."""
    
    try:
        # Import after setting up the path
        from saas_unified_server import app, db, User, TestSuite, TestCase, DeviceAgent
        
        with app.app_context():
            print("Setting up test user and sample data...")
            
            # Create test user
            test_user_id = str(uuid.uuid4())
            test_tenant_id = str(uuid.uuid4())
            
            test_user = User(
                id=test_user_id,
                email='<EMAIL>',
                first_name='Test',
                last_name='User',
                tenant_id=test_tenant_id,
                is_admin=False,
                is_active=True,
                created_at=datetime.utcnow()
            )
            test_user.set_password('testpass123')
            
            # Check if test user already exists
            existing_user = User.query.filter_by(email='<EMAIL>').first()
            if existing_user:
                print("Test user already exists. Updating...")
                test_user_id = existing_user.id
                test_tenant_id = existing_user.tenant_id
            else:
                db.session.add(test_user)
                print("✓ Test user created: <EMAIL>")
            
            # Create sample test suites
            test_suites_data = [
                {
                    'name': 'Android Login Tests',
                    'description': 'Test suite for Android app login functionality',
                    'platform': 'android'
                },
                {
                    'name': 'iOS Navigation Tests',
                    'description': 'Test suite for iOS app navigation flows',
                    'platform': 'ios'
                },
                {
                    'name': 'Cross-Platform UI Tests',
                    'description': 'UI tests that work on both Android and iOS',
                    'platform': 'both'
                }
            ]
            
            created_suites = []
            for suite_data in test_suites_data:
                test_suite = TestSuite(
                    id=str(uuid.uuid4()),
                    user_id=test_user_id,
                    name=suite_data['name'],
                    description=suite_data['description'],
                    platform=suite_data['platform'],
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                db.session.add(test_suite)
                created_suites.append(test_suite)
                print(f"✓ Test suite created: {suite_data['name']}")
            
            # Create sample test cases
            test_cases_data = [
                # Android Login Tests
                {
                    'suite_index': 0,
                    'name': 'Valid Login Test',
                    'description': 'Test login with valid credentials',
                    'steps': [
                        {'action': 'launch_app', 'target': 'com.example.app'},
                        {'action': 'wait_for_element', 'target': 'id:username'},
                        {'action': 'input_text', 'target': 'id:username', 'value': 'testuser'},
                        {'action': 'input_text', 'target': 'id:password', 'value': 'testpass'},
                        {'action': 'click', 'target': 'id:login_button'},
                        {'action': 'wait_for_element', 'target': 'id:dashboard'},
                        {'action': 'assert_element_visible', 'target': 'id:dashboard'}
                    ]
                },
                {
                    'suite_index': 0,
                    'name': 'Invalid Login Test',
                    'description': 'Test login with invalid credentials',
                    'steps': [
                        {'action': 'launch_app', 'target': 'com.example.app'},
                        {'action': 'input_text', 'target': 'id:username', 'value': 'invalid'},
                        {'action': 'input_text', 'target': 'id:password', 'value': 'invalid'},
                        {'action': 'click', 'target': 'id:login_button'},
                        {'action': 'wait_for_element', 'target': 'id:error_message'},
                        {'action': 'assert_element_visible', 'target': 'id:error_message'}
                    ]
                },
                # iOS Navigation Tests
                {
                    'suite_index': 1,
                    'name': 'Main Menu Navigation',
                    'description': 'Test navigation through main menu items',
                    'steps': [
                        {'action': 'launch_app', 'target': 'com.example.iosapp'},
                        {'action': 'wait_for_element', 'target': 'accessibility_id:MainMenu'},
                        {'action': 'click', 'target': 'accessibility_id:ProfileTab'},
                        {'action': 'wait_for_element', 'target': 'accessibility_id:ProfileView'},
                        {'action': 'click', 'target': 'accessibility_id:SettingsTab'},
                        {'action': 'wait_for_element', 'target': 'accessibility_id:SettingsView'}
                    ]
                },
                # Cross-Platform UI Tests
                {
                    'suite_index': 2,
                    'name': 'Button Interaction Test',
                    'description': 'Test button interactions across platforms',
                    'steps': [
                        {'action': 'launch_app', 'target': 'auto_detect'},
                        {'action': 'wait_for_element', 'target': 'xpath://button[@text="Submit" or @label="Submit"]'},
                        {'action': 'click', 'target': 'xpath://button[@text="Submit" or @label="Submit"]'},
                        {'action': 'wait_for_element', 'target': 'xpath://*[contains(@text, "Success") or contains(@label, "Success")]'}
                    ]
                }
            ]
            
            for case_data in test_cases_data:
                test_case = TestCase(
                    id=str(uuid.uuid4()),
                    test_suite_id=created_suites[case_data['suite_index']].id,
                    name=case_data['name'],
                    description=case_data['description'],
                    steps=case_data['steps'],
                    created_at=datetime.utcnow(),
                    updated_at=datetime.utcnow()
                )
                db.session.add(test_case)
                print(f"✓ Test case created: {case_data['name']}")
            
            # Create sample device agents
            device_agents_data = [
                {
                    'agent_name': 'Android Test Device',
                    'platform': 'android',
                    'capabilities': {
                        'platformName': 'Android',
                        'platformVersion': '11.0',
                        'deviceName': 'Android Emulator',
                        'automationName': 'UiAutomator2'
                    }
                },
                {
                    'agent_name': 'iOS Test Device',
                    'platform': 'ios',
                    'capabilities': {
                        'platformName': 'iOS',
                        'platformVersion': '15.0',
                        'deviceName': 'iPhone Simulator',
                        'automationName': 'XCUITest'
                    }
                }
            ]
            
            for agent_data in device_agents_data:
                device_agent = DeviceAgent(
                    id=str(uuid.uuid4()),
                    user_id=test_user_id,
                    agent_name=agent_data['agent_name'],
                    agent_token=str(uuid.uuid4()),
                    platform=agent_data['platform'],
                    status='offline',
                    capabilities=agent_data['capabilities'],
                    created_at=datetime.utcnow()
                )
                db.session.add(device_agent)
                print(f"✓ Device agent created: {agent_data['agent_name']}")
            
            # Commit all changes
            db.session.commit()
            
            print("\n" + "="*50)
            print("TEST USER SETUP COMPLETED SUCCESSFULLY!")
            print("="*50)
            print(f"Test User Credentials:")
            print(f"  Email: <EMAIL>")
            print(f"  Password: testpass123")
            print(f"  User ID: {test_user_id}")
            print(f"  Tenant ID: {test_tenant_id}")
            print(f"\nCreated:")
            print(f"  - {len(test_suites_data)} test suites")
            print(f"  - {len(test_cases_data)} test cases")
            print(f"  - {len(device_agents_data)} device agents")
            print(f"\nYou can now test the platform with these credentials.")
            print("="*50)
            
    except ImportError as e:
        print(f"Error importing modules: {e}")
        print("Make sure the SaaS server is properly configured and dependencies are installed.")
        sys.exit(1)
    except Exception as e:
        print(f"Error setting up test user: {e}")
        sys.exit(1)

def create_demo_admin():
    """Create a demo admin user for testing admin features."""
    
    try:
        from saas_unified_server import app, db, User
        
        with app.app_context():
            print("Creating demo admin user...")
            
            demo_admin_id = str(uuid.uuid4())
            demo_tenant_id = str(uuid.uuid4())
            
            demo_admin = User(
                id=demo_admin_id,
                email='<EMAIL>',
                first_name='Demo',
                last_name='Admin',
                tenant_id=demo_tenant_id,
                is_admin=True,
                is_active=True,
                created_at=datetime.utcnow()
            )
            demo_admin.set_password('demoadmin123')
            
            # Check if demo admin already exists
            existing_admin = User.query.filter_by(email='<EMAIL>').first()
            if existing_admin:
                print("Demo admin already exists.")
            else:
                db.session.add(demo_admin)
                db.session.commit()
                print("✓ Demo admin created: <EMAIL>")
                print("  Password: demoadmin123")
            
    except Exception as e:
        print(f"Error creating demo admin: {e}")
        sys.exit(1)

def main():
    """Main function to set up test data."""
    print("SaaS Platform Test Data Setup")
    print("==============================")
    
    # Set Flask app environment
    os.environ.setdefault('FLASK_APP', 'saas_unified_server.py')
    
    # Setup test user and sample data
    setup_test_user()
    
    # Create demo admin
    create_demo_admin()
    
    print("\nTest data setup completed successfully!")
    print("You can now use the platform for testing with the created users.")

if __name__ == '__main__':
    main()