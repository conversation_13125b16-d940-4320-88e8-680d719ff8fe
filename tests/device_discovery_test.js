/**
 * Comprehensive Device Discovery and Connectivity Test Suite
 * Tests Android device discovery, connectivity, and UI functionality
 * using Playwright automation framework
 */

const { test, expect } = require('@playwright/test');
const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));
// Test configuration
const CONFIG = {
  ANDROID_BACKEND_URL: 'http://localhost:8083',
  IOS_BACKEND_URL: 'http://localhost:8088',
  SAAS_BACKEND_URL: 'http://localhost:5000',
  TIMEOUT: 30000,
  DEVICE_DISCOVERY_TIMEOUT: 10000
};

// Helper functions
class DeviceTestHelper {
  static async checkAdbDevices() {
    try {
      const { stdout } = await execAsync('adb devices -l');
      const lines = stdout.trim().split('\n').slice(1); // Skip header
      const devices = [];
      
      for (const line of lines) {
        if (line.includes('device') && !line.includes('offline')) {
          const parts = line.split('\t');
          if (parts.length >= 2) {
            devices.push({
              id: parts[0],
              status: parts[1],
              details: line
            });
          }
        }
      }
      return devices;
    } catch (error) {
      console.error('ADB command failed:', error.message);
      return [];
    }
  }

  static async waitForBackendReady(url, timeout = 30000) {
    const startTime = Date.now();
    while (Date.now() - startTime < timeout) {
      try {
        const response = await fetch(`${url}/api/health`);
        if (response.ok) return true;
      } catch (error) {
        // Backend not ready yet
      }
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    return false;
  }

  static async getDevicesFromAPI(baseUrl) {
    try {
      const response = await fetch(`${baseUrl}/api/devices`);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      const data = await response.json();
      return data.devices || [];
    } catch (error) {
      console.error(`Failed to get devices from ${baseUrl}:`, error.message);
      return [];
    }
  }

  static async connectToDevice(page, deviceId) {
    await page.click(`[data-device-id="${deviceId}"]`);
    await page.click('button:has-text("Connect")');
    await page.waitForSelector('.device-connected', { timeout: 15000 });
  }
}

// Test Suite: Backend Health Checks
test.describe('Backend Health and Connectivity', () => {
  test('Android backend should be running and responsive', async () => {
    const isReady = await DeviceTestHelper.waitForBackendReady(CONFIG.ANDROID_BACKEND_URL);
    expect(isReady).toBe(true);
    
    const response = await fetch(`${CONFIG.ANDROID_BACKEND_URL}/api/health`);
    expect(response.ok).toBe(true);
  });

  test('iOS backend should be running and responsive', async () => {
    const isReady = await DeviceTestHelper.waitForBackendReady(CONFIG.IOS_BACKEND_URL);
    expect(isReady).toBe(true);
  });

  test('SaaS backend should be running and responsive', async () => {
    const isReady = await DeviceTestHelper.waitForBackendReady(CONFIG.SAAS_BACKEND_URL);
    expect(isReady).toBe(true);
  });
});

// Test Suite: ADB and Device Discovery
test.describe('ADB and Device Discovery', () => {
  test('ADB should detect connected Android devices', async () => {
    const devices = await DeviceTestHelper.checkAdbDevices();
    console.log('ADB detected devices:', devices);
    
    expect(devices.length).toBeGreaterThan(0);
    
    // Verify device properties
    for (const device of devices) {
      expect(device.id).toBeTruthy();
      expect(device.status).toBe('device');
      expect(device.details).toContain('device');
    }
  });

  test('Android backend API should return discovered devices', async () => {
    const devices = await DeviceTestHelper.getDevicesFromAPI(CONFIG.ANDROID_BACKEND_URL);
    console.log('Android API devices:', devices);
    
    expect(devices.length).toBeGreaterThan(0);
    
    // Verify device structure
    for (const device of devices) {
      expect(device).toHaveProperty('id');
      expect(device).toHaveProperty('name');
      expect(device).toHaveProperty('platform', 'Android');
      expect(device).toHaveProperty('status', 'Online');
      expect(device).toHaveProperty('udid');
      expect(device).toHaveProperty('osVersion');
    }
  });

  test('Device properties should be correctly retrieved', async () => {
    const adbDevices = await DeviceTestHelper.checkAdbDevices();
    const apiDevices = await DeviceTestHelper.getDevicesFromAPI(CONFIG.ANDROID_BACKEND_URL);
    
    expect(adbDevices.length).toBe(apiDevices.length);
    
    // Cross-verify device IDs
    const adbDeviceIds = adbDevices.map(d => d.id);
    const apiDeviceIds = apiDevices.map(d => d.id);
    
    for (const adbId of adbDeviceIds) {
      expect(apiDeviceIds).toContain(adbId);
    }
  });
});

// Test Suite: Web UI Device Discovery
test.describe('Web UI Device Discovery', () => {
  test('Android backend UI should display connected devices', async ({ page }) => {
    await page.goto(CONFIG.ANDROID_BACKEND_URL);
    await page.waitForLoadState('networkidle');
    
    // Wait for device discovery
    await page.waitForTimeout(3000);
    
    // Check if devices are displayed
    const deviceElements = await page.locator('[data-testid="device-item"], .device-item, .device-card').count();
    expect(deviceElements).toBeGreaterThan(0);
    
    // Verify device information is displayed
    const firstDevice = page.locator('[data-testid="device-item"], .device-item, .device-card').first();
    await expect(firstDevice).toBeVisible();
    
    // Check for device details
    const deviceText = await firstDevice.textContent();
    expect(deviceText).toMatch(/Android|RMX2151|realme/i);
  });

  test('Device selection should work correctly', async ({ page }) => {
    await page.goto(CONFIG.ANDROID_BACKEND_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Find and click on a device
    const deviceSelector = '[data-testid="device-item"], .device-item, .device-card, .device-list-item';
    await page.waitForSelector(deviceSelector, { timeout: 10000 });
    
    const firstDevice = page.locator(deviceSelector).first();
    await firstDevice.click();
    
    // Verify device is selected
    await expect(firstDevice).toHaveClass(/selected|active/);
  });

  test('Connect button should be enabled for selected device', async ({ page }) => {
    await page.goto(CONFIG.ANDROID_BACKEND_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Select a device
    const deviceSelector = '[data-testid="device-item"], .device-item, .device-card';
    await page.waitForSelector(deviceSelector, { timeout: 10000 });
    await page.locator(deviceSelector).first().click();
    
    // Check connect button
    const connectButton = page.locator('button:has-text("Connect"), [data-testid="connect-button"]');
    await expect(connectButton).toBeEnabled();
  });
});

// Test Suite: Device Connection and Session Management
test.describe('Device Connection and Session Management', () => {
  test('Should successfully connect to Android device', async ({ page }) => {
    await page.goto(CONFIG.ANDROID_BACKEND_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Get available devices first
    const devices = await DeviceTestHelper.getDevicesFromAPI(CONFIG.ANDROID_BACKEND_URL);
    expect(devices.length).toBeGreaterThan(0);
    
    const deviceId = devices[0].id;
    
    // Select and connect to device
    const deviceSelector = `[data-device-id="${deviceId}"], [data-testid="device-item"]`;
    await page.waitForSelector('[data-testid="device-item"], .device-item, .device-card', { timeout: 10000 });
    await page.locator('[data-testid="device-item"], .device-item, .device-card').first().click();
    
    // Click connect button
    const connectButton = page.locator('button:has-text("Connect"), [data-testid="connect-button"]');
    await connectButton.click();
    
    // Wait for connection to establish
    await page.waitForSelector('.connected, .device-connected, [data-status="connected"]', { 
      timeout: 30000 
    });
    
    // Verify connection status
    const connectionStatus = page.locator('.connection-status, .device-status, [data-testid="connection-status"]');
    await expect(connectionStatus).toContainText(/connected|online/i);
  });

  test('Should display device screen after connection', async ({ page }) => {
    await page.goto(CONFIG.ANDROID_BACKEND_URL);
    await page.waitForLoadState('networkidle');
    await page.waitForTimeout(3000);
    
    // Connect to device
    await page.locator('[data-testid="device-item"], .device-item, .device-card').first().click();
    await page.locator('button:has-text("Connect"), [data-testid="connect-button"]').click();
    
    // Wait for device screen
    await page.waitForSelector('.device-screen, #device-screen, [data-testid="device-screen"]', { 
      timeout: 30000 
    });
    
    // Verify screen is visible
    const deviceScreen = page.locator('.device-screen, #device-screen, [data-testid="device-screen"]');
    await expect(deviceScreen).toBeVisible();
  });

  test('Should handle connection errors gracefully', async ({ page }) => {
    await page.goto(CONFIG.ANDROID_BACKEND_URL);
    await page.waitForLoadState('networkidle');
    
    // Try to connect to a non-existent device
    await page.evaluate(() => {
      // Simulate connection to invalid device
      window.connectToDevice && window.connectToDevice('invalid-device-id');
    });
    
    // Check for error handling
    const errorMessage = page.locator('.error, .alert-error, [data-testid="error-message"]');
    // Error message should appear or connection should fail gracefully
  });
});

// Test Suite: API Endpoint Testing
test.describe('API Endpoint Testing', () => {
  test('GET /api/devices should return valid device data', async () => {
    const response = await fetch(`${CONFIG.ANDROID_BACKEND_URL}/api/devices`);
    expect(response.status).toBe(200);
    
    const data = await response.json();
    expect(data).toHaveProperty('devices');
    expect(Array.isArray(data.devices)).toBe(true);
    
    if (data.devices.length > 0) {
      const device = data.devices[0];
      expect(device).toHaveProperty('id');
      expect(device).toHaveProperty('platform', 'Android');
      expect(device).toHaveProperty('status');
    }
  });

  test('GET /api/devices/{id} should return specific device', async () => {
    const devices = await DeviceTestHelper.getDevicesFromAPI(CONFIG.ANDROID_BACKEND_URL);
    if (devices.length === 0) {
      test.skip('No devices available for testing');
    }
    
    const deviceId = devices[0].id;
    const response = await fetch(`${CONFIG.ANDROID_BACKEND_URL}/api/devices/${deviceId}`);
    expect(response.status).toBe(200);
    
    const device = await response.json();
    expect(device.id).toBe(deviceId);
    expect(device.platform).toBe('Android');
  });

  test('POST /api/device/connect should establish connection', async () => {
    const devices = await DeviceTestHelper.getDevicesFromAPI(CONFIG.ANDROID_BACKEND_URL);
    if (devices.length === 0) {
      test.skip('No devices available for testing');
    }
    
    const deviceId = devices[0].id;
    const response = await fetch(`${CONFIG.ANDROID_BACKEND_URL}/api/device/connect`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        device_id: deviceId,
        platform: 'Android'
      })
    });
    
    expect([200, 201]).toContain(response.status);
  });
});

// Test Suite: Cross-Platform Device Discovery
test.describe('Cross-Platform Device Discovery', () => {
  test('Should detect both Android and iOS devices when available', async () => {
    const androidDevices = await DeviceTestHelper.getDevicesFromAPI(CONFIG.ANDROID_BACKEND_URL);
    const iosDevices = await DeviceTestHelper.getDevicesFromAPI(CONFIG.IOS_BACKEND_URL);
    
    console.log('Android devices found:', androidDevices.length);
    console.log('iOS devices found:', iosDevices.length);
    
    // At least Android devices should be available
    expect(androidDevices.length).toBeGreaterThan(0);
    
    // Verify platform-specific properties
    for (const device of androidDevices) {
      expect(device.platform).toBe('Android');
    }
    
    for (const device of iosDevices) {
      expect(device.platform).toBe('iOS');
    }
  });

  test('Device IDs should be unique across platforms', async () => {
    const androidDevices = await DeviceTestHelper.getDevicesFromAPI(CONFIG.ANDROID_BACKEND_URL);
    const iosDevices = await DeviceTestHelper.getDevicesFromAPI(CONFIG.IOS_BACKEND_URL);
    
    const allDeviceIds = [
      ...androidDevices.map(d => d.id),
      ...iosDevices.map(d => d.id)
    ];
    
    const uniqueIds = new Set(allDeviceIds);
    expect(uniqueIds.size).toBe(allDeviceIds.length);
  });
});

// Test Suite: Performance and Reliability
test.describe('Performance and Reliability', () => {
  test('Device discovery should complete within reasonable time', async () => {
    const startTime = Date.now();
    const devices = await DeviceTestHelper.getDevicesFromAPI(CONFIG.ANDROID_BACKEND_URL);
    const endTime = Date.now();
    
    const discoveryTime = endTime - startTime;
    expect(discoveryTime).toBeLessThan(CONFIG.DEVICE_DISCOVERY_TIMEOUT);
    expect(devices.length).toBeGreaterThan(0);
  });

  test('Multiple concurrent device requests should be handled', async () => {
    const promises = Array(5).fill().map(() => 
      DeviceTestHelper.getDevicesFromAPI(CONFIG.ANDROID_BACKEND_URL)
    );
    
    const results = await Promise.all(promises);
    
    // All requests should succeed
    for (const devices of results) {
      expect(Array.isArray(devices)).toBe(true);
      expect(devices.length).toBeGreaterThan(0);
    }
    
    // Results should be consistent
    const firstResult = JSON.stringify(results[0]);
    for (const result of results) {
      expect(JSON.stringify(result)).toBe(firstResult);
    }
  });

  test('Backend should recover from temporary ADB failures', async () => {
    // This test simulates ADB being temporarily unavailable
    // In a real scenario, you might temporarily disable ADB or disconnect devices
    
    const initialDevices = await DeviceTestHelper.getDevicesFromAPI(CONFIG.ANDROID_BACKEND_URL);
    expect(initialDevices.length).toBeGreaterThan(0);
    
    // Wait a bit and check again
    await new Promise(resolve => setTimeout(resolve, 2000));
    
    const laterDevices = await DeviceTestHelper.getDevicesFromAPI(CONFIG.ANDROID_BACKEND_URL);
    expect(laterDevices.length).toBeGreaterThan(0);
  });
});

// Test Suite: Error Handling and Edge Cases
test.describe('Error Handling and Edge Cases', () => {
  test('Should handle invalid device IDs gracefully', async () => {
    const response = await fetch(`${CONFIG.ANDROID_BACKEND_URL}/api/devices/invalid-device-id`);
    expect([404, 400, 500]).toContain(response.status);
  });

  test('Should handle backend unavailability', async () => {
    // Test with a non-existent port
    const invalidUrl = 'http://localhost:9999';
    const devices = await DeviceTestHelper.getDevicesFromAPI(invalidUrl);
    expect(devices).toEqual([]);
  });

  test('Should validate device connection parameters', async () => {
    const response = await fetch(`${CONFIG.ANDROID_BACKEND_URL}/api/device/connect`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({
        // Missing required parameters
      })
    });
    
    expect([400, 422]).toContain(response.status);
  });
});

// Cleanup and teardown
test.afterAll(async () => {
  console.log('Device discovery tests completed');
  console.log('Summary: Tested device discovery, connectivity, and UI functionality');
});