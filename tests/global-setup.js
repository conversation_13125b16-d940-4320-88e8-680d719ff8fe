// global-setup.js
const { chromium } = require('@playwright/test');
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function globalSetup(config) {
  console.log('🚀 Starting global test setup...');
  
  // Wait for backends to be ready
  const backends = [
    { name: 'Android Backend', url: 'http://localhost:8083/api/health', port: 8083 },
    { name: 'iOS Backend', url: 'http://localhost:8088/api/health', port: 8088 },
    { name: 'SaaS Backend', url: 'http://localhost:3000/api/health', port: 3000 }
  ];
  
  console.log('⏳ Checking backend availability...');
  
  for (const backend of backends) {
    let retries = 10;
    let isReady = false;
    
    while (retries > 0 && !isReady) {
      try {
        const response = await fetch(backend.url, { 
          method: 'GET',
          timeout: 5000 
        });
        
        if (response.ok) {
          console.log(`✅ ${backend.name} is ready on port ${backend.port}`);
          isReady = true;
        } else {
          throw new Error(`HTTP ${response.status}`);
        }
      } catch (error) {
        console.log(`⚠️  ${backend.name} not ready (${error.message}), retrying... (${retries} attempts left)`);
        retries--;
        
        if (retries > 0) {
          await new Promise(resolve => setTimeout(resolve, 2000));
        }
      }
    }
    
    if (!isReady) {
      console.log(`❌ ${backend.name} is not available on port ${backend.port}`);
      console.log(`   This may affect tests that depend on ${backend.name}`);
    }
  }
  
  // Check ADB connectivity
  console.log('🔍 Checking ADB connectivity...');
  try {
    const { exec } = require('child_process');
    const { promisify } = require('util');
    const execAsync = promisify(exec);
    
    const { stdout } = await execAsync('adb devices -l');
    const devices = stdout.split('\n')
      .filter(line => line.includes('\tdevice'))
      .map(line => line.split('\t')[0]);
    
    if (devices.length > 0) {
      console.log(`✅ Found ${devices.length} connected Android device(s): ${devices.join(', ')}`);
    } else {
      console.log('⚠️  No Android devices found via ADB');
    }
  } catch (error) {
    console.log(`❌ ADB check failed: ${error.message}`);
  }
  
  console.log('✨ Global setup completed\n');
}

module.exports = globalSetup;