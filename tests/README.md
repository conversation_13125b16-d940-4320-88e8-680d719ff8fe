# Mobile App Automation - Device Discovery Test Suite

This comprehensive test suite uses <PERSON><PERSON> to thoroughly test device discovery and connectivity functionalities of the Mobile App Automation platform.

## 🎯 Test Coverage

The test suite covers:

### Backend Health & API Testing
- ✅ Android Backend (port 8083) health checks
- ✅ iOS Backend (port 8088) health checks  
- ✅ SaaS Backend (port 3000) health checks
- ✅ API endpoint validation and response testing

### Device Discovery Testing
- ✅ ADB device detection and listing
- ✅ Backend API device discovery
- ✅ Cross-platform device discovery (Android/iOS)
- ✅ Device information retrieval and validation

### Web UI Testing
- ✅ Device list display and updates
- ✅ Device connection interface
- ✅ Real-time device status updates
- ✅ User interaction workflows

### Connectivity & Session Management
- ✅ Device connection establishment
- ✅ Session creation and management
- ✅ Connection failure handling
- ✅ Session cleanup and termination

### Performance & Reliability
- ✅ Response time validation
- ✅ Load testing with multiple devices
- ✅ Error handling and recovery
- ✅ Timeout and retry mechanisms

## 🚀 Quick Start

### Prerequisites

1. **Node.js** (v16 or higher)
2. **Android Debug Bridge (ADB)** installed and in PATH
3. **Connected Android device** (for device discovery tests)
4. **Running backends**:
   - Android Backend on port 8083
   - iOS Backend on port 8088 (optional)
   - SaaS Backend on port 3000 (optional)

### Installation

```bash
# Navigate to tests directory
cd tests/

# Install dependencies and browsers
npm run setup

# Or install manually
npm install
npm run install-browsers
```

### Running Tests

```bash
# Run all tests
npm test

# Run tests with browser UI (headed mode)
npm run test:headed

# Run only device discovery tests
npm run test:device-discovery

# Run device discovery tests with browser UI
npm run test:device-discovery:headed

# Debug tests interactively
npm run test:debug

# View test report
npm run test:report
```

## 🔧 Configuration

### Backend URLs

The tests expect backends to be running on these ports:
- **Android Backend**: `http://localhost:8083`
- **iOS Backend**: `http://localhost:8088`
- **SaaS Backend**: `http://localhost:3000`

To modify these URLs, edit the configuration in `device_discovery_test.js`:

```javascript
const CONFIG = {
  ANDROID_BACKEND: 'http://localhost:8083',
  IOS_BACKEND: 'http://localhost:8088',
  SAAS_BACKEND: 'http://localhost:3000',
  // ... other settings
};
```

### Test Timeouts

Default timeouts can be adjusted in `playwright.config.js`:

```javascript
use: {
  actionTimeout: 30000,      // 30 seconds
  navigationTimeout: 30000,  // 30 seconds
},
timeout: 60000,              // 60 seconds per test
```

## 📊 Test Reports

After running tests, reports are generated in multiple formats:

- **HTML Report**: `playwright-report/index.html`
- **JSON Report**: `test-results.json`
- **JUnit Report**: `test-results.xml`
- **Screenshots**: `test-results/` (on failures)
- **Videos**: `test-results/` (on failures)

View the HTML report:
```bash
npm run test:report
```

## 🐛 Troubleshooting

### Common Issues

#### 1. No Android Devices Found
```bash
# Check ADB connection
adb devices -l

# Restart ADB if needed
adb kill-server
adb start-server
```

#### 2. Backend Not Responding
```bash
# Check if backends are running
curl http://localhost:8083/api/health  # Android
curl http://localhost:8088/api/health  # iOS
curl http://localhost:3000/api/health  # SaaS
```

#### 3. Port Conflicts
If backends are running on different ports, update the configuration in the test files.

#### 4. Browser Installation Issues
```bash
# Reinstall Playwright browsers
npx playwright install

# Install system dependencies (Linux)
npx playwright install-deps
```

### Debug Mode

For detailed debugging:

```bash
# Run with debug output
DEBUG=pw:api npm test

# Run specific test with debugging
npm run test:debug -- --grep "Device Discovery"

# Run with trace viewer
npm test -- --trace on
```

## 📁 File Structure

```
tests/
├── device_discovery_test.js    # Main test suite
├── package.json               # Dependencies and scripts
├── playwright.config.js       # Playwright configuration
├── global-setup.js           # Global test setup
├── global-teardown.js        # Global test cleanup
├── README.md                 # This file
├── test-results/             # Test outputs (generated)
└── playwright-report/        # HTML reports (generated)
```

## 🔄 Continuous Integration

For CI/CD integration, use:

```bash
# CI-optimized test run
CI=true npm test
```

This enables:
- Retry on failure (2 retries)
- Single worker (no parallel execution)
- Fail on `test.only` usage

## 📝 Writing Additional Tests

To add new tests, follow the existing patterns in `device_discovery_test.js`:

```javascript
test.describe('New Feature Tests', () => {
  test('should test new functionality', async ({ page }) => {
    // Test implementation
  });
});
```

## 🤝 Contributing

When adding new tests:
1. Follow existing naming conventions
2. Add appropriate test descriptions
3. Include error handling
4. Update this README if needed
5. Ensure tests are independent and can run in any order

## 📞 Support

For issues or questions:
1. Check the troubleshooting section above
2. Review test logs and reports
3. Verify backend and device connectivity
4. Check Playwright documentation: https://playwright.dev/