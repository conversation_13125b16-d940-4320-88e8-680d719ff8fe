{"name": "mobile-app-automation-tests", "version": "1.0.0", "description": "Comprehensive test suite for Mobile App Automation platform device discovery and connectivity", "main": "device_discovery_test.js", "scripts": {"test": "playwright test", "test:headed": "playwright test --headed", "test:debug": "playwright test --debug", "test:device-discovery": "playwright test device_discovery_test.js", "test:device-discovery:headed": "playwright test device_discovery_test.js --headed", "test:report": "playwright show-report", "install-browsers": "playwright install", "setup": "npm install && npm run install-browsers"}, "keywords": ["mobile", "automation", "testing", "playwright", "device-discovery", "android", "ios", "appium"], "author": "Mobile App Automation Team", "license": "MIT", "devDependencies": {"@playwright/test": "^1.40.0", "playwright": "^1.40.0"}, "dependencies": {"node-fetch": "^3.3.2"}}