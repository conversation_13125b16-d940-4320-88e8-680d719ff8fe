// global-teardown.js
const fetch = (...args) => import('node-fetch').then(({default: fetch}) => fetch(...args));

async function globalTeardown(config) {
  console.log('🧹 Starting global test teardown...');
  
  // Clean up any test sessions that might be running
  const backends = [
    { name: 'Android Backend', baseUrl: 'http://localhost:8083' },
    { name: 'iOS Backend', baseUrl: 'http://localhost:8088' },
    { name: 'SaaS Backend', baseUrl: 'http://localhost:3000' }
  ];
  
  for (const backend of backends) {
    try {
      // Try to get active sessions
      const sessionsResponse = await fetch(`${backend.baseUrl}/api/sessions`, {
        method: 'GET',
        timeout: 5000
      });
      
      if (sessionsResponse.ok) {
        const sessions = await sessionsResponse.json();
        
        if (sessions && sessions.length > 0) {
          console.log(`🔄 Cleaning up ${sessions.length} active session(s) on ${backend.name}...`);
          
          // Clean up each session
          for (const session of sessions) {
            try {
              await fetch(`${backend.baseUrl}/api/sessions/${session.id}`, {
                method: 'DELETE',
                timeout: 5000
              });
              console.log(`   ✅ Cleaned up session ${session.id}`);
            } catch (error) {
              console.log(`   ⚠️  Failed to clean up session ${session.id}: ${error.message}`);
            }
          }
        } else {
          console.log(`✅ No active sessions found on ${backend.name}`);
        }
      }
    } catch (error) {
      console.log(`⚠️  Could not check sessions on ${backend.name}: ${error.message}`);
    }
  }
  
  // Clean up any temporary test files
  console.log('🗂️  Cleaning up temporary test files...');
  try {
    const fs = require('fs');
    const path = require('path');
    
    const tempDir = path.join(__dirname, 'temp');
    if (fs.existsSync(tempDir)) {
      fs.rmSync(tempDir, { recursive: true, force: true });
      console.log('   ✅ Temporary files cleaned up');
    }
  } catch (error) {
    console.log(`   ⚠️  Failed to clean up temporary files: ${error.message}`);
  }
  
  // Generate test summary
  console.log('📊 Test execution summary:');
  console.log(`   - Test results available in: test-results/`);
  console.log(`   - HTML report available in: playwright-report/`);
  console.log(`   - Use 'npm run test:report' to view detailed results`);
  
  console.log('✨ Global teardown completed\n');
}

module.exports = globalTeardown;