# Mobile Testing SaaS - Local Device Agent

The Local Device Agent is a lightweight application that runs on your local machine to connect your physical mobile devices to the Mobile Testing SaaS platform.

## Overview

The agent enables you to:
- **Automatically discover** connected Android and iOS devices
- **Execute tests** remotely from the SaaS platform
- **Stream results** in real-time to the cloud
- **Maintain secure communication** with the platform

## Architecture

```
┌─────────────────────┐    WebSocket/HTTPS    ┌─────────────────────┐
│   SaaS Platform     │◄─────────────────────►│   Local Agent      │
│                     │                        │                     │
│ • Web Interface     │                        │ • Device Discovery  │
│ • Test Management   │                        │ • Test Execution    │
│ • Result Storage    │                        │ • Result Streaming  │
└─────────────────────┘                        └─────────────────────┘
                                                          │
                                                          ▼
                                               ┌─────────────────────┐
                                               │  Physical Devices   │
                                               │                     │
                                               │ 📱 Android Devices  │
                                               │ 📱 iOS Devices      │
                                               └─────────────────────┘
```

## Quick Start

### 1. Download and Install

```bash
# Download the installer
curl -O https://your-saas-platform.com/downloads/install_agent.sh

# Make it executable
chmod +x install_agent.sh

# Run the installer
./install_agent.sh
```

### 2. Configure the Agent

During installation, you'll be prompted for:
- **SaaS Platform URL**: Your platform's URL
- **Agent ID**: Unique identifier for your agent
- **API Key**: Authentication token from your account

### 3. Connect Your Devices

**Android Devices:**
1. Enable Developer Options
2. Enable USB Debugging
3. Connect via USB cable
4. Accept debugging authorization

**iOS Devices (macOS only):**
1. Connect via USB cable
2. Trust the computer when prompted
3. Ensure Xcode is installed

### 4. Start the Agent

```bash
# Start the agent
~/.mobile-testing-agent/start_agent.sh
```

## System Requirements

### Minimum Requirements

| Component | Requirement |
|-----------|-------------|
| **OS** | macOS 10.15+, Ubuntu 18.04+, Windows 10+ |
| **Python** | 3.8 or higher |
| **RAM** | 4GB available |
| **Storage** | 2GB free space |
| **Network** | Stable internet connection |

### Platform-Specific Requirements

**macOS (for iOS testing):**
- Xcode 12.0 or higher
- Xcode Command Line Tools
- libimobiledevice (installed automatically)

**All Platforms (for Android testing):**
- Android SDK Platform Tools (ADB)
- USB drivers for your devices

## Installation Details

The installer automatically sets up:

1. **Python Environment**: Virtual environment with required packages
2. **Node.js & Appium**: Mobile automation server
3. **Platform Tools**: ADB, iOS tools (macOS only)
4. **Agent Configuration**: Secure connection setup
5. **Startup Scripts**: Easy agent management

## Configuration

Agent configuration is stored in `~/.mobile-testing-agent/config/agent.conf`:

```json
{
  "agent_id": "your_agent_id",
  "api_key": "your_api_key",
  "platform_url": "https://your-platform.com",
  "device_discovery": {
    "android": { "enabled": true },
    "ios": { "enabled": true }
  },
  "logging": {
    "level": "INFO",
    "file": "~/.mobile-testing-agent/logs/agent.log"
  }
}
```

## Usage

### Starting the Agent

```bash
# Start with default configuration
~/.mobile-testing-agent/start_agent.sh

# Start with custom configuration
cd ~/.mobile-testing-agent
python local_device_agent.py --config /path/to/custom.conf
```

### Monitoring

```bash
# View agent logs
tail -f ~/.mobile-testing-agent/logs/agent.log

# View Appium logs
tail -f ~/.mobile-testing-agent/logs/appium.log

# Check connected devices
adb devices                    # Android
idevice_id -l                 # iOS (macOS only)
```

### Stopping the Agent

```bash
# Stop the agent (Ctrl+C in the terminal)
# Or kill the process
pkill -f "local_device_agent.py"
```

## Device Discovery

The agent automatically discovers devices using:

**Android Devices:**
- Uses `adb devices` to enumerate connected devices
- Retrieves device properties (model, OS version, etc.)
- Monitors device connection status

**iOS Devices:**
- Uses `idevice_id -l` to list connected devices
- Retrieves device information via `ideviceinfo`
- Requires devices to be trusted

## Security

### Authentication
- **JWT Tokens**: Secure agent authentication
- **API Keys**: Platform-issued credentials
- **SSL/TLS**: Encrypted communication

### Data Protection
- Test data encrypted in transit
- No sensitive data stored locally
- Secure credential management

## Troubleshooting

### Common Issues

**Agent won't connect:**
```bash
# Check network connectivity
ping your-platform.com

# Verify configuration
cat ~/.mobile-testing-agent/config/agent.conf

# Check logs
tail -f ~/.mobile-testing-agent/logs/agent.log
```

**Devices not detected:**
```bash
# Android: Check ADB
adb devices
adb kill-server && adb start-server

# iOS: Check libimobiledevice
idevice_id -l
brew reinstall libimobiledevice  # macOS
```

**Appium issues:**
```bash
# Check Appium installation
appium --version
appium-doctor  # Diagnose issues

# Restart Appium
pkill -f appium
appium &
```

### Getting Help

1. **Check Logs**: Always start with the agent logs
2. **Platform Documentation**: Visit your SaaS platform's help section
3. **Device Setup**: Ensure devices are properly configured
4. **Network**: Verify firewall and proxy settings

## Advanced Configuration

### Custom Appium Server

```json
{
  "appium_server": {
    "host": "localhost",
    "port": 4723,
    "auto_start": false,
    "custom_args": ["--log-level", "debug"]
  }
}
```

### Device Filtering

```json
{
  "device_discovery": {
    "android": {
      "enabled": true,
      "min_api_level": 21,
      "exclude_emulators": true
    },
    "ios": {
      "enabled": true,
      "min_version": "12.0"
    }
  }
}
```

### Logging Configuration

```json
{
  "logging": {
    "level": "DEBUG",
    "file": "custom_agent.log",
    "max_size_mb": 50,
    "backup_count": 10,
    "console_output": true
  }
}
```

## Updates

The agent supports automatic updates:

```bash
# Check for updates
python local_device_agent.py --check-updates

# Update agent
python local_device_agent.py --update
```

## Uninstallation

```bash
# Remove agent directory
rm -rf ~/.mobile-testing-agent

# Remove global packages (optional)
npm uninstall -g appium
brew uninstall libimobiledevice  # macOS
```

## Support

For technical support:
- 📧 Email: <EMAIL>
- 📖 Documentation: https://your-platform.com/docs
- 💬 Community: https://your-platform.com/community

---

**Mobile Testing SaaS Platform**  
Version 1.0.0 | © 2024