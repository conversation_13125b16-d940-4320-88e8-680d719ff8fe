version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:13
    container_name: saas_postgres
    environment:
      POSTGRES_DB: saas_platform
      POSTGRES_USER: saas_user
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD:-secure_password_123}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    networks:
      - saas_network
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U saas_user -d saas_platform"]
      interval: 30s
      timeout: 10s
      retries: 5

  # Redis Cache
  redis:
    image: redis:6-alpine
    container_name: saas_redis
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD:-redis_password_123}
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - saas_network
    restart: unless-stopped
    healthcheck:
      test: ["C<PERSON>", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 10s
      retries: 5

  # SaaS Application Server
  saas_server:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: saas_app
    environment:
      # Database Configuration
      DATABASE_URL: postgresql://saas_user:${POSTGRES_PASSWORD:-secure_password_123}@postgres:5432/saas_platform
      
      # Redis Configuration
      REDIS_URL: redis://:${REDIS_PASSWORD:-redis_password_123}@redis:6379/0
      
      # Security Configuration
      SECRET_KEY: ${SECRET_KEY:-your-super-secret-key-change-in-production}
      JWT_SECRET_KEY: ${JWT_SECRET_KEY:-jwt-super-secret-key-change-in-production}
      
      # Server Configuration
      SERVER_HOST: 0.0.0.0
      SERVER_PORT: 8080
      FLASK_ENV: ${FLASK_ENV:-production}
      
      # File Upload Configuration
      UPLOAD_FOLDER: /app/uploads
      
      # Admin User (for initial setup)
      ADMIN_EMAIL: ${ADMIN_EMAIL:-<EMAIL>}
      ADMIN_PASSWORD: ${ADMIN_PASSWORD:-admin123}
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
    ports:
      - "8080:8080"
    networks:
      - saas_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8080/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 40s

  # Android Application Service
  android_app:
    build:
      context: .
      dockerfile: Dockerfile.android
    container_name: android_automation_app
    environment:
      FLASK_ENV: ${FLASK_ENV:-production}
      FLASK_PORT: 8083
      APPIUM_PORT: 4726
      DATABASE_URL: postgresql://saas_user:${POSTGRES_PASSWORD:-secure_password_123}@postgres:5432/saas_platform
      REDIS_URL: redis://:${REDIS_PASSWORD:-redis_password_123}@redis:6379/0
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
      - /dev/bus/usb:/dev/bus/usb  # USB device access for Android devices
    ports:
      - "8083:8083"  # Flask app
      - "4726:4726"  # Appium server
    networks:
      - saas_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    privileged: true  # Required for USB device access
    devices:
      - /dev/bus/usb:/dev/bus/usb  # USB device mapping
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8083/api/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # iOS Application Service
  ios_app:
    build:
      context: .
      dockerfile: Dockerfile.ios
    container_name: ios_automation_app
    environment:
      FLASK_ENV: ${FLASK_ENV:-production}
      FLASK_PORT: 8088
      APPIUM_PORT: 4723
      DATABASE_URL: postgresql://saas_user:${POSTGRES_PASSWORD:-secure_password_123}@postgres:5432/saas_platform
      REDIS_URL: redis://:${REDIS_PASSWORD:-redis_password_123}@redis:6379/0
    volumes:
      - app_uploads:/app/uploads
      - app_logs:/app/logs
    ports:
      - "8088:8088"  # Flask app
      - "4723:4723"  # Appium server
    networks:
      - saas_network
    depends_on:
      postgres:
        condition: service_healthy
      redis:
        condition: service_healthy
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8088/api/health"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s

  # Nginx Reverse Proxy (Optional for production)
  nginx:
    image: nginx:alpine
    container_name: saas_nginx
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    ports:
      - "80:80"
      - "443:443"
    networks:
      - saas_network
    depends_on:
      - saas_server
      - android_app
      - ios_app
    restart: unless-stopped
    profiles:
      - production

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  app_uploads:
    driver: local
  app_logs:
    driver: local

networks:
  saas_network:
    driver: bridge