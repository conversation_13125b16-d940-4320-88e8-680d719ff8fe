2025-08-22 20:38:56,911 - __main__ - WARNING - Dynamic port allocation failed: No module named 'utils.dynamic_port_init'. Using fallback ports.
2025-08-22 20:38:56,920 - app_android.utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-22 20:38:56,921 - app_android.utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-22 20:38:56,921 - app_android.config_android - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-08-22 20:38:56,922 - app_android.config_android - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/android_data/reports
2025-08-22 20:38:56,922 - app_android.config_android - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/screenshots_android
2025-08-22 20:38:56,923 - app_android.config_android - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/android_data/reference_images
2025-08-22 20:38:56,924 - app_android.config_android - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/android_data/test_suites
2025-08-22 20:38:56,924 - app_android.config_android - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/reports_android/suites
2025-08-22 20:38:56,925 - app_android.config_android - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/recordings_android
2025-08-22 20:38:56,925 - app_android.config_android - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/temp_android
2025-08-22 20:38:56,925 - app_android.config_android - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/MobileApp-AutoTest/files_to_push
2025-08-22 20:38:56,926 - __main__ - INFO - Using instance-specific database paths with suffix: _port_8081
2025-08-22 20:38:56,926 - __main__ - INFO - Using custom ports (Flask: 8081, Appium: 4724, WDA: 8300) - preserving existing processes for multi-instance support
2025-08-22 20:38:56,926 - __main__ - INFO - Skipping process termination when using custom ports for multi-instance support
2025-08-22 20:38:58,593 - app_android.utils.global_values_db - INFO - Using global values database at: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/app/data/global_values_port_8081.db
2025-08-22 20:38:58,593 - app_android.utils.global_values_db - INFO - Global values database initialized successfully
2025-08-22 20:38:58,596 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-22 20:38:58,597 - utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-22 20:38:58,597 - config - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/test_cases_ios
2025-08-22 20:38:58,598 - config - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/reports_ios
2025-08-22 20:38:58,598 - config - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/screenshots_ios
2025-08-22 20:38:58,599 - config - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/ios_data/reference_images
2025-08-22 20:38:58,599 - config - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/test_suites_ios
2025-08-22 20:38:58,600 - config - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/reports_ios/suites
2025-08-22 20:38:58,600 - config - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/recordings_ios
2025-08-22 20:38:58,601 - config - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/temp_ios
2025-08-22 20:38:58,601 - config - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
2025-08-22 20:38:58,601 - app_android.utils.global_values_db - INFO - Using global values from config.py
2025-08-22 20:38:58,601 - app_android.utils.global_values_db - INFO - Updated default values from config.py: {'default_element_timeout': 60, 'Test Run Retry': 3, 'Auto Rerun Failed': False, 'Test Case Delay': 15, 'Max Step Execution Time': 300, 'Connection Retry Attempts': 3, 'Connection Retry Delay': 2}
2025-08-22 20:38:58,604 - app_android.utils.healenium_config - INFO - Loaded Healenium configuration: enabled=True
2025-08-22 20:38:58,605 - appium_device_controller - WARNING - TouchAction not available in this Appium Python Client version - using W3C Actions fallback
2025-08-22 20:38:58,655 - AppiumDeviceController - INFO - Successfully imported Airtest library.
2025-08-22 20:38:59,112 - app_android.utils.directory_paths_db - INFO - Directory paths and environments database initialized/verified
2025-08-22 20:38:59,113 - config_android - INFO - Using database path for TEST_CASES: /Users/<USER>/Documents/automation-tool/android_data/test_cases
2025-08-22 20:38:59,113 - config_android - INFO - Using database path for REPORTS: /Users/<USER>/Documents/automation-tool/android_data/reports
2025-08-22 20:38:59,114 - config_android - INFO - Using database path for SCREENSHOTS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/screenshots_ios
2025-08-22 20:38:59,114 - config_android - INFO - Using database path for REFERENCE_IMAGES: /Users/<USER>/Documents/automation-tool/android_data/reference_images
2025-08-22 20:38:59,115 - config_android - INFO - Using database path for TEST_SUITES: /Users/<USER>/Documents/automation-tool/android_data/test_suites
2025-08-22 20:38:59,115 - config_android - INFO - Using database path for RESULTS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/reports_ios/suites
2025-08-22 20:38:59,115 - config_android - INFO - Using database path for RECORDINGS: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/recordings_ios
2025-08-22 20:38:59,116 - config_android - INFO - Using database path for TEMP_FILES: /Users/<USER>/Documents/automation-tool/MobileAppAutomation/temp_ios
2025-08-22 20:38:59,116 - config_android - INFO - Using database path for FILES_TO_PUSH: /Users/<USER>/Documents/automation-tool/files_to_push
