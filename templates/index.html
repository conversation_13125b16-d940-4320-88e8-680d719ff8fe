{% extends "base.html" %}

{% block title %}Mobile App Auto-Test Platform{% endblock %}

{% block content %}
<div class="container-fluid p-0">
    <div class="row min-vh-100 align-items-center justify-content-center" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);">
        <div class="col-md-8 col-lg-6">
            <div class="card shadow-lg border-0" style="border-radius: 20px; background: rgba(255, 255, 255, 0.95); backdrop-filter: blur(10px);">
                <div class="card-body p-5 text-center">
                    <div class="mb-4">
                        <i class="fas fa-mobile-alt" style="font-size: 4rem; color: #667eea;"></i>
                    </div>
                    
                    <h1 class="display-4 fw-bold text-primary mb-3">Mobile App Auto-Test Platform</h1>
                    <p class="lead text-muted mb-4">
                        Comprehensive automated testing solution for iOS and Android applications.
                        Create, execute, and manage your mobile test suites with ease.
                    </p>
                    
                    <div class="row g-3 mb-4">
                        <div class="col-md-4">
                            <div class="feature-item">
                                <i class="fas fa-robot text-success mb-2" style="font-size: 2rem;"></i>
                                <h6>Automated Testing</h6>
                                <small class="text-muted">AI-powered test automation</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-item">
                                <i class="fas fa-chart-line text-info mb-2" style="font-size: 2rem;"></i>
                                <h6>Real-time Analytics</h6>
                                <small class="text-muted">Live test execution monitoring</small>
                            </div>
                        </div>
                        <div class="col-md-4">
                            <div class="feature-item">
                                <i class="fas fa-cloud text-warning mb-2" style="font-size: 2rem;"></i>
                                <h6>Cloud Platform</h6>
                                <small class="text-muted">Scalable cloud infrastructure</small>
                            </div>
                        </div>
                    </div>
                    
                    <div class="d-grid gap-3 d-md-flex justify-content-md-center">
                        <a href="{{ url_for('login_page') }}" class="btn btn-primary btn-lg px-4 py-3" style="border-radius: 15px;">
                            <i class="fas fa-sign-in-alt me-2"></i>Login to Dashboard
                        </a>
                        <a href="{{ url_for('register_page') }}" class="btn btn-outline-primary btn-lg px-4 py-3" style="border-radius: 15px;">
                            <i class="fas fa-user-plus me-2"></i>Create Account
                        </a>
                    </div>
                    
                    <hr class="my-4">
                    
                    <div class="row text-start">
                        <div class="col-md-6">
                            <h6 class="text-primary"><i class="fab fa-android me-2"></i>Android Support</h6>
                            <ul class="list-unstyled small text-muted">
                                <li>• Native app testing</li>
                                <li>• Hybrid app support</li>
                                <li>• Real device testing</li>
                                <li>• Emulator compatibility</li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h6 class="text-primary"><i class="fab fa-apple me-2"></i>iOS Support</h6>
                            <ul class="list-unstyled small text-muted">
                                <li>• Native app testing</li>
                                <li>• XCUITest integration</li>
                                <li>• Real device testing</li>
                                <li>• Simulator support</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="text-center mt-4">
                <p class="text-white-50 small">
                    <i class="fas fa-shield-alt me-1"></i>
                    Secure • Scalable • Reliable
                </p>
            </div>
        </div>
    </div>
</div>

<style>
.feature-item {
    padding: 1rem;
    border-radius: 10px;
    transition: transform 0.2s ease;
}

.feature-item:hover {
    transform: translateY(-2px);
    background: rgba(102, 126, 234, 0.05);
}

.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}
</style>
{% endblock %}