{% extends "base.html" %}

{% block title %}Dashboard - Mobile App Auto-Test Platform{% endblock %}

{% block content %}
<div class="row mt-4">
    <div class="col-12">
        <div class="d-flex justify-content-between align-items-center mb-4">
            <div>
                <h2 class="mb-1">
                    <i class="fas fa-tachometer-alt me-2 text-primary"></i>Dashboard
                </h2>
                {% if session.user_email %}
                    <p class="text-muted mb-0">Welcome back, {{ session.user_email }}</p>
                {% else %}
                    <p class="text-muted mb-0">Welcome to the Mobile App Auto-Test Platform!</p>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Platform Selection -->
<div class="row mb-4">
    <div class="col-12">
        <h4 class="mb-3">
            <i class="fas fa-mobile-alt me-2"></i>Select Testing Platform
        </h4>
    </div>
    
    <div class="col-md-6 mb-3">
        <div class="card platform-card h-100" style="cursor: pointer;" data-href="{{ url_for('app_android') }}">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fab fa-android fa-4x text-success"></i>
                </div>
                <h5 class="card-title">Android Testing</h5>
                <p class="card-text text-muted">
                    Test Android applications with comprehensive automation tools
                </p>
                <div class="mt-4">
                    <button class="btn btn-success btn-lg">
                        <i class="fas fa-play me-2"></i>Launch Android App
                    </button>
                </div>
            </div>
        </div>
    </div>
    
    <div class="col-md-6 mb-3">
        <div class="card platform-card h-100" style="cursor: pointer;" data-href="{{ url_for('app_ios') }}">
            <div class="card-body text-center">
                <div class="mb-3">
                    <i class="fab fa-apple fa-4x text-primary"></i>
                </div>
                <h5 class="card-title">iOS Testing</h5>
                <p class="card-text text-muted">
                    Test iOS applications with advanced automation capabilities
                </p>
                <div class="mt-4">
                    <button class="btn btn-primary btn-lg">
                        <i class="fas fa-play me-2"></i>Launch iOS App
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>


{% endblock %}

{% block scripts %}
<script>
    // Platform card hover effects and click handlers
    document.querySelectorAll('.platform-card').forEach(card => {
        card.addEventListener('mouseenter', function() {
            this.style.boxShadow = '0 8px 25px rgba(0,0,0,0.15)';
        });
        
        card.addEventListener('mouseleave', function() {
            this.style.boxShadow = '';
        });
        
        // Add click handler for platform launch
        card.addEventListener('click', function() {
            const href = this.getAttribute('data-href');
            const platform = href.includes('android') ? 'android' : 'ios';
            launchPlatform(platform, this);
        });
    });
    
    function launchPlatform(platform, cardElement) {
        const button = cardElement.querySelector('button');
        const originalText = button.innerHTML;
        
        // Show loading state
        showLoading(button);
        
        // Create session first
        fetch('/api/session/create', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                platform: platform,
                user_id: '{{ session.user_id or "anonymous" }}'
            })
        })
        .then(response => response.json())
        .then(data => {
            hideLoading(button, originalText);
            
            if (data.success) {
                // Session created successfully, redirect to the platform
                if (data.session_id) {
                    // Use session-specific URL
                    window.location.href = platform === 'android' ? `/app_android/${data.session_id}` : `/app_ios/${data.session_id}`;
                } else {
                    // Fallback to direct navigation
                    window.location.href = platform === 'android' ? '/app_android' : '/app_ios';
                }
            } else {
                showToast(data.error || `Failed to launch ${platform} platform`, 'danger');
            }
        })
        .catch(error => {
            hideLoading(button, originalText);
            console.error('Launch error:', error);
            showToast(`Error launching ${platform} platform: ${error.message}`, 'danger');
        });
    }
</script>
{% endblock %}