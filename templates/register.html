{% extends "base.html" %}

{% block title %}Register - Mobile App Auto-Test Platform{% endblock %}

{% block content %}
<div class="row justify-content-center mt-4">
    <div class="col-md-8 col-lg-6">
        <div class="card shadow">
            <div class="card-header bg-success text-white text-center">
                <h4 class="mb-0">
                    <i class="fas fa-user-plus me-2"></i>Create Account
                </h4>
            </div>
            <div class="card-body">
                <form id="registerForm">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="first_name" class="form-label">
                                <i class="fas fa-user me-1"></i>First Name
                            </label>
                            <input type="text" class="form-control" id="first_name" name="first_name" placeholder="Enter your first name" required>
                            <div class="invalid-feedback"></div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="last_name" class="form-label">
                                <i class="fas fa-user me-1"></i>Last Name
                            </label>
                            <input type="text" class="form-control" id="last_name" name="last_name" placeholder="Enter your last name" required>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-1"></i>Email Address
                        </label>
                        <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email address" required>
                        <div class="invalid-feedback"></div>
                        <div class="form-text">
                            We'll never share your email with anyone else.
                        </div>
                    </div>
                    
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-1"></i>Password
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="password" name="password" placeholder="Create a password" required>
                                <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback"></div>
                            <div class="form-text">
                                Minimum 8 characters with letters and numbers.
                            </div>
                        </div>
                        
                        <div class="col-md-6 mb-3">
                            <label for="confirm_password" class="form-label">
                                <i class="fas fa-lock me-1"></i>Confirm Password
                            </label>
                            <div class="input-group">
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" placeholder="Confirm your password" required>
                                <button class="btn btn-outline-secondary" type="button" id="toggleConfirmPassword">
                                    <i class="fas fa-eye"></i>
                                </button>
                            </div>
                            <div class="invalid-feedback"></div>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="organization" class="form-label">
                            <i class="fas fa-building me-1"></i>Organization (Optional)
                        </label>
                        <input type="text" class="form-control" id="organization" name="organization" placeholder="Enter your organization name">
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="agree_terms" name="agree_terms" required>
                        <label class="form-check-label" for="agree_terms">
                            I agree to the <a href="#" data-bs-toggle="modal" data-bs-target="#termsModal">Terms of Service</a> 
                            and <a href="#" data-bs-toggle="modal" data-bs-target="#privacyModal">Privacy Policy</a>
                        </label>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-success" id="registerBtn">
                            <i class="fas fa-user-plus me-2"></i>Create Account
                        </button>
                    </div>
                </form>
                
                <hr>
                
                <div class="text-center">
                    <p class="mb-0">
                        Already have an account? 
                        <a href="{{ url_for('login') }}" class="text-decoration-none">
                            <i class="fas fa-sign-in-alt me-1"></i>Login here
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Terms of Service Modal -->
<div class="modal fade" id="termsModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Terms of Service</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>1. Acceptance of Terms</h6>
                <p>By using this Mobile App Auto-Test Platform, you agree to these terms of service.</p>
                
                <h6>2. Service Description</h6>
                <p>This platform provides automated testing services for mobile applications on iOS and Android platforms.</p>
                
                <h6>3. User Responsibilities</h6>
                <ul>
                    <li>Provide accurate registration information</li>
                    <li>Maintain the security of your account</li>
                    <li>Use the service in compliance with applicable laws</li>
                    <li>Not attempt to disrupt or compromise the service</li>
                </ul>
                
                <h6>4. Data Usage</h6>
                <p>Test data and results are stored securely and used only for providing testing services.</p>
                
                <h6>5. Service Availability</h6>
                <p>We strive to maintain high availability but cannot guarantee uninterrupted service.</p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>

<!-- Privacy Policy Modal -->
<div class="modal fade" id="privacyModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Privacy Policy</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <h6>Information We Collect</h6>
                <ul>
                    <li>Account information (name, email, organization)</li>
                    <li>Test data and results</li>
                    <li>Usage analytics and logs</li>
                </ul>
                
                <h6>How We Use Information</h6>
                <ul>
                    <li>Provide and improve testing services</li>
                    <li>Communicate with users about their account</li>
                    <li>Analyze usage patterns for service optimization</li>
                </ul>
                
                <h6>Data Security</h6>
                <p>We implement industry-standard security measures to protect your data.</p>
                
                <h6>Data Sharing</h6>
                <p>We do not sell or share personal data with third parties except as required by law.</p>
                
                <h6>Contact Us</h6>
                <p>For privacy concerns, contact <NAME_EMAIL></p>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Toggle password visibility
    function setupPasswordToggle(passwordFieldId, toggleButtonId) {
        document.getElementById(toggleButtonId).addEventListener('click', function() {
            const passwordField = document.getElementById(passwordFieldId);
            const toggleIcon = this.querySelector('i');
            
            if (passwordField.type === 'password') {
                passwordField.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordField.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        });
    }
    
    setupPasswordToggle('password', 'togglePassword');
    setupPasswordToggle('confirm_password', 'toggleConfirmPassword');
    
    // Password strength indicator
    document.getElementById('password').addEventListener('input', function() {
        const password = this.value;
        const strengthIndicator = document.getElementById('passwordStrength') || createPasswordStrengthIndicator();
        
        let strength = 0;
        let feedback = [];
        
        if (password.length >= 8) strength++;
        else feedback.push('At least 8 characters');
        
        if (/[a-z]/.test(password)) strength++;
        else feedback.push('Lowercase letter');
        
        if (/[A-Z]/.test(password)) strength++;
        else feedback.push('Uppercase letter');
        
        if (/[0-9]/.test(password)) strength++;
        else feedback.push('Number');
        
        if (/[^a-zA-Z0-9]/.test(password)) strength++;
        else feedback.push('Special character');
        
        updatePasswordStrengthIndicator(strengthIndicator, strength, feedback);
    });
    
    function createPasswordStrengthIndicator() {
        const indicator = document.createElement('div');
        indicator.id = 'passwordStrength';
        indicator.className = 'mt-2';
        document.getElementById('password').parentNode.parentNode.appendChild(indicator);
        return indicator;
    }
    
    function updatePasswordStrengthIndicator(indicator, strength, feedback) {
        const colors = ['danger', 'danger', 'warning', 'info', 'success'];
        const labels = ['Very Weak', 'Weak', 'Fair', 'Good', 'Strong'];
        
        indicator.innerHTML = `
            <div class="progress" style="height: 5px;">
                <div class="progress-bar bg-${colors[strength]}" style="width: ${(strength / 5) * 100}%"></div>
            </div>
            <small class="text-${colors[strength]}">
                Password Strength: ${labels[strength] || 'Very Weak'}
                ${feedback.length > 0 ? ' - Missing: ' + feedback.join(', ') : ''}
            </small>
        `;
    }
    
    // Form validation
    document.getElementById('registerForm').addEventListener('submit', function(e) {
        const password = document.getElementById('password').value;
        const confirmPassword = document.getElementById('confirm_password').value;
        
        if (password !== confirmPassword) {
            e.preventDefault();
            showToast('Passwords do not match', 'danger');
            return;
        }
        
        const submitBtn = document.getElementById('registerBtn');
        const originalText = submitBtn.innerHTML;
        showLoading(submitBtn);
        
        // Re-enable button after 10 seconds in case of network issues
        setTimeout(() => {
            hideLoading(submitBtn, originalText);
        }, 10000);
    });
    
    // Auto-focus first name field
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('first_name').focus();
    });
</script>
{% endblock %}