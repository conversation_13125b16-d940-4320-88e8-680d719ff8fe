{% extends "base.html" %}

{% block title %}Login - Mobile App Auto-Test Platform{% endblock %}

{% block content %}
<div class="row justify-content-center mt-5">
    <div class="col-md-6 col-lg-4">
        <div class="card shadow">
            <div class="card-header bg-primary text-white text-center">
                <h4 class="mb-0">
                    <i class="fas fa-sign-in-alt me-2"></i>Login
                </h4>
            </div>
            <div class="card-body">
                <form id="loginForm">
                    <div class="mb-3">
                        <label for="email" class="form-label">
                            <i class="fas fa-envelope me-1"></i>Email
                        </label>
                        <input type="email" class="form-control" id="email" name="email" placeholder="Enter your email" required>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="password" class="form-label">
                            <i class="fas fa-lock me-1"></i>Password
                        </label>
                        <div class="input-group">
                            <input type="password" class="form-control" id="password" name="password" placeholder="Enter your password" required>
                            <button class="btn btn-outline-secondary" type="button" id="togglePassword">
                                <i class="fas fa-eye"></i>
                            </button>
                        </div>
                        <div class="invalid-feedback"></div>
                    </div>
                    
                    <div class="mb-3 form-check">
                        <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                        <label class="form-check-label" for="remember_me">
                            Remember me
                        </label>
                    </div>
                    
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary" id="loginBtn">
                            <i class="fas fa-sign-in-alt me-2"></i>Login
                        </button>
                    </div>
                </form>
                
                <hr>
                
                <div class="text-center">
                    <p class="mb-0">
                        Don't have an account? 
                        <a href="{{ url_for('register_page') }}" class="text-decoration-none">
                            <i class="fas fa-user-plus me-1"></i>Register here
                        </a>
                    </p>
                </div>
                
                <!-- Demo Credentials -->
                <div class="mt-4 p-3 bg-light rounded">
                    <h6 class="text-muted mb-2">
                        <i class="fas fa-info-circle me-1"></i>Test Credentials
                    </h6>
                    <div class="row">
                        <div class="col-12">
                            <small class="text-muted d-block">Test User (Supabase):</small>
                            <small class="fw-bold"><EMAIL></small><br>
                            <small class="fw-bold">test123</small>
                        </div>
                    </div>
                    <div class="mt-2">
                        <button type="button" class="btn btn-sm btn-outline-primary me-2" onclick="fillTestUser()">
                            Fill Test User
                        </button>
                        <small class="text-muted">
                            <i class="fas fa-database me-1"></i>Authenticated via Supabase
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
    // Toggle password visibility
    document.getElementById('togglePassword').addEventListener('click', function() {
        const passwordField = document.getElementById('password');
        const toggleIcon = this.querySelector('i');
        
        if (passwordField.type === 'password') {
            passwordField.type = 'text';
            toggleIcon.classList.remove('fa-eye');
            toggleIcon.classList.add('fa-eye-slash');
        } else {
            passwordField.type = 'password';
            toggleIcon.classList.remove('fa-eye-slash');
            toggleIcon.classList.add('fa-eye');
        }
    });
    
    // Test user function
    function fillTestUser() {
        document.getElementById('email').value = '<EMAIL>';
        document.getElementById('password').value = 'test123';
    }
    
    // Form submission with loading state
    document.getElementById('loginForm').addEventListener('submit', function(e) {
        e.preventDefault();
        
        const submitBtn = document.getElementById('loginBtn');
        const originalText = submitBtn.innerHTML;
        showLoading(submitBtn);
        
        const formData = new FormData(this);
        const loginData = {
            email: formData.get('email'),
            password: formData.get('password')
        };
        
        console.log('Sending login data:', {
            email: loginData.email,
            password: loginData.password ? '[HIDDEN]' : 'EMPTY'
        });
        
        fetch('/api/auth/login', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify(loginData)
        })
        .then(response => {
            console.log('Response status:', response.status);
            console.log('Response headers:', response.headers);
            if (!response.ok) {
                return response.json().then(errorData => {
                    throw new Error(errorData.message || `HTTP ${response.status}`);
                });
            }
            return response.json();
        })
        .then(data => {
            hideLoading(submitBtn, originalText);
            console.log('Supabase login response:', data);

            if (data.success && data.access_token) {
                // Show success message
                showAlert('Login successful! Redirecting...', 'success');

                // Redirect to dashboard after a short delay
                setTimeout(() => {
                    window.location.href = '/dashboard';
                }, 1000);
            } else {
                showAlert(data.error || data.message || 'Login failed. Please try again.', 'danger');
            }
        })
        .catch(error => {
            hideLoading(submitBtn, originalText);
            console.error('Login error:', error);
            showAlert(error.message || 'An error occurred. Please try again.', 'danger');
        });
    });
    
    // Helper function to show alerts
    function showAlert(message, type) {
        const alertDiv = document.createElement('div');
        alertDiv.className = `alert alert-${type} alert-dismissible fade show`;
        alertDiv.innerHTML = `
            ${message}
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        `;
        
        const form = document.getElementById('loginForm');
        form.insertBefore(alertDiv, form.firstChild);
        
        // Auto-dismiss after 5 seconds
        setTimeout(() => {
            if (alertDiv.parentNode) {
                alertDiv.remove();
            }
        }, 5000);
    }
    
    // Auto-focus email field
    document.addEventListener('DOMContentLoaded', function() {
        document.getElementById('email').focus();
    });
</script>
{% endblock %}