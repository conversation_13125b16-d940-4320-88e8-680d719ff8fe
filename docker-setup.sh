#!/bin/bash

# Docker Setup Script for Mobile App Automation Testing
# This script sets up the complete Docker environment for device testing

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Docker is installed and running
check_docker() {
    print_status "Checking Docker installation..."
    
    if ! command -v docker &> /dev/null; then
        print_error "Docker is not installed. Please install Docker first."
        exit 1
    fi
    
    if ! docker info &> /dev/null; then
        print_error "Docker is not running. Please start Docker first."
        exit 1
    fi
    
    print_success "Docker is installed and running"
}

# Function to check if Docker Compose is available
check_docker_compose() {
    print_status "Checking Docker Compose..."
    
    if docker compose version &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker compose"
    elif command -v docker-compose &> /dev/null; then
        DOCKER_COMPOSE_CMD="docker-compose"
    else
        print_error "Docker Compose is not available. Please install Docker Compose."
        exit 1
    fi
    
    print_success "Docker Compose is available: $DOCKER_COMPOSE_CMD"
}

# Function to setup environment files
setup_environment() {
    print_status "Setting up environment files..."
    
    # Create .env file if it doesn't exist
    if [ ! -f .env ]; then
        print_status "Creating .env file..."
        cat > .env << EOF
# Database Configuration
POSTGRES_DB=mobile_automation
POSTGRES_USER=automation_user
POSTGRES_PASSWORD=secure_password_123
POSTGRES_HOST=postgres
POSTGRES_PORT=5432

# Redis Configuration
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=redis_password_123

# Application Configuration
FLASK_ENV=development
SECRET_KEY=your-secret-key-here
JWT_SECRET_KEY=your-jwt-secret-key-here

# Device Agent Configuration
DEVICE_AGENT_URL=http://device_agent:8084
DEVICE_AGENT_ENABLED=true

# Testing Configuration
ENABLE_DEVICE_TESTING=true
DEBUG_DEVICE_DISCOVERY=true
LOG_LEVEL=DEBUG

# Ports Configuration
SAAS_PORT=8080
ANDROID_FLASK_PORT=8083
ANDROID_APPIUM_PORT=4726
IOS_FLASK_PORT=8088
IOS_APPIUM_PORT=4727
DEVICE_AGENT_PORT=8084
DASHBOARD_PORT=8090
EOF
        print_success "Created .env file"
    else
        print_warning ".env file already exists, skipping creation"
    fi
}

# Function to create necessary directories
setup_directories() {
    print_status "Creating necessary directories..."
    
    directories=(
        "logs"
        "uploads"
        "temp"
        "temp_android"
        "temp_ios"
        "dashboard/static"
        "dashboard/templates"
    )
    
    for dir in "${directories[@]}"; do
        if [ ! -d "$dir" ]; then
            mkdir -p "$dir"
            print_status "Created directory: $dir"
        fi
    done
    
    print_success "Directory setup complete"
}

# Function to check device permissions (Linux/macOS)
check_device_permissions() {
    print_status "Checking device access permissions..."
    
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        # Linux - check udev rules and user groups
        if ! groups $USER | grep -q "plugdev"; then
            print_warning "User $USER is not in 'plugdev' group. You may need to add yourself:"
            print_warning "sudo usermod -a -G plugdev $USER"
        fi
        
        if [ ! -f /etc/udev/rules.d/51-android.rules ]; then
            print_warning "Android udev rules not found. Device detection may not work properly."
        fi
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        # macOS - check for developer tools
        if ! command -v xcrun &> /dev/null; then
            print_warning "Xcode command line tools not found. iOS device testing may not work."
        fi
    fi
}

# Function to build Docker images
build_images() {
    print_status "Building Docker images..."
    
    # Build base images first
    $DOCKER_COMPOSE_CMD build --no-cache
    
    print_success "Docker images built successfully"
}

# Function to start device agent on host
start_device_agent() {
    print_status "Starting local device agent on host machine..."
    
    # Check if device agent is already running
    if pgrep -f "local_device_agent.py" > /dev/null; then
        print_warning "Device agent is already running"
        return 0
    fi
    
    # Start device agent in background
    nohup python3 local_device_agent.py --config config/agent.conf --log-level INFO > logs/device_agent.log 2>&1 &
    local agent_pid=$!
    
    # Wait a moment for startup
    sleep 3
    
    # Check if it started successfully
    if kill -0 $agent_pid 2>/dev/null; then
        print_success "Device agent started successfully (PID: $agent_pid)"
        echo $agent_pid > .device_agent.pid
    else
        print_error "Failed to start device agent"
        return 1
    fi
}

# Function to stop device agent
stop_device_agent() {
    print_status "Stopping local device agent..."
    
    if [ -f .device_agent.pid ]; then
        local pid=$(cat .device_agent.pid)
        if kill -0 $pid 2>/dev/null; then
            kill $pid
            print_success "Device agent stopped"
        fi
        rm -f .device_agent.pid
    else
        # Fallback: kill by process name
        pkill -f "local_device_agent.py" && print_success "Device agent stopped"
    fi
}

# Function to start services
start_services() {
    local mode=$1
    
    print_status "Starting services in $mode mode..."
    
    case $mode in
        "development")
            $DOCKER_COMPOSE_CMD -f docker-compose.yml -f docker-compose.dev.yml up -d
            ;;
        "device-testing")
            # Start device agent first for device testing mode
            start_device_agent
            $DOCKER_COMPOSE_CMD -f docker-compose.yml -f docker-compose.device-testing.yml up -d
            ;;
        "production")
            $DOCKER_COMPOSE_CMD up -d
            ;;
        *)
            print_error "Invalid mode: $mode"
            exit 1
            ;;
    esac
    
    print_success "Services started in $mode mode"
}

# Function to show service status
show_status() {
    print_status "Service Status:"
    $DOCKER_COMPOSE_CMD ps
    
    echo ""
    print_status "Available URLs:"
    echo "  SaaS Platform: http://localhost:8080"
    echo "  Android App: http://localhost:8083"
    echo "  iOS App: http://localhost:8088"
    echo "  Device Agent: http://localhost:8084 (runs on host)"
    echo "  Device Dashboard: http://localhost:8090"
    echo "  pgAdmin: http://localhost:5050 (dev mode only)"
    echo "  Redis Commander: http://localhost:8081 (dev mode only)"
}

# Function to stop services
stop_services() {
    print_status "Stopping all services..."
    $DOCKER_COMPOSE_CMD down
    
    # Also stop device agent if running
    stop_device_agent
    
    print_success "All services stopped"
}

# Function to clean up
cleanup() {
    print_status "Cleaning up Docker resources..."
    $DOCKER_COMPOSE_CMD down -v --remove-orphans
    docker system prune -f
    print_success "Cleanup complete"
}

# Function to show logs
show_logs() {
    local service=$1
    if [ -z "$service" ]; then
        $DOCKER_COMPOSE_CMD logs -f
    else
        $DOCKER_COMPOSE_CMD logs -f "$service"
    fi
}

# Main function
main() {
    local command=$1
    local mode=${2:-"development"}
    
    case $command in
        "setup")
            check_docker
            check_docker_compose
            setup_environment
            setup_directories
            check_device_permissions
            build_images
            print_success "Setup complete! Run './docker-setup.sh start [mode]' to start services."
            ;;
        "start")
            check_docker
            check_docker_compose
            start_services "$mode"
            sleep 5
            show_status
            ;;
        "stop")
            stop_services
            ;;
        "restart")
            stop_services
            start_services "$mode"
            show_status
            ;;
        "status")
            show_status
            ;;
        "logs")
            show_logs "$mode"
            ;;
        "cleanup")
            cleanup
            ;;
        "agent")
            case "$mode" in
                "start")
                    start_device_agent
                    ;;
                "stop")
                    stop_device_agent
                    ;;
                "status")
                    if pgrep -f "local_device_agent.py" > /dev/null; then
                        print_success "Device agent is running"
                        curl -s http://localhost:8084/health || print_warning "Device agent not responding"
                    else
                        print_warning "Device agent is not running"
                    fi
                    ;;
                *)
                    print_error "Usage: $0 agent {start|stop|status}"
                    exit 1
                    ;;
            esac
            ;;
        "build")
            build_images
            ;;
        *)
            echo "Usage: $0 {setup|start|stop|restart|status|logs|cleanup|build} [mode|service]"
            echo ""
            echo "Commands:"
            echo "  setup          - Initial setup (run this first)"
            echo "  start [mode]   - Start services (development|device-testing|production)"
            echo "  stop           - Stop all services"
            echo "  restart [mode] - Restart services"
            echo "  status         - Show service status and URLs"
            echo "  logs [service] - Show logs (all services or specific service)"
            echo "  cleanup        - Stop services and clean up resources"
            echo "  build          - Build Docker images"
            echo "  agent [action] - Manage device agent (start|stop|status)"
            echo ""
            echo "Modes:"
            echo "  development    - Full development environment with debugging tools"
            echo "  device-testing - Production-like with device agent for physical testing"
            echo "  production     - Production environment"
            echo ""
            echo "Device Agent Commands:"
            echo "  agent start    - Start device agent on host machine"
            echo "  agent stop     - Stop device agent"
            echo "  agent status   - Check device agent status"
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@"