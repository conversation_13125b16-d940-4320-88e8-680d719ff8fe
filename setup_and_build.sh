#!/bin/bash

# Mobile App Automation - Setup and Build Script
# This script creates a virtual environment, installs dependencies, and builds the app

set -e  # Exit on any error

echo "🚀 Mobile App Automation - Setup and Build"
echo "=========================================="

# Check if Python 3 is available
if ! command -v python3 &> /dev/null; then
    echo "❌ Python 3 is not installed. Please install Python 3.8 or higher."
    exit 1
fi

echo "✅ Python 3 found: $(python3 --version)"

# Create virtual environment if it doesn't exist
if [ ! -d "venv" ]; then
    echo "📦 Creating virtual environment..."
    python3 -m venv venv
    echo "✅ Virtual environment created"
else
    echo "✅ Virtual environment already exists"
fi

# Activate virtual environment
echo "🔄 Activating virtual environment..."
source venv/bin/activate

# Upgrade pip
echo "⬆️ Upgrading pip..."
pip install --upgrade pip

# Install dependencies
echo "📦 Installing dependencies..."
cd secure_distribution_app
pip install -r requirements.txt

# Build the application
echo "🔨 Building application..."
python build_secure_executable.py

echo ""
echo "✅ Build completed successfully!"
echo ""
echo "📁 Output location: secure_distribution_app/dist/"
echo ""
echo "📋 Next steps:"
echo "1. Test the executable in dist/ folder"
echo "2. Upload unified package to Google Drive"
echo "3. Update encrypted link in hidden_link_downloader.py"
echo "4. Distribute to users"
echo ""
echo "💡 To run the app in development mode:"
echo "   source venv/bin/activate"
echo "   cd secure_distribution_app"
echo "   python main.py"
