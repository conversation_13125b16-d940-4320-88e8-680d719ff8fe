#!/usr/bin/env python3
"""
Device Testing Dashboard
Provides a unified interface for monitoring device connectivity and testing
"""

import os
import json
import logging
from flask import Flask, render_template, jsonify, request
from flask_socketio import Socket<PERSON>, emit
import requests
from datetime import datetime

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

app = Flask(__name__)
app.config['SECRET_KEY'] = os.environ.get('SECRET_KEY', 'dashboard-secret-key')
socketio = SocketIO(app, cors_allowed_origins="*")

# Configuration
DEVICE_AGENT_URL = os.environ.get('DEVICE_AGENT_URL', 'http://host.docker.internal:8084')
ANDROID_APP_URL = os.environ.get('ANDROID_APP_URL', 'http://android_app:8083')
IOS_APP_URL = os.environ.get('IOS_APP_URL', 'http://ios_app:8088')
SAAS_PLATFORM_URL = os.environ.get('SAAS_PLATFORM_URL', 'http://saas_platform:8080')

@app.route('/')
def dashboard():
    """Main dashboard page"""
    return render_template('dashboard.html')

@app.route('/api/health')
def health_check():
    """Health check endpoint"""
    return jsonify({
        'status': 'healthy',
        'timestamp': datetime.now().isoformat(),
        'service': 'device_dashboard'
    })

@app.route('/api/devices')
def get_devices():
    """Get connected devices from device agent"""
    try:
        response = requests.get(f'{DEVICE_AGENT_URL}/api/devices', timeout=5)
        if response.status_code == 200:
            return jsonify(response.json())
        else:
            return jsonify({'error': 'Failed to fetch devices', 'devices': []}), 500
    except Exception as e:
        logger.error(f"Error fetching devices: {e}")
        return jsonify({'error': str(e), 'devices': []}), 500

@app.route('/api/services/status')
def get_services_status():
    """Get status of all services"""
    services = {
        'device_agent': DEVICE_AGENT_URL,
        'android_app': ANDROID_APP_URL,
        'ios_app': IOS_APP_URL,
        'saas_platform': SAAS_PLATFORM_URL
    }
    
    status = {}
    for service_name, service_url in services.items():
        try:
            response = requests.get(f'{service_url}/api/health', timeout=3)
            status[service_name] = {
                'status': 'healthy' if response.status_code == 200 else 'unhealthy',
                'url': service_url,
                'response_time': response.elapsed.total_seconds()
            }
        except Exception as e:
            status[service_name] = {
                'status': 'unreachable',
                'url': service_url,
                'error': str(e)
            }
    
    return jsonify(status)

@socketio.on('connect')
def handle_connect():
    """Handle client connection"""
    logger.info('Client connected to dashboard')
    emit('status', {'message': 'Connected to Device Testing Dashboard'})

@socketio.on('disconnect')
def handle_disconnect():
    """Handle client disconnection"""
    logger.info('Client disconnected from dashboard')

if __name__ == '__main__':
    # Use port 8080 for secure builds, fallback to 8090 for development
    default_port = 8080 if os.environ.get('SECURE_BUILD', 'false').lower() == 'true' else 8090
    port = int(os.environ.get('DASHBOARD_PORT', default_port))
    logger.info(f"Starting Device Testing Dashboard on port {port}")
    socketio.run(app, host='0.0.0.0', port=port, debug=False)